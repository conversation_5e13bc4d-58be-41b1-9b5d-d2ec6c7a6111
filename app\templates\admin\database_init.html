{% extends "base.html" %}

{% block title %}数据库初始化工具 - AEC-FT ICP{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}">
<style>
.status-card {
    border-left: 4px solid #6c757d;
    transition: all 0.3s ease;
}
.status-card.ready {
    border-left-color: #28a745;
    background-color: #f8fff9;
}
.status-card.not-ready {
    border-left-color: #dc3545;
    background-color: #fff8f8;
}
.check-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}
.check-item:last-child {
    border-bottom: none;
}
.btn-init {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    color: white;
    transition: all 0.3s ease;
}
.btn-init:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-database me-2"></i>数据库初始化工具</h2>
                <button class="btn btn-outline-secondary" onclick="checkStatus()">
                    <i class="fas fa-sync-alt me-1"></i>刷新状态
                </button>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- 状态检查卡片 -->
        <div class="col-md-6">
            <div class="card status-card" id="statusCard">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>统一批次管理功能状态
                    </h5>
                </div>
                <div class="card-body">
                    <div id="statusContent">
                        <div class="text-center py-3">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                            <p class="mt-2 text-muted">正在检查状态...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 初始化操作卡片 -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>初始化操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>统一批次管理功能</strong>需要以下数据库结构：
                        <ul class="mt-2 mb-0">
                            <li>et_wait_lot_extension 扩展表</li>
                            <li>v_unified_lot_management 统一视图</li>
                            <li>lotprioritydone 表结构扩展</li>
                        </ul>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-init btn-lg" onclick="initializeDatabase()" id="initBtn">
                            <i class="fas fa-rocket me-2"></i>初始化统一批次管理
                        </button>
                        
                        <small class="text-muted text-center">
                            <i class="fas fa-shield-alt me-1"></i>
                            此操作是安全的，不会影响现有数据
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 操作日志 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list-alt me-2"></i>操作日志
                    </h5>
                </div>
                <div class="card-body">
                    <div id="logContent" style="max-height: 300px; overflow-y: auto;">
                        <p class="text-muted">暂无操作记录</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时检查状态
document.addEventListener('DOMContentLoaded', function() {
    checkStatus();
});

// 检查统一批次管理功能状态
async function checkStatus() {
    try {
        const response = await fetch('/admin/tools/check-unified-management-status');
        const data = await response.json();
        
        updateStatusDisplay(data);
        
    } catch (error) {
        console.error('检查状态失败:', error);
        showError('状态检查失败: ' + error.message);
    }
}

// 更新状态显示
function updateStatusDisplay(data) {
    const statusCard = document.getElementById('statusCard');
    const statusContent = document.getElementById('statusContent');
    const initBtn = document.getElementById('initBtn');
    
    if (data.success) {
        // 更新卡片样式
        statusCard.className = data.ready ? 'card status-card ready' : 'card status-card not-ready';
        
        // 更新状态内容
        const checks = data.checks || {};
        statusContent.innerHTML = `
            <div class="mb-3">
                <h6 class="${data.ready ? 'text-success' : 'text-warning'}">
                    <i class="fas fa-${data.ready ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${data.message}
                </h6>
            </div>
            
            <div class="check-items">
                <div class="check-item">
                    <i class="fas fa-${checks.et_wait_lot_extension ? 'check text-success' : 'times text-danger'} me-2"></i>
                    扩展表 (et_wait_lot_extension)
                </div>
                <div class="check-item">
                    <i class="fas fa-${checks.v_unified_lot_management ? 'check text-success' : 'times text-danger'} me-2"></i>
                    统一视图 (v_unified_lot_management)
                </div>
                <div class="check-item">
                    <i class="fas fa-${checks.lotprioritydone_extended ? 'check text-success' : 'times text-danger'} me-2"></i>
                    表结构扩展 (lotprioritydone)
                </div>
            </div>
        `;
        
        // 更新按钮状态
        if (data.ready) {
            initBtn.innerHTML = '<i class="fas fa-check me-2"></i>功能已就绪';
            initBtn.className = 'btn btn-success btn-lg';
            initBtn.disabled = true;
        } else {
            initBtn.innerHTML = '<i class="fas fa-rocket me-2"></i>初始化统一批次管理';
            initBtn.className = 'btn btn-init btn-lg';
            initBtn.disabled = false;
        }
    } else {
        statusContent.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${data.message}
            </div>
        `;
    }
}

// 初始化数据库
async function initializeDatabase() {
    const initBtn = document.getElementById('initBtn');
    const originalText = initBtn.innerHTML;
    
    try {
        // 更新按钮状态
        initBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在初始化...';
        initBtn.disabled = true;
        
        addLog('开始初始化统一批次管理数据库结构...', 'info');
        
        const response = await fetch('/admin/tools/init-unified-management', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            addLog('✅ 初始化成功！', 'success');
            if (data.details) {
                data.details.forEach(detail => addLog(detail, 'success'));
            }
            
            // 刷新状态
            setTimeout(() => {
                checkStatus();
            }, 1000);
            
        } else {
            addLog('❌ 初始化失败: ' + data.message, 'error');
            if (data.details) {
                data.details.forEach(detail => addLog(detail, 'error'));
            }
        }
        
    } catch (error) {
        console.error('初始化失败:', error);
        addLog('❌ 初始化过程中发生错误: ' + error.message, 'error');
    } finally {
        // 恢复按钮状态
        initBtn.innerHTML = originalText;
        initBtn.disabled = false;
    }
}

// 添加日志
function addLog(message, type = 'info') {
    const logContent = document.getElementById('logContent');
    const timestamp = new Date().toLocaleTimeString();
    
    const logEntry = document.createElement('div');
    logEntry.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} py-2 mb-2`;
    logEntry.innerHTML = `
        <small class="text-muted">[${timestamp}]</small> ${message}
    `;
    
    // 如果是第一条日志，清空默认提示
    if (logContent.children.length === 1 && logContent.children[0].textContent.includes('暂无操作记录')) {
        logContent.innerHTML = '';
    }
    
    logContent.appendChild(logEntry);
    logContent.scrollTop = logContent.scrollHeight;
}

// 显示错误
function showError(message) {
    addLog(message, 'error');
}
</script>
{% endblock %}
