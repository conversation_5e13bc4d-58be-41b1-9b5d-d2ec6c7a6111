from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from app import db
from sqlalchemy import text
from datetime import datetime
import logging
import json

manual_adjustment_api = Blueprint('manual_adjustment_api', __name__)
logger = logging.getLogger(__name__)

def _check_schema_compatibility():
    """检查数据库schema是否支持统一数据模型"""
    try:
        check_query = text("""
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'lotprioritydone' 
            AND column_name = 'SCHEDULING_STATUS'
        """)
        result = db.session.execute(check_query)
        return result.scalar() > 0
    except Exception as e:
        logger.warning(f"Schema检查失败: {e}")
        return False

@manual_adjustment_api.route('/api/v2/production/manual-adjustment/batch-update', methods=['POST'])
@login_required
def batch_update_priorities():
    """🔥 增强版：批量更新批次优先级和分选机分配（支持失败批次救援）"""
    try:
        # 检查用户会话状态
        if not current_user.is_authenticated:
            logger.warning("⚠️ 用户会话已过期，需要重新登录")
            return jsonify({
                'success': False,
                'message': '用户会话已过期，请刷新页面重新登录',
                'error_type': 'session_expired',
                'redirect_to_login': True
            }), 401
        
        data = request.get_json()
        updates = data.get('updates', [])
        operation_type = data.get('operation_type', 'drag_adjustment')
        session_id = data.get('session_id', None)
        
        if not updates:
            return jsonify({
                'success': False,
                'message': '没有提供更新数据'
            }), 400
        
        logger.info(f"🔄 开始增强版批量更新 {len(updates)} 条记录，操作类型: {operation_type}")
        
        # 检查schema兼容性
        is_unified_schema = _check_schema_compatibility()
        
        # 批量大小限制
        max_batch_size = 500  # 🔥 增加批量限制到500
        if len(updates) > max_batch_size:
            return jsonify({
                'success': False,
                'message': f'批量更新记录数量超限，最多支持 {max_batch_size} 条记录，当前: {len(updates)} 条',
                'max_batch_size': max_batch_size,
                'current_size': len(updates),
                'error_type': 'batch_size_limit'
            }), 400
        
        # 🔥 增强版数据验证
        validation_result = enhanced_batch_validation(updates, is_unified_schema)
        if not validation_result['valid']:
            return jsonify({
                'success': False,
                'message': f'数据验证失败: {validation_result["message"]}',
                'errors': validation_result['errors'],
                'warnings': validation_result.get('warnings', []),
                'error_type': 'data_validation'
            }), 400
        
        # 执行批量更新
        updated_count = 0
        failed_updates = []
        rescued_count = 0  # 🔥 新增：救援的失败批次计数
        
        for update in updates:
            try:
                lot_id = update['lot_id']
                new_priority = update.get('priority')
                new_handler_id = update.get('handler_id')
                old_handler_id = update.get('old_handler_id')
                adjustment_reason = update.get('adjustment_reason', '手动调整')
                
                # 🔥 查找对应的批次记录（支持统一模型）
                if is_unified_schema:
                    check_query = text("""
                        SELECT id, PRIORITY, HANDLER_ID, SCHEDULING_STATUS, FAILURE_REASON 
                        FROM lotprioritydone 
                        WHERE LOT_ID = :lot_id
                    """)
                else:
                    check_query = text("""
                        SELECT id, PRIORITY, HANDLER_ID 
                        FROM lotprioritydone 
                        WHERE LOT_ID = :lot_id
                    """)
                
                result = db.session.execute(check_query, {'lot_id': lot_id})
                record = result.fetchone()
                
                if record:
                    current_priority = record[1]
                    current_handler_id = record[2]
                    current_status = record[3] if is_unified_schema else 'SUCCESS'
                    
                    # 构建更新SQL
                    update_fields = []
                    update_params = {'lot_id': lot_id}
                    
                    # 更新优先级
                    if new_priority is not None:
                        update_fields.append("PRIORITY = :priority")
                        update_params['priority'] = new_priority
                    
                    # 更新分选机分配
                    if new_handler_id:
                        update_fields.append("HANDLER_ID = :handler_id")
                        update_params['handler_id'] = new_handler_id
                    
                    # 🔥 失败批次救援逻辑
                    if is_unified_schema and current_status == 'FAILED' and new_handler_id and new_handler_id != 'UNASSIGNED':
                        update_fields.append("SCHEDULING_STATUS = 'MANUAL_ADJUSTED'")
                        update_fields.append("WIP_STATE = 'SCHEDULED'")
                        update_fields.append("PROC_STATE = 'READY'")
                        
                        # 更新失败原因，添加救援记录
                        current_failure_reason = record[4] if len(record) > 4 else ''
                        new_failure_reason = f"{current_failure_reason} [手动救援: {adjustment_reason} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}]"
                        update_fields.append("FAILURE_REASON = :failure_reason")
                        update_params['failure_reason'] = new_failure_reason
                        
                        rescued_count += 1
                        logger.info(f"🚑 救援失败批次 {lot_id}: {current_handler_id} -> {new_handler_id}")
                    
                    # 🔥 状态变更逻辑（成功批次的手动调整）
                    elif is_unified_schema and current_status == 'SUCCESS' and (new_priority != current_priority or new_handler_id != current_handler_id):
                        update_fields.append("SCHEDULING_STATUS = 'MANUAL_ADJUSTED'")
                        logger.info(f"📝 标记手动调整 {lot_id}: 状态从 SUCCESS 变更为 MANUAL_ADJUSTED")
                    
                    if update_fields:
                        # 执行更新
                        update_sql = f"""
                            UPDATE lotprioritydone 
                            SET {', '.join(update_fields)}
                            WHERE LOT_ID = :lot_id
                        """
                        
                        result = db.session.execute(text(update_sql), update_params)
                        affected_rows = result.rowcount
                        
                        if affected_rows > 0:
                            updated_count += 1
                            logger.info(f"✅ 更新批次 {lot_id}: priority={new_priority}, handler={new_handler_id}")
                        else:
                            logger.warning(f"⚠️ 更新批次 {lot_id} 影响行数为0")
                    
                else:
                    failed_updates.append({
                        'lot_id': lot_id,
                        'reason': '批次记录不存在'
                    })
                    logger.warning(f"⚠️ 批次 {lot_id} 不存在")
                    
            except Exception as e:
                failed_updates.append({
                    'lot_id': update.get('lot_id', 'unknown'),
                    'reason': str(e)
                })
                logger.error(f"❌ 更新批次失败: {e}")
        
        # 提交事务
        db.session.commit()
        
        # 记录操作历史
        record_adjustment_history(updates, current_user.username, operation_type, session_id)
        
        # 🔥 增强版响应信息
        response_data = {
            'success': True,
            'message': f'成功更新 {updated_count} 条记录',
            'updated_count': updated_count,
            'total_count': len(updates),
            'rescued_count': rescued_count,  # 新增：救援计数
            'schema_info': {
                'is_unified_schema': is_unified_schema,
                'supports_failed_rescue': is_unified_schema
            }
        }
        
        if failed_updates:
            response_data['failed_updates'] = failed_updates
            response_data['message'] += f'，{len(failed_updates)} 条失败'
        
        if rescued_count > 0:
            response_data['message'] += f'，救援 {rescued_count} 个失败批次'
        
        logger.info(f"✅ 增强版批量更新完成: {updated_count}/{len(updates)} 成功, {rescued_count} 个失败批次被救援")
        
        return jsonify(response_data)
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 批量更新失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500

# 🔥 新增：失败批次专用救援API
@manual_adjustment_api.route('/api/v2/production/manual-adjustment/rescue-failed', methods=['POST'])
@login_required
def rescue_failed_lots():
    """专用于失败批次救援的API"""
    try:
        data = request.get_json()
        rescue_operations = data.get('rescue_operations', [])
        
        if not rescue_operations:
            return jsonify({
                'success': False,
                'message': '没有提供救援操作数据'
            }), 400
        
        # 检查schema兼容性
        is_unified_schema = _check_schema_compatibility()
        if not is_unified_schema:
            return jsonify({
                'success': False,
                'message': '当前数据库架构不支持失败批次救援功能，请先运行数据库迁移'
            }), 400
        
        rescued_count = 0
        failed_rescues = []
        
        for operation in rescue_operations:
            try:
                lot_id = operation['lot_id']
                target_handler_id = operation['target_handler_id']
                target_priority = operation.get('target_priority', 999)
                rescue_reason = operation.get('rescue_reason', '手动救援失败批次')
                
                # 验证是否为失败批次
                check_query = text("""
                    SELECT SCHEDULING_STATUS, FAILURE_REASON, HANDLER_ID 
                    FROM lotprioritydone 
                    WHERE LOT_ID = :lot_id AND SCHEDULING_STATUS = 'FAILED'
                """)
                
                result = db.session.execute(check_query, {'lot_id': lot_id})
                record = result.fetchone()
                
                if not record:
                    failed_rescues.append({
                        'lot_id': lot_id,
                        'reason': '批次不存在或非失败状态'
                    })
                    continue
                
                current_failure_reason = record[1] or ''
                
                # 执行救援操作
                rescue_query = text("""
                    UPDATE lotprioritydone 
                    SET 
                        SCHEDULING_STATUS = 'MANUAL_ADJUSTED',
                        HANDLER_ID = :target_handler_id,
                        PRIORITY = :target_priority,
                        WIP_STATE = 'SCHEDULED',
                        PROC_STATE = 'READY',
                        FAILURE_REASON = :updated_failure_reason
                    WHERE LOT_ID = :lot_id AND SCHEDULING_STATUS = 'FAILED'
                """)
                
                updated_failure_reason = f"{current_failure_reason} [救援成功: {rescue_reason} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 操作员: {current_user.username}]"
                
                result = db.session.execute(rescue_query, {
                    'lot_id': lot_id,
                    'target_handler_id': target_handler_id,
                    'target_priority': target_priority,
                    'updated_failure_reason': updated_failure_reason
                })
                
                if result.rowcount > 0:
                    rescued_count += 1
                    logger.info(f"🚑 成功救援失败批次 {lot_id} -> {target_handler_id}")
                else:
                    failed_rescues.append({
                        'lot_id': lot_id,
                        'reason': '救援操作未影响任何记录'
                    })
                
            except Exception as e:
                failed_rescues.append({
                    'lot_id': operation.get('lot_id', 'unknown'),
                    'reason': str(e)
                })
                logger.error(f"❌ 救援批次失败: {e}")
        
        db.session.commit()
        
        response_data = {
            'success': True,
            'message': f'成功救援 {rescued_count} 个失败批次',
            'rescued_count': rescued_count,
            'total_operations': len(rescue_operations)
        }
        
        if failed_rescues:
            response_data['failed_rescues'] = failed_rescues
            response_data['message'] += f'，{len(failed_rescues)} 个救援失败'
        
        logger.info(f"🚑 失败批次救援完成: {rescued_count}/{len(rescue_operations)} 成功")
        
        return jsonify(response_data)
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 失败批次救援失败: {e}")
        return jsonify({
            'success': False,
            'message': f'救援失败: {str(e)}'
        }), 500

# 🔥 新增：批量状态变更API
@manual_adjustment_api.route('/api/v2/production/manual-adjustment/batch-status-change', methods=['POST'])
@login_required
def batch_status_change():
    """批量状态变更（SUCCESS <-> MANUAL_ADJUSTED）"""
    try:
        data = request.get_json()
        lot_ids = data.get('lot_ids', [])
        target_status = data.get('target_status', 'MANUAL_ADJUSTED')
        change_reason = data.get('change_reason', '批量状态变更')
        
        if not lot_ids:
            return jsonify({
                'success': False,
                'message': '没有提供批次ID列表'
            }), 400
        
        # 检查schema兼容性
        is_unified_schema = _check_schema_compatibility()
        if not is_unified_schema:
            return jsonify({
                'success': False,
                'message': '当前数据库架构不支持状态变更功能'
            }), 400
        
        # 验证目标状态
        valid_statuses = ['SUCCESS', 'MANUAL_ADJUSTED']
        if target_status not in valid_statuses:
            return jsonify({
                'success': False,
                'message': f'无效的目标状态: {target_status}，支持的状态: {valid_statuses}'
            }), 400
        
        changed_count = 0
        failed_changes = []
        
        for lot_id in lot_ids:
            try:
                # 检查当前状态
                check_query = text("""
                    SELECT SCHEDULING_STATUS 
                    FROM lotprioritydone 
                    WHERE LOT_ID = :lot_id AND SCHEDULING_STATUS IN ('SUCCESS', 'MANUAL_ADJUSTED')
                """)
                
                result = db.session.execute(check_query, {'lot_id': lot_id})
                record = result.fetchone()
                
                if not record:
                    failed_changes.append({
                        'lot_id': lot_id,
                        'reason': '批次不存在或状态不支持变更'
                    })
                    continue
                
                current_status = record[0]
                if current_status == target_status:
                    continue  # 状态相同，跳过
                
                # 执行状态变更
                update_query = text("""
                    UPDATE lotprioritydone 
                    SET SCHEDULING_STATUS = :target_status
                    WHERE LOT_ID = :lot_id
                """)
                
                result = db.session.execute(update_query, {
                    'lot_id': lot_id,
                    'target_status': target_status
                })
                
                if result.rowcount > 0:
                    changed_count += 1
                    logger.info(f"🔄 状态变更 {lot_id}: {current_status} -> {target_status}")
                
            except Exception as e:
                failed_changes.append({
                    'lot_id': lot_id,
                    'reason': str(e)
                })
                logger.error(f"❌ 状态变更失败 {lot_id}: {e}")
        
        db.session.commit()
        
        response_data = {
            'success': True,
            'message': f'成功变更 {changed_count} 个批次状态为 {target_status}',
            'changed_count': changed_count,
            'total_count': len(lot_ids),
            'target_status': target_status
        }
        
        if failed_changes:
            response_data['failed_changes'] = failed_changes
            response_data['message'] += f'，{len(failed_changes)} 个变更失败'
        
        logger.info(f"🔄 批量状态变更完成: {changed_count}/{len(lot_ids)} 成功")
        
        return jsonify(response_data)
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 批量状态变更失败: {e}")
        return jsonify({
            'success': False,
            'message': f'状态变更失败: {str(e)}'
        }), 500

@manual_adjustment_api.route('/api/v2/production/manual-adjustment/validate', methods=['POST'])
@login_required
def validate_adjustments():
    """🔥 增强版：验证调整操作的合理性"""
    try:
        data = request.get_json()
        updates = data.get('updates', [])
        
        if not updates:
            return jsonify({
                'success': False,
                'message': '没有提供验证数据'
            }), 400
        
        # 检查schema兼容性
        is_unified_schema = _check_schema_compatibility()
        
        # 执行增强版验证
        validation_result = comprehensive_validation(updates, is_unified_schema)
        
        return jsonify({
            'success': True,
            'validation_result': validation_result,
            'schema_info': {
                'is_unified_schema': is_unified_schema,
                'supports_failed_rescue': is_unified_schema
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 验证失败: {e}")
        return jsonify({
            'success': False,
            'message': f'验证失败: {str(e)}'
        }), 500

@manual_adjustment_api.route('/api/v2/production/manual-adjustment/handler-capacity', methods=['POST'])
@login_required
def check_handler_capacity():
    """检查分选机负载能力"""
    try:
        data = request.get_json()
        updates = data.get('updates', [])
        
        capacity_check = validate_handler_capacity(updates)
        
        return jsonify({
            'success': True,
            'capacity_check': capacity_check
        })
        
    except Exception as e:
        logger.error(f"❌ 负载检查失败: {e}")
        return jsonify({
            'success': False,
            'message': f'负载检查失败: {str(e)}'
        }), 500

def validate_batch_updates(updates):
    """验证批量更新数据的合理性"""
    errors = []
    warnings = []
    
    # 检查必需字段
    for update in updates:
        if not update.get('lot_id'):
            errors.append('缺少批次号 (lot_id)')
            continue
            
        # 验证批次是否存在于 lotprioritydone 表中
        try:
            check_query = text("SELECT LOT_ID FROM lotprioritydone WHERE LOT_ID = :lot_id")
            result = db.session.execute(check_query, {'lot_id': update['lot_id']})
            if not result.fetchone():
                errors.append(f'批次 {update["lot_id"]} 在排产表中不存在')
        except Exception as e:
            errors.append(f'验证批次 {update.get("lot_id", "unknown")} 时出错: {str(e)}')
    
    # 检查优先级重复和连续性
    priority_groups = {}
    for update in updates:
        handler_id = update.get('handler_id')
        priority = update.get('priority')
        
        if priority is not None and handler_id:
            if handler_id not in priority_groups:
                priority_groups[handler_id] = []
            priority_groups[handler_id].append(priority)
    
    # 检查每个分选机内的优先级是否有重复
    for handler_id, priorities in priority_groups.items():
        if len(priorities) != len(set(priorities)):
            errors.append(f'分选机 {handler_id} 存在重复的优先级')
    
    # 检查优先级范围
    for update in updates:
        priority = update.get('priority')
        if priority is not None:
            if priority < 1 or priority > 999:
                errors.append(f'批次 {update.get("lot_id")} 的优先级 {priority} 超出范围 (1-999)')
    
    # 检查批次ID有效性
    for update in updates:
        lot_id = update.get('lot_id')
        if not lot_id or lot_id.strip() == '':
            errors.append('存在空的批次ID')
    
    return {
        'valid': len(errors) == 0,
        'message': '; '.join(errors) if errors else '验证通过',
        'errors': errors,
        'warnings': warnings
    }

def enhanced_batch_validation(updates, is_unified_schema):
    """增强版批量数据验证"""
    errors = []
    warnings = []
    
    # 1. 基础数据验证 (兼容旧模式)
    basic_validation = validate_batch_updates(updates)
    if not basic_validation['valid']:
        errors.extend(basic_validation['errors'])
        warnings.extend(basic_validation['warnings'])
    
    # 2. 负载平衡检查 (兼容旧模式)
    capacity_validation = validate_handler_capacity(updates)
    if capacity_validation.get('warnings'):
        warnings.extend(capacity_validation['warnings'])
    
    # 3. 技术匹配度检查 (兼容旧模式)
    tech_validation = validate_technical_match(updates)
    if tech_validation.get('warnings'):
        warnings.extend(tech_validation['warnings'])
    
    # 4. 统一模式下的特定验证 (新增)
    if is_unified_schema:
        for update in updates:
            lot_id = update.get('lot_id')
            handler_id = update.get('handler_id')
            status = update.get('SCHEDULING_STATUS') # 统一模式下使用 SCHEDULING_STATUS
            
            if status == 'FAILED' and handler_id and handler_id != 'UNASSIGNED':
                errors.append(f'批次 {lot_id} 为失败状态，但尝试分配分选机 {handler_id}，请先救援')
            elif status == 'SUCCESS' and handler_id and handler_id != 'UNASSIGNED':
                warnings.append(f'批次 {lot_id} 为成功状态，但尝试分配分选机 {handler_id}，可能需要手动调整')
    
    return {
        'valid': len(errors) == 0,
        'message': '; '.join(errors) if errors else '验证通过',
        'errors': errors,
        'warnings': warnings
    }

def comprehensive_validation(updates, is_unified_schema):
    """综合验证调整操作"""
    validation_results = []
    
    # 1. 基础数据验证 (兼容旧模式)
    basic_validation = validate_batch_updates(updates)
    validation_results.append({
        'type': 'basic',
        'level': 'error' if not basic_validation['valid'] else 'success',
        'message': basic_validation['message'],
        'details': basic_validation
    })
    
    # 2. 负载平衡检查 (兼容旧模式)
    capacity_validation = validate_handler_capacity(updates)
    validation_results.append({
        'type': 'capacity',
        'level': 'warning' if capacity_validation.get('warnings') else 'success',
        'message': '负载检查完成',
        'details': capacity_validation
    })
    
    # 3. 技术匹配度检查 (兼容旧模式)
    tech_validation = validate_technical_match(updates)
    validation_results.append({
        'type': 'technical',
        'level': 'info',
        'message': '技术匹配检查完成',
        'details': tech_validation
    })
    
    # 4. 统一模式下的特定验证 (新增)
    if is_unified_schema:
        for update in updates:
            lot_id = update.get('lot_id')
            handler_id = update.get('handler_id')
            status = update.get('SCHEDULING_STATUS') # 统一模式下使用 SCHEDULING_STATUS
            
            if status == 'FAILED' and handler_id and handler_id != 'UNASSIGNED':
                validation_results.append({
                    'type': 'unified_specific',
                    'level': 'error',
                    'message': f'批次 {lot_id} 为失败状态，但尝试分配分选机 {handler_id}，请先救援',
                    'details': {'lot_id': lot_id, 'handler_id': handler_id, 'status': status}
                })
            elif status == 'SUCCESS' and handler_id and handler_id != 'UNASSIGNED':
                validation_results.append({
                    'type': 'unified_specific',
                    'level': 'warning',
                    'message': f'批次 {lot_id} 为成功状态，但尝试分配分选机 {handler_id}，可能需要手动调整',
                    'details': {'lot_id': lot_id, 'handler_id': handler_id, 'status': status}
                })
    
    return validation_results

def validate_handler_capacity(updates):
    """验证分选机负载能力"""
    warnings = []
    handler_loads = {}
    
    # 计算每个分选机的负载
    for update in updates:
        handler_id = update.get('handler_id')
        lot_qty = update.get('quantity', 1000)  # 默认数量
        
        if handler_id and handler_id != 'UNASSIGNED':
            if handler_id not in handler_loads:
                handler_loads[handler_id] = 0
            handler_loads[handler_id] += lot_qty
    
    # 检查负载是否超限（假设每个分选机8小时容量为10000件）
    max_capacity = 10000
    for handler_id, load in handler_loads.items():
        load_percentage = (load / max_capacity) * 100
        
        if load_percentage > 100:
            warnings.append(f'分选机 {handler_id} 负载超限: {load_percentage:.1f}%')
        elif load_percentage > 90:
            warnings.append(f'分选机 {handler_id} 负载较高: {load_percentage:.1f}%')
    
    return {
        'handler_loads': handler_loads,
        'warnings': warnings,
        'max_capacity': max_capacity
    }

def validate_technical_match(updates):
    """验证技术匹配度"""
    warnings = []
    
    # 这里可以添加技术匹配逻辑
    # 例如：检查产品类型与分选机类型的匹配度
    
    for update in updates:
        lot_id = update.get('lot_id')
        handler_id = update.get('handler_id')
        
        # 模拟匹配度检查
        if handler_id and handler_id != 'UNASSIGNED':
            # 这里可以集成实际的匹配算法
            match_score = 85  # 模拟匹配分数
            
            if match_score < 60:
                warnings.append(f'批次 {lot_id} 与分选机 {handler_id} 匹配度较低: {match_score}%')
    
    return {
        'warnings': warnings
    }

def record_adjustment_history(updates, username, operation_type, session_id):
    """记录调整操作历史"""
    try:
        history_record = {
            'timestamp': datetime.utcnow().isoformat(),
            'user': username,
            'operation_type': operation_type,
            'session_id': session_id,
            'updates_count': len(updates),
            'updates': updates[:10] if len(updates) > 10 else updates  # 限制存储的更新记录数量
        }
        
        # 这里可以将历史记录保存到数据库或日志文件
        logger.info(f"📝 记录调整历史: {json.dumps(history_record, ensure_ascii=False)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 记录操作历史失败: {e}")
        return False 