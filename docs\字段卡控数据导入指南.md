# 字段卡控数据导入指南

> **专门针对排产系统的安全数据导入方案**  
> 保护表结构，防止业务逻辑断裂，确保排产算法正常运行

## 🚨 问题背景

原始的`import_excel_to_mysql.py`工具存在严重的表结构安全问题：

1. **破坏表结构**：使用`DROP TABLE IF EXISTS`会删除已有表
2. **丢失数据类型**：所有字段都变成`TEXT`类型  
3. **字段名混乱**：Excel表头与数据库字段名可能不匹配
4. **缺乏验证**：没有字段卡控和映射机制

**结果**：排产逻辑依赖的字段类型和名称发生变化，导致排产算法失效！

## ✅ 解决方案

### 1. 字段卡控安全版本

新版本`import_excel_to_mysql.py`具备以下安全特性：

- 🔒 **严格保护表结构**：只插入数据，绝不修改表结构
- 🎯 **智能字段映射**：Excel字段自动映射到数据库字段
- 🔍 **数据类型转换**：根据表结构自动转换数据类型
- ⚠️ **必填字段验证**：确保关键业务字段完整
- 📊 **详细日志记录**：完整的操作日志和错误追踪

### 2. 表结构验证工具

新增`table_structure_validator.py`提供：

- 🔍 **表结构验证**：检查所有业务表是否存在
- 🎯 **字段完整性检查**：验证必填字段是否齐全
- 📋 **类型一致性验证**：确保字段类型符合业务要求
- 💡 **修复建议生成**：提供具体的修复步骤

## 🗂️ 保护的核心业务表

系统保护以下排产核心表的字段结构：

### 排产调度核心表
- **`ct`** - 产品周期管理表
- **`wip_lot`** - WIP批次管理表  
- **`ET_WAIT_LOT`** - 待排产批次表
- **`ET_UPH_EQP`** - UPH设备效率表

### 设备状态管理表
- **`eqp_status`** - 设备状态表
- **`et_ft_test_spec`** - 测试规格表

### 优先级配置表
- **`devicepriorityconfig`** - 产品优先级配置表
- **`lotpriorityconfig`** - 批次优先级配置表

### 资源管理表
- **`tcc_inv`** - 套件资源表

## 🛠️ 使用流程

### 第一步：验证表结构

在导入数据前，首先验证数据库表结构：

```bash
python table_structure_validator.py
```

**输出示例**：
```
🔍 表结构验证报告
============================================================
📊 验证统计:
   总表数: 10
   已验证: 8
   缺失表: 2
   结构不匹配: 0

❌ 缺失的表:
   • wip_lot - WIP批次管理表 - 排产核心 🔒 保护模式
   • devicepriorityconfig - 产品优先级配置表 - 排产核心 🔒 保护模式

💡 修复建议:
   1. ⚠️ 存在缺失的关键业务表，建议运行 init_db.py 创建表结构
      执行: python init_db.py
      影响表: wip_lot, devicepriorityconfig
```

### 第二步：修复表结构（如需要）

如果发现缺失表或结构问题，执行初始化：

```powershell
python init_db.py
```

### 第三步：安全导入数据

使用字段卡控版本进行安全导入：

```powershell
# 导入指定目录的Excel文件
python import_excel_to_mysql.py D:\data\excel_files\

# 或者在Python代码中调用
from import_excel_to_mysql import import_excel_files_safely
success, result = import_excel_files_safely('D:\\data\\excel_files\\')
```

## 🔍 字段映射规则

### 自动映射机制

系统支持多种字段映射方式：

1. **直接匹配**：Excel字段名与数据库字段名完全一致
2. **配置映射**：通过`BUSINESS_TABLE_SCHEMAS`配置映射关系
3. **智能推断**：基于表名和工作表名的智能映射

### 映射示例

**CT表字段映射**：
```python
'field_mapping': {
    'LOT_ID': 'LOT_ID',         # 直接映射
    'DEVICE': 'DEVICE',         # 直接映射  
    'STAGE': 'STAGE',           # 直接映射
    'START_TIME': 'START_TIME', # 自动转换为DATETIME类型
    'QTY': 'QTY',              # 自动转换为INT类型
    'YIELD_RATE': 'YIELD_RATE'  # 自动转换为DECIMAL类型
}
```

### 数据类型转换

系统会根据数据库字段类型自动转换Excel数据：

- **VARCHAR(n)** ← Excel文本（自动截断到指定长度）
- **INT** ← Excel数字（自动转换为整数）
- **DECIMAL(m,n)** ← Excel数字（保留指定小数位）
- **DATE** ← Excel日期（自动识别日期格式）
- **DATETIME** ← Excel日期时间（自动转换格式）

## ⚠️ 安全约束

### 必填字段验证

每个保护模式的表都有必填字段要求：

```python
# 示例：WIP_LOT表必填字段
'required_fields': ['LOT_ID', 'DEVICE', 'STAGE']
```

如果Excel中缺少必填字段，导入将失败并提示具体缺失字段。

### 表结构保护

对于标记为`protect_mode: True`的表：

- ✅ **只能插入数据**，不能修改表结构
- ✅ **必须预先存在**，不能动态创建
- ✅ **字段类型固定**，不能随意改变
- ✅ **必填字段验证**，确保数据完整性

## 📊 导入结果示例

**成功导入**：
```
✅ 安全导入成功: 安全导入完成，成功处理 3 个文件，失败 0 个
   处理时间: 45.2 秒
   总记录数: 15847
   表结构保护: ✅ 已启用
```

**部分失败**：
```
❌ 安全导入失败: 部分文件处理失败，请检查日志
⚠️  缺少关键业务表: ['wip_lot', 'devicepriorityconfig']
```

## 🚨 常见问题与解决方案

### 问题1：表不存在错误

**错误信息**：
```
保护模式表 wip_lot 不存在！请先运行 init_db.py 创建表结构
```

**解决方案**：
```powershell
python init_db.py
```

### 问题2：必填字段缺失

**错误信息**：
```
Excel缺少必填字段: ['LOT_ID', 'DEVICE']
```

**解决方案**：
1. 检查Excel文件是否包含所有必填字段
2. 确认字段名称是否与配置中的映射一致
3. 考虑添加字段映射配置

### 问题3：字段类型不匹配

**错误信息**：
```
字段 QTY 类型转换失败: invalid literal for int()
```

**解决方案**：
1. 检查Excel中QTY字段是否包含非数字内容
2. 清理Excel数据，确保数值字段只包含数字
3. 使用Excel的数据验证功能

### 问题4：字段映射失败

**错误信息**：
```
Excel字段 Lot_ID 无法映射到数据库，将被跳过
```

**解决方案**：
1. 检查字段名称是否正确（注意大小写）
2. 在`BUSINESS_TABLE_SCHEMAS`中添加映射配置：
```python
'field_mapping': {
    'Lot_ID': 'LOT_ID',  # 添加这行映射
    # ... 其他映射
}
```

## 🔧 自定义配置

### 添加新表的保护

如需为新表添加字段卡控，在`BUSINESS_TABLE_SCHEMAS`中添加配置：

```python
'your_new_table': {
    'required_fields': ['field1', 'field2'],
    'field_mapping': {
        'excel_field1': 'db_field1',
        'excel_field2': 'db_field2'
    },
    'field_types': {
        'db_field1': 'VARCHAR(50)',
        'db_field2': 'INT'
    },
    'description': '您的新表描述',
    'protect_mode': True  # 启用严格保护
}
```

### 修改字段映射

根据实际Excel文件格式，调整字段映射：

```python
# 在对应表的配置中修改field_mapping
'field_mapping': {
    # Excel表头 -> 数据库字段
    '批次号': 'LOT_ID',        # 中文表头映射
    '产品型号': 'DEVICE',      # 中文表头映射
    'Lot ID': 'LOT_ID',       # 空格处理
    'Device Name': 'DEVICE'   # 英文表头映射
}
```

## 📋 最佳实践

### 1. 导入前准备

- [ ] 运行表结构验证工具
- [ ] 确保所有必要的表已创建
- [ ] 备份现有数据（如有）
- [ ] 检查Excel文件格式和字段名

### 2. Excel文件准备

- [ ] 确保表头字段名与数据库字段或映射配置一致
- [ ] 清理空行和无效数据
- [ ] 确保数值字段不包含文本内容
- [ ] 确保日期字段格式正确

### 3. 导入过程监控

- [ ] 观察控制台输出，注意警告信息
- [ ] 检查日志文件`import_mysql_safe.log`
- [ ] 验证导入结果的数据完整性
- [ ] 运行排产算法测试确保功能正常

### 4. 导入后验证

- [ ] 检查导入的记录数是否正确
- [ ] 验证关键字段的数据类型
- [ ] 运行业务逻辑测试
- [ ] 检查排产算法是否正常工作

## 🎯 总结

通过使用字段卡控安全版本的数据导入工具，您可以：

✅ **保护表结构**：确保排产核心表的结构不被破坏  
✅ **保证数据质量**：自动验证和转换数据类型  
✅ **避免业务风险**：防止排产逻辑因字段变化而失效  
✅ **提高导入效率**：智能字段映射减少手工配置  
✅ **便于问题排查**：详细的日志和错误提示  

这套方案专门针对排产系统的特殊需求设计，在保证数据导入灵活性的同时，严格保护了业务逻辑的完整性。 