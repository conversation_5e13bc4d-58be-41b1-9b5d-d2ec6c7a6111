#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的executeManualScheduling测试
"""

import requests
import json

def test_execute_manual_scheduling():
    print("🧪 测试executeManualScheduling...")
    
    # 设置测试参数
    url = "http://127.0.0.1:5000/api/v2/production/execute-manual-scheduling"
    
    # 创建会话并登录
    session = requests.Session()
    
    # 登录
    login_response = session.post("http://127.0.0.1:5000/auth/login", data={
        'username': 'admin',
        'password': 'admin'
    })
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return False
    
    print("✅ 登录成功")
    
    # 调用API
    request_data = {
        'algorithm': 'intelligent',
        'optimization_target': 'balanced',
        'auto_mode': False,
        'time_limit': 30,
        'population_size': 100,
        'mutation_rate': 0.1,
        'crossover_rate': 0.8,
        'elite_size': 20,
        'max_iterations': 100,
        'convergence_threshold': 0.001,
        'memory_limit_mb': 1024,
        'max_parallel_workers': 4,
        'debug_mode': False
    }
    
    print("📡 发送API请求...")
    response = session.post(url, json=request_data)
    
    print(f"📡 API响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ API调用成功")
        try:
            result = response.json()
            print("📊 排产结果分析:")
            print(f"  - 排产成功: {result.get('success', False)}")
            print(f"  - 使用算法: {result.get('algorithm_used', '未知')}")
            print(f"  - 排产结果数量: {len(result.get('schedule', []))}")
            print(f"  - 消息: {result.get('message', '无消息')}")
            
            # 检查前3个排产结果的扩展字段
            schedule = result.get('schedule', [])
            if schedule:
                print(f"\n🔍 前3个排产结果的扩展字段:")
                for i, lot in enumerate(schedule[:3]):
                    print(f"  批次{i+1} ({lot.get('LOT_ID', '未知')}):")
                    print(f"    - comprehensive_score: {lot.get('comprehensive_score', 'N/A')}")
                    print(f"    - match_type: {lot.get('match_type', 'N/A')}")
                    print(f"    - processing_time: {lot.get('processing_time', 'N/A')}")
                    print(f"    - algorithm_version: {lot.get('algorithm_version', 'N/A')}")
                    print(f"    - priority_score: {lot.get('priority_score', 'N/A')}")
                    print(f"    - equipment_status: {lot.get('equipment_status', 'N/A')}")
                    print(f"    - selection_reason: {lot.get('selection_reason', 'N/A')}")
                    print()
            
            return result.get('success', False)
        except Exception as e:
            print(f"❌ 解析响应失败: {e}")
            return False
    else:
        print("❌ API调用失败")
        try:
            error_info = response.json()
            print(f"错误内容: {error_info}")
        except:
            print(f"错误内容: {response.text}")
        return False

if __name__ == "__main__":
    result = test_execute_manual_scheduling()
    print(f"\n🎯 测试结果: {'✅ 成功' if result else '❌ 失败'}") 