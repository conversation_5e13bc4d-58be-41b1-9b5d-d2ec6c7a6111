# 🔒 APS车规芯片终测智能调度平台 - 安全Docker构建文件
# 多阶段构建，编译保护源代码，防止源码泄露
# 作者: Claude AI Assistant

# ============================================
# 阶段1: 构建环境 (源代码仅在此阶段存在)
# ============================================
FROM python:3.9-slim as builder

# 设置构建参数
ARG BUILD_DATE
ARG VERSION=1.0
ARG COMMIT_SHA

# 标签信息
LABEL maintainer="APS Development Team"
LABEL version="${VERSION}"
LABEL build-date="${BUILD_DATE}"
LABEL commit-sha="${COMMIT_SHA}"
LABEL description="APS Secure Build Stage"

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 安装Python编译工具
RUN pip install --no-cache-dir \
    cython==3.0.6 \
    nuitka==1.8.4 \
    wheel

WORKDIR /build

# 🔒 步骤1: 复制优化后的依赖文件
COPY requirements_optimized.txt .

# 🔒 步骤2: 安装依赖包
RUN pip install --no-cache-dir -r requirements_optimized.txt

# 🔒 步骤3: 复制源代码（仅在构建阶段）
COPY app/ ./app/
COPY run.py ./
COPY config/ ./config/

# 🔒 步骤4: 创建编译脚本
RUN echo '#!/usr/bin/env python3' > compile_core.py && \
    echo 'import py_compile' >> compile_core.py && \
    echo 'import os' >> compile_core.py && \
    echo 'import sys' >> compile_core.py && \
    echo 'print("🔒 开始编译核心算法模块...")' >> compile_core.py && \
    echo 'core_files = [' >> compile_core.py && \
    echo '    "app/services/real_scheduling_service.py",' >> compile_core.py && \
    echo '    "app/services/algorithm_selector.py",' >> compile_core.py && \
    echo '    "app/services/multilevel_cache_manager.py",' >> compile_core.py && \
    echo '    "app/services/parallel_scheduling_engine.py",' >> compile_core.py && \
    echo '    "app/services/intelligent_scheduling_service.py",' >> compile_core.py && \
    echo '    "app/services/intelligent_cache_adapter.py",' >> compile_core.py && \
    echo '    "app/services/enhanced_data_source_manager.py"' >> compile_core.py && \
    echo ']' >> compile_core.py && \
    echo 'compiled_count = 0' >> compile_core.py && \
    echo 'for file_path in core_files:' >> compile_core.py && \
    echo '    if os.path.exists(file_path):' >> compile_core.py && \
    echo '        try:' >> compile_core.py && \
    echo '            py_compile.compile(file_path, file_path + "c", doraise=True)' >> compile_core.py && \
    echo '            os.remove(file_path)' >> compile_core.py && \
    echo '            print(f"✅ 已编译并删除: {file_path}")' >> compile_core.py && \
    echo '            compiled_count += 1' >> compile_core.py && \
    echo '        except Exception as e:' >> compile_core.py && \
    echo '            print(f"❌ 编译失败: {file_path} - {e}")' >> compile_core.py && \
    echo '            sys.exit(1)' >> compile_core.py && \
    echo '    else:' >> compile_core.py && \
    echo '        print(f"⚠️ 文件不存在: {file_path}")' >> compile_core.py && \
    echo 'print(f"🎉 成功编译 {compiled_count} 个核心算法文件")' >> compile_core.py && \
    python compile_core.py && \
    rm compile_core.py

# 🔒 步骤5: 编译主入口文件为独立可执行文件
RUN python -m nuitka \
    --standalone \
    --onefile \
    --output-filename=aps_server \
    --include-data-dir=app/templates=templates \
    --include-data-dir=app/static=static \
    --include-data-dir=config=config \
    --follow-imports \
    --python-flag=no_site \
    --python-flag=no_warnings \
    run.py

# 🔒 步骤6: 创建安全的应用目录结构
RUN mkdir -p /secure_app && \
    cp -r app/ /secure_app/ && \
    cp -r config/ /secure_app/ && \
    cp dist/aps_server /secure_app/

# 🔒 步骤7: 删除所有残留的.py源文件
RUN find /secure_app -name "*.py" -type f -delete && \
    find /secure_app -name "*.pyx" -type f -delete && \
    find /secure_app -name "*.c" -type f -delete && \
    echo "✅ 所有源代码文件已清理完成"

# ============================================
# 阶段2: 运行环境 (只包含编译后的文件)
# ============================================
FROM python:3.9-slim as runtime

# 标签信息
LABEL maintainer="APS Development Team"
LABEL version="${VERSION}"
LABEL description="APS Secure Runtime Environment"
LABEL security.level="HIGH"

# 🔒 创建非特权用户
RUN groupadd -r apsgroup --gid=1000 && \
    useradd -r -g apsgroup --uid=1000 --home-dir=/app --shell=/bin/bash apsuser

# 🔒 安装最小运行时依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 🔒 安装运行时Python包（仅必需的）
COPY --from=builder /usr/local/lib/python3.9/site-packages/ /usr/local/lib/python3.9/site-packages/
COPY --from=builder /usr/local/bin/ /usr/local/bin/

# 🔒 创建应用目录
WORKDIR /app

# 🔒 只复制编译后的安全文件
COPY --from=builder /secure_app/ /app/

# 🔒 创建必要的目录并设置权限
RUN mkdir -p /app/logs /app/uploads /app/downloads /app/instance && \
    chown -R apsuser:apsgroup /app && \
    chmod -R 750 /app && \
    chmod +x /app/aps_server

# 🔒 切换到非特权用户
USER apsuser

# 🔒 设置安全的环境变量
ENV PYTHONPATH=/app
ENV FLASK_APP=run.py
ENV FLASK_ENV=production
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONHASHSEED=random

# 🔒 暴露端口
EXPOSE 5000

# 🔒 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD python -c "import urllib.request; urllib.request.urlopen('http://localhost:5000')"

# 🔒 设置默认命令
CMD ["./aps_server"]

# ============================================
# 构建说明
# ============================================
# 构建命令:
# docker build --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
#              --build-arg COMMIT_SHA=$(git rev-parse HEAD) \
#              -f Dockerfile.secure \
#              -t aps-secure:latest .
#
# 安全特性:
# ✅ 多阶段构建 - 源代码不存在于最终镜像
# ✅ 代码编译 - 核心算法编译为字节码
# ✅ 非root用户 - 以apsuser运行应用
# ✅ 最小权限 - 只包含必要的运行时组件
# ✅ 健康检查 - 监控应用状态
# ✅ 安全标签 - 镜像元数据和版本信息 