<!DOCTYPE html>
<html>
<head>
    <title>定时任务测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>定时任务功能测试</h1>
    
    <div>
        <h2>测试API连接</h2>
        <button onclick="testGetTasks()">获取任务列表</button>
        <button onclick="testCreateTask()">创建测试任务</button>
        <button onclick="testGetStatus()">获取状态</button>
        <div id="result"></div>
    </div>

    <script>
        function testGetTasks() {
            fetch('/api/v2/system/scheduled-tasks')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('result').innerHTML = 
                        '<h3>获取任务列表结果:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = 
                        '<h3>错误:</h3><pre>' + error.toString() + '</pre>';
                });
        }

        function testCreateTask() {
            const taskData = {
                name: "测试任务",
                type: "daily",
                hour: 9,
                minute: 0,
                strategy: "intelligent",
                target: "balanced",
                autoImport: true,
                emailNotification: false
            };

            fetch('/api/v2/system/scheduled-tasks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(taskData)
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('result').innerHTML = 
                        '<h3>创建任务结果:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = 
                        '<h3>错误:</h3><pre>' + error.toString() + '</pre>';
                });
        }

        function testGetStatus() {
            fetch('/api/v2/system/scheduled-tasks/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('result').innerHTML = 
                        '<h3>获取状态结果:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = 
                        '<h3>错误:</h3><pre>' + error.toString() + '</pre>';
                });
        }
    </script>
</body>
</html>
