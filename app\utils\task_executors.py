# -*- coding: utf-8 -*-
"""
APScheduler任务执行器
定义各种类型的定时任务执行函数

Author: AI Assistant
Date: 2025-06-13
"""

import logging
import json
import traceback
from datetime import datetime, timedelta
from typing import Dict, Any, List

from flask import current_app
from app import db

# 设置日志
logger = logging.getLogger(__name__)

def test_task(message: str = "Hello World", **kwargs) -> Dict[str, Any]:
    """测试任务 - 用于验证调度器功能"""
    try:
        result = {
            'status': 'success',
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'kwargs': kwargs
        }
        
        logger.info(f"测试任务执行成功: {message}")
        return result
        
    except Exception as e:
        logger.error(f"测试任务执行失败: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

def auto_schedule_task(algorithm: str = 'deadline', 
                      optimization_target: str = 'makespan',
                      time_limit: int = 30,
                      **kwargs) -> Dict[str, Any]:
    """自动排产任务 - 定期执行智能排产"""
    try:
        logger.info(f"开始执行自动排产任务: algorithm={algorithm}, target={optimization_target}")
        
        # 导入排产API
        try:
            from app.api.production_api import execute_auto_schedule
        except ImportError:
            # 如果API不存在，返回模拟结果
            logger.warning("production_api模块不存在，返回模拟结果")
            return {
                'status': 'success',
                'scheduled_count': 5,
                'algorithm': algorithm,
                'optimization_target': optimization_target,
                'message': '模拟排产完成',
                'timestamp': datetime.now().isoformat()
            }
        
        # 构建排产参数
        schedule_params = {
            'algorithm': algorithm,
            'optimization_target': optimization_target,
            'time_limit': time_limit,
            'population_size': kwargs.get('population_size', 100),
            'auto_mode': True  # 标记为自动模式
        }
        
        # 执行排产
        schedule_result = execute_auto_schedule(schedule_params)
        
        # 记录结果
        if schedule_result.get('success', False):
            scheduled_count = len(schedule_result.get('schedule', []))
            logger.info(f"自动排产任务完成: 生成 {scheduled_count} 条排产记录")
            
            result = {
                'status': 'success',
                'scheduled_count': scheduled_count,
                'algorithm': algorithm,
                'optimization_target': optimization_target,
                'metrics': schedule_result.get('metrics', {}),
                'timestamp': datetime.now().isoformat(),
                'execution_time': schedule_result.get('execution_time', 0)
            }
        else:
            logger.warning(f"自动排产任务警告: {schedule_result.get('message', '无有效结果')}")
            result = {
                'status': 'warning',
                'message': schedule_result.get('message', '无有效的排产结果'),
                'timestamp': datetime.now().isoformat()
            }
        
        return result
        
    except Exception as e:
        logger.error(f"自动排产任务失败: {e}")
        traceback.print_exc()
        return {
            'status': 'error',
            'error': str(e),
            'algorithm': algorithm,
            'timestamp': datetime.now().isoformat()
        }

def data_sync_task(sync_type: str = 'all', **kwargs) -> Dict[str, Any]:
    """数据同步任务 - 定期同步数据库数据"""
    try:
        logger.info(f"开始执行数据同步任务: sync_type={sync_type}")
        
        sync_results = {}
        total_synced = 0
        
        # 同步不同类型的数据
        if sync_type in ['all', 'wip']:
            # 同步WIP数据
            wip_result = _sync_wip_data()
            sync_results['wip'] = wip_result
            total_synced += wip_result.get('count', 0)
        
        if sync_type in ['all', 'equipment']:
            # 同步设备状态
            eqp_result = _sync_equipment_status()
            sync_results['equipment'] = eqp_result
            total_synced += eqp_result.get('count', 0)
        
        if sync_type in ['all', 'uph']:
            # 同步UPH数据
            uph_result = _sync_uph_data()
            sync_results['uph'] = uph_result
            total_synced += uph_result.get('count', 0)
        
        logger.info(f"数据同步任务完成: 总共同步 {total_synced} 条记录")
        
        return {
            'status': 'success',
            'sync_type': sync_type,
            'total_synced': total_synced,
            'details': sync_results,
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"数据同步任务失败: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'sync_type': sync_type,
            'timestamp': datetime.now().isoformat()
        }

def email_attachment_task(check_interval_hours: int = 1, config_id: int = None, **kwargs) -> Dict[str, Any]:
    """邮件附件获取任务 - 定期检查和处理邮件附件"""
    try:
        logger.info(f"开始执行邮件附件获取任务: check_interval={check_interval_hours}小时, config_id={config_id}")
        
        # 直接使用HighPerformanceEmailProcessor，不再依赖旧的EmailScheduler
        from app.models import EmailConfig
        from app.utils.high_performance_email_processor import HighPerformanceEmailProcessor
        from app import db
        
        # 根据config_id获取特定配置或所有启用的配置
        if config_id is not None:
            # 获取特定邮箱配置
            config = EmailConfig.query.get(config_id)
            if not config:
                logger.error(f"邮箱配置不存在: config_id={config_id}")
                return {
                    'status': 'error',
                    'message': f'邮箱配置不存在: {config_id}',
                    'processed_emails': 0,
                    'processed_attachments': 0,
                    'timestamp': datetime.now().isoformat()
                }
            
            if not config.enabled:
                logger.info(f"邮箱配置已禁用: {config.name}")
                return {
                    'status': 'success',
                    'message': f'邮箱配置已禁用: {config.name}',
                    'processed_emails': 0,
                    'processed_attachments': 0,
                    'timestamp': datetime.now().isoformat()
                }
            
            enabled_configs = [config]
        else:
            # 获取所有启用的邮箱配置
            enabled_configs = EmailConfig.query.filter_by(enabled=True).all()
        
        if not enabled_configs:
            logger.info("没有启用的邮箱配置，跳过邮件附件获取任务")
            return {
                'status': 'success',
                'message': '没有启用的邮箱配置',
                'processed_emails': 0,
                'processed_attachments': 0,
                'check_interval_hours': check_interval_hours,
                'timestamp': datetime.now().isoformat()
            }
        
        total_emails = 0
        total_attachments = 0
        processed_configs = 0
        failed_configs = []
        
        # 处理每个邮箱配置
        for config in enabled_configs:
            try:
                logger.info(f"处理邮箱配置: {config.name} ({config.email})")
                
                # 检查工作时间
                if not _is_in_work_hours(config):
                    logger.info(f"邮箱配置 '{config.name}' 当前不在工作时间内，跳过")
                    continue
                
                # 创建邮件处理器 - 让处理器内部自己管理连接，避免重复连接
                # 移除外部的连接调用，process_fast/fetch_attachments内部会自动处理连接
                processor = HighPerformanceEmailProcessor(config)
                
                # 获取邮件附件（根据配置的获取天数）
                fetch_days = getattr(config, 'fetch_days', 10)
                result = processor.fetch_attachments(days=fetch_days)
                
                # 统计结果
                config_emails = result.get('total', 0)
                config_attachments = result.get('downloaded', 0)
                
                # 检查处理结果，判断是否连接成功
                if not result.get('success', False):
                    error_msg = result.get('error', '处理失败')
                    if '连接失败' in error_msg or 'LOGIN' in error_msg:
                        logger.error(f"邮箱 '{config.name}' 连接失败: {error_msg}")
                        failed_configs.append({
                            'name': config.name,
                            'email': config.email,
                            'error': f'连接失败: {error_msg}'
                        })
                        continue
                
                total_emails += config_emails
                total_attachments += config_attachments
                processed_configs += 1
                
                logger.info(f"邮箱 '{config.name}' 处理完成: {config_emails} 封邮件, {config_attachments} 个附件")
                
            except Exception as e:
                logger.error(f"处理邮箱配置 '{config.name}' 失败: {e}")
                failed_configs.append({
                    'name': config.name,
                    'email': config.email,
                    'error': str(e)
                })
        
        # 构建结果
        result = {
            'status': 'success',
            'processed_emails': total_emails,
            'processed_attachments': total_attachments,
            'processed_configs': processed_configs,
            'total_configs': len(enabled_configs),
            'failed_configs': failed_configs,
            'check_interval_hours': check_interval_hours,
            'timestamp': datetime.now().isoformat()
        }
        
        if failed_configs:
            result['message'] = f"处理完成，但有 {len(failed_configs)} 个配置失败"
        else:
            result['message'] = f"所有邮箱配置处理成功"
        
        logger.info(f"邮件附件获取任务完成: 处理 {total_emails} 封邮件, {total_attachments} 个附件")
        
        return result
        
    except Exception as e:
        logger.error(f"邮件附件获取任务失败: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

def system_maintenance_task(maintenance_type: str = 'all', **kwargs) -> Dict[str, Any]:
    """系统维护任务 - 定期清理和优化系统"""
    try:
        logger.info(f"开始执行系统维护任务: maintenance_type={maintenance_type}")
        
        maintenance_results = {}
        
        if maintenance_type in ['all', 'logs']:
            # 清理旧日志
            log_result = _cleanup_old_logs()
            maintenance_results['logs'] = log_result
        
        if maintenance_type in ['all', 'cache']:
            # 清理缓存
            cache_result = _cleanup_cache()
            maintenance_results['cache'] = cache_result
        
        if maintenance_type in ['all', 'database']:
            # 数据库优化
            db_result = _optimize_database()
            maintenance_results['database'] = db_result
        
        if maintenance_type in ['all', 'temp_files']:
            # 清理临时文件
            temp_result = _cleanup_temp_files()
            maintenance_results['temp_files'] = temp_result
        
        logger.info(f"系统维护任务完成: {maintenance_type}")
        
        return {
            'status': 'success',
            'maintenance_type': maintenance_type,
            'results': maintenance_results,
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"系统维护任务失败: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'maintenance_type': maintenance_type,
            'timestamp': datetime.now().isoformat()
        }

def production_monitoring_task(threshold_hours: int = 24, **kwargs) -> Dict[str, Any]:
    """生产监控任务 - 监控生产状态和异常情况"""
    try:
        logger.info(f"开始执行生产监控任务: threshold_hours={threshold_hours}")
        
        monitoring_results = {
            'overdue_batches': 0,
            'idle_equipment': 0,
            'completion_rate': 0.0,
            'alerts': []
        }
        
        # 检查超期批次
        overdue_count = _check_overdue_batches(threshold_hours)
        monitoring_results['overdue_batches'] = overdue_count
        
        if overdue_count > 0:
            monitoring_results['alerts'].append(f"发现 {overdue_count} 个超期批次")
        
        # 检查设备闲置
        idle_count = _check_idle_equipment(threshold_hours)
        monitoring_results['idle_equipment'] = idle_count
        
        if idle_count > 0:
            monitoring_results['alerts'].append(f"发现 {idle_count} 台设备长时间闲置")
        
        # 计算完成率
        completion_rate = _calculate_completion_rate()
        monitoring_results['completion_rate'] = completion_rate
        
        if completion_rate < 0.8:
            monitoring_results['alerts'].append(f"完成率偏低: {completion_rate:.1%}")
        
        alert_count = len(monitoring_results['alerts'])
        logger.info(f"生产监控任务完成: 发现 {alert_count} 个告警")
        
        return {
            'status': 'success',
            'threshold_hours': threshold_hours,
            'alert_count': alert_count,
            'monitoring_results': monitoring_results,
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"生产监控任务失败: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

# ==================== 辅助函数 ====================

def _sync_wip_data() -> Dict[str, Any]:
    """同步WIP数据"""
    try:
        # 这里应该是实际的WIP数据同步逻辑
        # 暂时返回模拟结果
        synced_count = 10
        
        return {
            'status': 'success',
            'count': synced_count,
            'message': f'WIP数据同步完成: {synced_count} 条记录'
        }
    except Exception as e:
        return {
            'status': 'error',
            'count': 0,
            'error': str(e)
        }

def _sync_equipment_status() -> Dict[str, Any]:
    """同步设备状态"""
    try:
        # 这里应该是实际的设备状态同步逻辑
        synced_count = 15
        
        return {
            'status': 'success',
            'count': synced_count,
            'message': f'设备状态同步完成: {synced_count} 条记录'
        }
    except Exception as e:
        return {
            'status': 'error',
            'count': 0,
            'error': str(e)
        }

def _sync_uph_data() -> Dict[str, Any]:
    """同步UPH数据"""
    try:
        # 这里应该是实际的UPH数据同步逻辑
        synced_count = 8
        
        return {
            'status': 'success',
            'count': synced_count,
            'message': f'UPH数据同步完成: {synced_count} 条记录'
        }
    except Exception as e:
        return {
            'status': 'error',
            'count': 0,
            'error': str(e)
        }

def _is_in_work_hours(config) -> bool:
    """检查当前时间是否在邮箱配置的工作时间内"""
    try:
        from datetime import datetime, time
        
        now = datetime.now()
        current_time = now.time()
        
        # 解析工作时间
        try:
            work_start = datetime.strptime(config.work_start_time, '%H:%M').time()
            work_end = datetime.strptime(config.work_end_time, '%H:%M').time()
        except:
            # 默认工作时间 8:00-18:00
            work_start = time(8, 0)
            work_end = time(18, 0)
        
        # 检查是否在工作时间内
        if work_start <= work_end:
            # 正常情况：如 08:00-18:00
            return work_start <= current_time <= work_end
        else:
            # 跨日情况：如 22:00-06:00
            return current_time >= work_start or current_time <= work_end
            
    except Exception as e:
        logger.warning(f"检查工作时间失败，默认允许执行: {e}")
        return True

def _cleanup_old_logs() -> Dict[str, Any]:
    """清理旧日志"""
    try:
        # 清理7天前的调度器日志
        cutoff_date = datetime.now() - timedelta(days=7)
        
        from app.models import SchedulerJobLog
        old_logs = SchedulerJobLog.query.filter(SchedulerJobLog.created_at < cutoff_date).all()
        
        deleted_count = len(old_logs)
        for log in old_logs:
            db.session.delete(log)
        
        db.session.commit()
        
        return {
            'status': 'success',
            'deleted_count': deleted_count,
            'message': f'清理了 {deleted_count} 条旧日志记录'
        }
    except Exception as e:
        db.session.rollback()
        return {
            'status': 'error',
            'deleted_count': 0,
            'error': str(e)
        }

def _cleanup_cache() -> Dict[str, Any]:
    """清理缓存"""
    try:
        # 这里可以添加缓存清理逻辑
        # 比如清理Redis缓存、文件缓存等
        
        return {
            'status': 'success',
            'message': '缓存清理完成'
        }
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e)
        }

def _optimize_database() -> Dict[str, Any]:
    """优化数据库"""
    try:
        # 这里可以添加数据库优化逻辑
        # 比如重建索引、清理碎片等
        
        return {
            'status': 'success',
            'message': '数据库优化完成'
        }
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e)
        }

def _cleanup_temp_files() -> Dict[str, Any]:
    """清理临时文件"""
    try:
        import os
        import glob
        
        # 清理下载目录中的临时文件
        temp_patterns = [
            'downloads/*.tmp',
            'static/exports/*.tmp',
            'logs/*.tmp'
        ]
        
        deleted_count = 0
        for pattern in temp_patterns:
            files = glob.glob(pattern)
            for file_path in files:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        deleted_count += 1
                except:
                    pass
        
        return {
            'status': 'success',
            'deleted_count': deleted_count,
            'message': f'清理了 {deleted_count} 个临时文件'
        }
    except Exception as e:
        return {
            'status': 'error',
            'deleted_count': 0,
            'error': str(e)
        }

def _check_overdue_batches(threshold_hours: int) -> int:
    """检查超期批次"""
    try:
        # 这里应该是实际的超期批次检查逻辑
        # 暂时返回模拟结果
        overdue_count = 0
        
        return overdue_count
    except Exception as e:
        logger.error(f"检查超期批次失败: {e}")
        return 0

def _check_idle_equipment(threshold_hours: int) -> int:
    """检查闲置设备"""
    try:
        # 这里应该是实际的设备闲置检查逻辑
        # 暂时返回模拟结果
        idle_count = 0
        
        return idle_count
    except Exception as e:
        logger.error(f"检查闲置设备失败: {e}")
        return 0

def _calculate_completion_rate() -> float:
    """计算完成率"""
    try:
        # 这里应该是实际的完成率计算逻辑
        # 暂时返回模拟结果
        completion_rate = 0.85
        
        return completion_rate
    except Exception as e:
        logger.error(f"计算完成率失败: {e}")
        return 0.0

# 导出所有任务函数
__all__ = [
    'test_task',
    'auto_schedule_task',
    'data_sync_task',
    'email_attachment_task',
    'system_maintenance_task',
    'production_monitoring_task'
] 