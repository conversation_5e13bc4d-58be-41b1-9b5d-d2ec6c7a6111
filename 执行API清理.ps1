#!/usr/bin/env pwsh

<#
.SYNOPSIS
    AEC-FT API Cleanup Tool
.DESCRIPTION
    Clean up redundant API endpoints and unused files
.PARAMETER Phase
    Cleanup phase: 1=Safe cleanup, 2=API migration, 3=Deep cleanup
.PARAMETER DryRun
    Preview mode without actual deletion
.PARAMETER BackupDir
    Backup directory path
.EXAMPLE
    .\执行API清理.ps1 -Phase 1 -DryRun
.EXAMPLE
    .\执行API清理.ps1 -Phase 1 -BackupDir "backup_20250117"
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet(1, 2, 3)]
    [int]$Phase = 1,
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun,
    
    [Parameter(Mandatory=$false)]
    [string]$BackupDir = "backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
)

$ErrorActionPreference = "Stop"

# Color output functions
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "[OK] $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARN] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Cyan"
}

# Create backup
function Create-Backup {
    Write-Info "Creating backup to: $BackupDir"
    
    if (-not (Test-Path $BackupDir)) {
        New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
        Write-Success "Backup directory created"
    }
    
    # Backup critical files
    $backupFiles = @(
        "app\compat\",
        "app\api\routes.py",
        "app\api\routes_v3.py",
        "app\utils\",
        "app\config\deprecated_api_map.json"
    )
    
    foreach ($file in $backupFiles) {
        if (Test-Path $file) {
            $destPath = Join-Path $BackupDir $file
            $destDir = Split-Path $destPath -Parent
            
            if (-not (Test-Path $destDir)) {
                New-Item -ItemType Directory -Path $destDir -Force | Out-Null
            }
            
            if (Test-Path $file -PathType Container) {
                Copy-Item -Path $file -Destination $destPath -Recurse -Force
            } else {
                Copy-Item -Path $file -Destination $destPath -Force
            }
            
            Write-Success "Backed up: $file"
        }
    }
}

# Phase 1: Safe cleanup
function Execute-Phase1 {
    Write-Info "=== Phase 1: Safe Cleanup (Low Risk) ==="
    
    # 1. Delete unused compatibility files
    $compatFilesToDelete = @(
        "app\compat\model_injector.py",
        "app\compat\legacy_model_proxy.py"
    )
    
    Write-Info "Cleaning unused compatibility files..."
    foreach ($file in $compatFilesToDelete) {
        if (Test-Path $file) {
            if ($DryRun) {
                Write-Warning "[DRY RUN] Would delete: $file"
            } else {
                Remove-Item -Path $file -Force
                Write-Success "Deleted: $file"
            }
        } else {
            Write-Info "File not found: $file"
        }
    }
    
    # 2. Delete unused utility files
    $utilFilesToDelete = @(
        "app\utils\page_tester.py",
        "app\utils\frontend_cleanup.py"
    )
    
    Write-Info "Cleaning unused utility files..."
    foreach ($file in $utilFilesToDelete) {
        if (Test-Path $file) {
            if ($DryRun) {
                Write-Warning "[DRY RUN] Would delete: $file"
            } else {
                Remove-Item -Path $file -Force
                Write-Success "Deleted: $file"
            }
        } else {
            Write-Info "File not found: $file"
        }
    }
    
    # 3. Delete test files
    $testFilesToDelete = @(
        "app\static\test_styles.html"
    )
    
    Write-Info "Cleaning test files..."
    foreach ($file in $testFilesToDelete) {
        if (Test-Path $file) {
            if ($DryRun) {
                Write-Warning "[DRY RUN] Would delete: $file"
            } else {
                Remove-Item -Path $file -Force
                Write-Success "Deleted: $file"
            }
        } else {
            Write-Info "File not found: $file"
        }
    }
    
    Write-Success "Phase 1 cleanup completed!"
}

# Phase 2: API migration preparation
function Execute-Phase2 {
    Write-Info "=== Phase 2: API Migration (Medium Risk) ==="
    
    Write-Warning "Phase 2 requires manual steps:"
    Write-Info "1. Analyze frontend API calls"
    Write-Info "2. Create corresponding API v2 endpoints"
    Write-Info "3. Test new endpoint functionality"
    Write-Info "4. Gradually migrate frontend calls"
    
    # Show APIs to migrate
    Write-Info "High priority APIs to migrate:"
    $highPriorityAPIs = @(
        "/api/production/import-from-directory",
        "/api/production/save-priority-done",
        "/api/orders/scan-lot-types",
        "/api/orders/classification-rules"
    )
    
    foreach ($api in $highPriorityAPIs) {
        Write-Info "  - $api"
    }
    
    Write-Info "Medium priority APIs to migrate:"
    $mediumPriorityAPIs = @(
        "/api/email_configs/*",
        "/api/email_attachments/*",
        "/api/ai/chat",
        "/api/ai/status"
    )
    
    foreach ($api in $mediumPriorityAPIs) {
        Write-Info "  - $api"
    }
    
    Write-Warning "Please complete API migration manually, then run Phase 3"
}

# Phase 3: Deep cleanup
function Execute-Phase3 {
    Write-Info "=== Phase 3: Deep Cleanup (High Risk) ==="
    
    Write-Warning "Phase 3 contains high-risk operations. Please confirm:"
    Write-Warning "1. API migration testing completed"
    Write-Warning "2. All important files backed up"
    Write-Warning "3. Tested in staging environment"
    
    $confirmation = Read-Host "Continue with Phase 3? (y/N)"
    if ($confirmation -ne "y" -and $confirmation -ne "Y") {
        Write-Info "Phase 3 execution cancelled"
        return
    }
    
    Write-Info "Starting deep cleanup..."
    
    # Add more cleanup logic here
    Write-Warning "Phase 3 requires manual complex refactoring work"
    Write-Info "Recommend consulting development team before execution"
}

# Check project structure
function Test-ProjectStructure {
    $requiredPaths = @(
        "app\api\",
        "app\compat\",
        "app\utils\",
        "docs\"
    )
    
    foreach ($path in $requiredPaths) {
        if (-not (Test-Path $path)) {
            Write-Error "Project path not found: $path"
            Write-Error "Please run this script from the project root directory"
            exit 1
        }
    }
    
    Write-Success "Project structure check passed"
}

# Generate cleanup report
function Generate-CleanupReport {
    $reportFile = "API_Cleanup_Report_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
    
    $report = @"
=== AEC-FT API Cleanup Report ===
Execution Time: $(Get-Date)
Phase: $Phase
Dry Run Mode: $DryRun
Backup Directory: $BackupDir

Results:
Phase1: Safe cleanup completed
Phase2: Manual API migration required
Phase3: Manual deep cleanup required

Notes:
1. Test all functions to ensure normal operation
2. Restore from backup directory if issues occur
3. Recommend full testing before production deployment

Backup Location: $BackupDir
"@
    
    $report | Out-File -FilePath $reportFile -Encoding UTF8
    Write-Success "Cleanup report generated: $reportFile"
}

# Main execution flow
try {
    Write-Info "=== AEC-FT API Cleanup Tool ==="
    Write-Info "Phase: $Phase"
    Write-Info "Dry Run: $DryRun"
    Write-Info "Backup Dir: $BackupDir"
    Write-Info ""
    
    # Check project structure
    Test-ProjectStructure
    
    # Create backup
    if (-not $DryRun) {
        Create-Backup
    }
    
    # Execute corresponding phase
    switch ($Phase) {
        1 { Execute-Phase1 }
        2 { Execute-Phase2 }
        3 { Execute-Phase3 }
    }
    
    # Generate report
    Generate-CleanupReport
    
    Write-Success "=== API Cleanup Execution Completed ==="
    Write-Info "Please check the generated report file for details"
    
    if ($DryRun) {
        Write-Warning "This was a dry run - no files were actually deleted"
        Write-Info "To execute for real, remove the -DryRun parameter"
    }
    
} catch {
    Write-Error "Error occurred during execution: $($_.Exception.Message)"
    Write-Error "Please check error information and retry"
    exit 1
} 