#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试后端调度器服务
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.services.background_scheduler_service import background_scheduler

def test_scheduler():
    """测试后端调度器服务"""
    result = create_app()
    if isinstance(result, tuple):
        app, _ = result
    else:
        app = result
    
    with app.app_context():
        print("🔍 测试后端调度器服务...")
        
        # 初始化调度器
        background_scheduler.init_app(app)
        
        # 手动调用加载任务方法
        print("📋 手动加载数据库任务...")
        background_scheduler._load_tasks_from_database()
        
        # 检查加载的任务
        print(f"✅ 加载了 {len(background_scheduler.tasks)} 个任务")
        
        for task_id, task_config in background_scheduler.tasks.items():
            print(f"  - 任务ID: {task_id}")
            print(f"    名称: {task_config.get('name')}")
            print(f"    类型: {task_config.get('type')}")
            print(f"    状态: {task_config.get('status')}")
            print(f"    下次执行: {task_config.get('next_run_time')}")
            print()
        
        # 检查调度器中的作业
        if background_scheduler.scheduler:
            jobs = background_scheduler.scheduler.get_jobs()
            print(f"📅 调度器中有 {len(jobs)} 个作业")
            for job in jobs:
                print(f"  - 作业ID: {job.id}")
                print(f"    名称: {job.name}")
                # 安全地获取下次运行时间
                try:
                    next_run = getattr(job, 'next_run_time', '未知')
                    print(f"    下次运行: {next_run}")
                except:
                    print(f"    下次运行: 未知")
                print()

if __name__ == '__main__':
    test_scheduler()
