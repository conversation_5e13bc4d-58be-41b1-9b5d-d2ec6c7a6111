#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端定时任务调度服务
替代前端localStorage的定时任务管理，提供完整的后端定时任务功能
"""

import logging
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR, EVENT_JOB_MISSED
import pytz

logger = logging.getLogger(__name__)

class BackgroundSchedulerService:
    """后端定时任务调度服务"""
    
    def __init__(self):
        self.scheduler = None
        self.is_running = False
        self.tasks = {}  # 内存缓存，从数据库加载
        self.timezone = pytz.timezone('Asia/Shanghai')
        self.app = None
        
    def init_app(self, app):
        """初始化Flask应用"""
        self.app = app
        
        # 创建调度器
        self.scheduler = BackgroundScheduler(timezone=self.timezone)
        
        # 添加事件监听器
        self.scheduler.add_listener(self._job_listener, 
                                  EVENT_JOB_EXECUTED | EVENT_JOB_ERROR | EVENT_JOB_MISSED)
        
        # 注册到Flask扩展
        app.extensions['background_scheduler'] = self
        
        logger.info("后端定时任务调度服务初始化完成")
    
    def start(self):
        """启动调度器"""
        if self.scheduler and not self.is_running:
            try:
                self.scheduler.start()
                self.is_running = True

                # 启动后加载数据库中的任务
                self._load_tasks_from_database()

                logger.info("后端定时任务调度器启动成功")
                return True
            except Exception as e:
                logger.error(f"启动后端定时任务调度器失败: {e}")
                return False
        return False
    
    def stop(self):
        """停止调度器"""
        if self.scheduler and self.is_running:
            try:
                self.scheduler.shutdown(wait=False)
                self.is_running = False
                logger.info("后端定时任务调度器停止成功")
                return True
            except Exception as e:
                logger.error(f"停止后端定时任务调度器失败: {e}")
                return False
        return False
    
    def create_scheduled_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建定时任务"""
        try:
            # 生成任务ID
            task_id = f"task_{uuid.uuid4().hex[:8]}"
            
            # 验证任务数据
            if not self._validate_task_data(task_data):
                return {
                    'success': False,
                    'message': '任务数据验证失败'
                }
            
            # 创建触发器
            trigger = self._create_trigger(task_data)
            if not trigger:
                return {
                    'success': False,
                    'message': '创建触发器失败'
                }
            
            # 添加任务到调度器
            job = self.scheduler.add_job(
                func=self._execute_scheduling_task,
                trigger=trigger,
                args=[task_data],
                id=task_id,
                name=task_data.get('name', f'Task_{task_id}'),
                replace_existing=True
            )
            
            # 保存任务配置到内存
            task_config = {
                **task_data,
                'id': task_id,
                'status': 'active',
                'created_at': datetime.now().isoformat(),
                'last_executed': None,
                'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None
            }
            self.tasks[task_id] = task_config

            # 🔥 保存到数据库
            self._save_task_to_database(task_config)

            logger.info(f"定时任务创建成功: {task_data.get('name')} (ID: {task_id})")
            
            return {
                'success': True,
                'message': '任务创建成功',
                'task_id': task_id,
                'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None
            }
            
        except Exception as e:
            logger.error(f"创建定时任务失败: {e}")
            return {
                'success': False,
                'message': f'创建任务失败: {str(e)}'
            }
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """获取所有任务"""
        try:
            tasks_list = []
            
            for task_id, task_config in self.tasks.items():
                # 获取调度器中的任务状态
                job = self.scheduler.get_job(task_id)
                
                task_info = {
                    **task_config,
                    'next_run_time': job.next_run_time.isoformat() if job and job.next_run_time else None,
                    'status': 'active' if job else 'paused'
                }
                
                tasks_list.append(task_info)
            
            return tasks_list
            
        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return []
    
    def pause_task(self, task_id: str) -> Dict[str, Any]:
        """暂停任务"""
        try:
            job = self.scheduler.get_job(task_id)
            if not job:
                return {
                    'success': False,
                    'message': '任务不存在'
                }
            
            self.scheduler.pause_job(task_id)
            
            if task_id in self.tasks:
                self.tasks[task_id]['status'] = 'paused'
            
            logger.info(f"任务暂停成功: {task_id}")
            
            return {
                'success': True,
                'message': '任务已暂停'
            }
            
        except Exception as e:
            logger.error(f"暂停任务失败: {e}")
            return {
                'success': False,
                'message': f'暂停任务失败: {str(e)}'
            }
    
    def resume_task(self, task_id: str) -> Dict[str, Any]:
        """恢复任务"""
        try:
            job = self.scheduler.get_job(task_id)
            if not job:
                return {
                    'success': False,
                    'message': '任务不存在'
                }
            
            self.scheduler.resume_job(task_id)
            
            if task_id in self.tasks:
                self.tasks[task_id]['status'] = 'active'
            
            logger.info(f"任务恢复成功: {task_id}")
            
            return {
                'success': True,
                'message': '任务已恢复'
            }
            
        except Exception as e:
            logger.error(f"恢复任务失败: {e}")
            return {
                'success': False,
                'message': f'恢复任务失败: {str(e)}'
            }
    
    def delete_task(self, task_id: str) -> Dict[str, Any]:
        """删除任务"""
        try:
            # 从调度器中删除
            self.scheduler.remove_job(task_id)
            
            # 从内存中删除
            if task_id in self.tasks:
                del self.tasks[task_id]

            # 🔥 从数据库中删除
            self._delete_task_from_database(task_id)

            logger.info(f"任务删除成功: {task_id}")
            
            return {
                'success': True,
                'message': '任务已删除'
            }
            
        except Exception as e:
            logger.error(f"删除任务失败: {e}")
            return {
                'success': False,
                'message': f'删除任务失败: {str(e)}'
            }

    def cleanup_orphaned_tasks(self) -> Dict[str, Any]:
        """清理孤立任务"""
        try:
            cleaned_count = 0
            total_found = 0

            # 获取调度器中的所有任务
            scheduler_jobs = self.scheduler.get_jobs()
            scheduler_job_ids = {job.id for job in scheduler_jobs}

            # 获取内存中的任务
            memory_task_ids = set(self.tasks.keys())

            # 找出孤立的任务
            orphaned_in_scheduler = scheduler_job_ids - memory_task_ids
            orphaned_in_memory = memory_task_ids - scheduler_job_ids

            total_found = len(orphaned_in_scheduler) + len(orphaned_in_memory)

            # 清理调度器中的孤立任务
            for job_id in orphaned_in_scheduler:
                try:
                    self.scheduler.remove_job(job_id)
                    cleaned_count += 1
                    logger.info(f"清理调度器中的孤立任务: {job_id}")
                except Exception as e:
                    logger.error(f"清理调度器任务失败 {job_id}: {e}")

            # 清理内存中的孤立任务
            for task_id in orphaned_in_memory:
                try:
                    del self.tasks[task_id]
                    cleaned_count += 1
                    logger.info(f"清理内存中的孤立任务: {task_id}")
                except Exception as e:
                    logger.error(f"清理内存任务失败 {task_id}: {e}")

            return {
                'success': True,
                'message': f'清理完成，共清理 {cleaned_count} 个孤立任务',
                'cleaned_count': cleaned_count,
                'total_found': total_found
            }

        except Exception as e:
            logger.error(f"清理孤立任务失败: {e}")
            return {
                'success': False,
                'message': f'清理失败: {str(e)}'
            }
    
    def _validate_task_data(self, task_data: Dict[str, Any]) -> bool:
        """验证任务数据"""
        required_fields = ['name', 'type']
        
        for field in required_fields:
            if not task_data.get(field):
                logger.error(f"缺少必要字段: {field}")
                return False
        
        task_type = task_data.get('type')
        if task_type not in ['once', 'daily', 'weekly', 'interval']:
            logger.error(f"无效的任务类型: {task_type}")
            return False
        
        return True
    
    def _create_trigger(self, task_data: Dict[str, Any]):
        """创建触发器"""
        task_type = task_data.get('type')
        
        try:
            if task_type == 'once':
                # 一次性任务
                date_str = task_data.get('date')
                hour = task_data.get('hour', 0)
                minute = task_data.get('minute', 0)
                
                run_date = datetime.strptime(f"{date_str} {hour:02d}:{minute:02d}", "%Y-%m-%d %H:%M")
                run_date = self.timezone.localize(run_date)
                
                return DateTrigger(run_date=run_date)
                
            elif task_type == 'daily':
                # 每日任务
                hour = task_data.get('hour', 0)
                minute = task_data.get('minute', 0)
                
                return CronTrigger(hour=hour, minute=minute, timezone=self.timezone)
                
            elif task_type == 'weekly':
                # 每周任务
                hour = task_data.get('hour', 0)
                minute = task_data.get('minute', 0)
                weekdays = task_data.get('weekdays', [])
                
                # 转换星期几
                day_map = {
                    'sunday': 'sun', 'monday': 'mon', 'tuesday': 'tue',
                    'wednesday': 'wed', 'thursday': 'thu', 'friday': 'fri', 'saturday': 'sat'
                }
                
                cron_days = ','.join([day_map.get(day, day) for day in weekdays])
                
                return CronTrigger(day_of_week=cron_days, hour=hour, minute=minute, timezone=self.timezone)
                
            elif task_type == 'interval':
                # 间隔任务
                interval_value = task_data.get('intervalValue', 1)
                interval_unit = task_data.get('intervalUnit', 'hours')
                
                kwargs = {}
                if interval_unit == 'minutes':
                    kwargs['minutes'] = interval_value
                elif interval_unit == 'hours':
                    kwargs['hours'] = interval_value
                elif interval_unit == 'days':
                    kwargs['days'] = interval_value
                
                return IntervalTrigger(**kwargs, timezone=self.timezone)
                
        except Exception as e:
            logger.error(f"创建触发器失败: {e}")
            return None
    
    def _execute_scheduling_task(self, task_data: Dict[str, Any]):
        """执行排产任务"""
        try:
            with self.app.app_context():
                logger.info(f"开始执行定时排产任务: {task_data.get('name')}")
                
                # 导入排产服务
                from app.api_v2.production.manual_scheduling_api import execute_manual_scheduling_internal
                
                # 准备排产参数
                scheduling_params = {
                    'algorithm': task_data.get('strategy', 'intelligent'),
                    'optimization_target': task_data.get('target', 'balanced'),
                    'auto_mode': True,
                    'time_limit': 30,
                    'population_size': 100
                }
                
                # 执行排产
                result = execute_manual_scheduling_internal(scheduling_params)
                
                # 更新任务执行记录
                task_id = task_data.get('id')
                if task_id and task_id in self.tasks:
                    self.tasks[task_id]['last_executed'] = datetime.now().isoformat()
                
                if result.get('success'):
                    logger.info(f"定时排产任务执行成功: {task_data.get('name')}")
                else:
                    logger.error(f"定时排产任务执行失败: {result.get('message')}")
                
        except Exception as e:
            logger.error(f"执行定时排产任务失败: {e}")
    
    def _job_listener(self, event):
        """任务事件监听器"""
        job_id = event.job_id

        if event.exception:
            logger.error(f"任务执行失败 {job_id}: {event.exception}")
        else:
            logger.info(f"任务执行成功 {job_id}")

    def _load_tasks_from_database(self):
        """从数据库加载任务"""
        if not self.app:
            return

        try:
            with self.app.app_context():
                from app.models import SchedulingTasks
                from app import db

                # 查询所有启用的任务
                db_tasks = SchedulingTasks.query.filter_by(enabled=True).all()

                for db_task in db_tasks:
                    try:
                        # 解析任务配置
                        task_config = json.loads(db_task.schedule_config)
                        task_config['id'] = str(db_task.id)
                        task_config['name'] = db_task.task_name
                        task_config['type'] = db_task.task_type
                        task_config['created_at'] = db_task.created_at.isoformat() if db_task.created_at else None
                        task_config['last_executed'] = db_task.last_run.isoformat() if db_task.last_run else None

                        # 创建触发器
                        trigger = self._create_trigger(task_config)
                        if trigger:
                            # 添加到调度器
                            job = self.scheduler.add_job(
                                func=self._execute_scheduling_task,
                                trigger=trigger,
                                args=[task_config],
                                id=str(db_task.id),
                                name=db_task.task_name,
                                replace_existing=True
                            )

                            # 添加到内存缓存
                            task_config['next_run_time'] = job.next_run_time.isoformat() if job.next_run_time else None
                            task_config['status'] = 'active'
                            self.tasks[str(db_task.id)] = task_config

                            logger.info(f"从数据库恢复任务: {db_task.task_name}")

                    except Exception as e:
                        logger.error(f"恢复任务失败 {db_task.task_name}: {e}")

                logger.info(f"从数据库恢复了 {len(self.tasks)} 个定时任务")

        except Exception as e:
            logger.error(f"从数据库加载任务失败: {e}")

    def _save_task_to_database(self, task_config: Dict[str, Any]):
        """保存任务到数据库"""
        if not self.app:
            return

        try:
            with self.app.app_context():
                from app.models import SchedulingTasks
                from app import db

                # 创建数据库记录
                db_task = SchedulingTasks(
                    task_name=task_config.get('name'),
                    task_type=task_config.get('type'),
                    schedule_config=json.dumps(task_config),
                    enabled=True,
                    created_by=task_config.get('created_by'),
                    next_run=datetime.fromisoformat(task_config['next_run_time']) if task_config.get('next_run_time') else None
                )

                db.session.add(db_task)
                db.session.commit()

                # 更新任务ID为数据库ID
                old_id = task_config['id']
                new_id = str(db_task.id)

                # 更新调度器中的任务ID
                job = self.scheduler.get_job(old_id)
                if job:
                    self.scheduler.remove_job(old_id)
                    self.scheduler.add_job(
                        func=self._execute_scheduling_task,
                        trigger=job.trigger,
                        args=[{**task_config, 'id': new_id}],
                        id=new_id,
                        name=task_config.get('name'),
                        replace_existing=True
                    )

                # 更新内存缓存
                if old_id in self.tasks:
                    del self.tasks[old_id]
                task_config['id'] = new_id
                self.tasks[new_id] = task_config

                logger.info(f"任务已保存到数据库: {task_config.get('name')} (ID: {new_id})")

        except Exception as e:
            logger.error(f"保存任务到数据库失败: {e}")

    def _delete_task_from_database(self, task_id: str):
        """从数据库删除任务"""
        if not self.app:
            return

        try:
            with self.app.app_context():
                from app.models import SchedulingTasks
                from app import db

                # 删除数据库记录
                db_task = SchedulingTasks.query.get(task_id)
                if db_task:
                    db.session.delete(db_task)
                    db.session.commit()
                    logger.info(f"任务已从数据库删除: {task_id}")

        except Exception as e:
            logger.error(f"从数据库删除任务失败: {e}")

# 创建全局实例
background_scheduler = BackgroundSchedulerService()
