# APS Secure Deployment Script - Fixed Version
# Banking-level security for source code protection
# Author: Claude AI Assistant

param(
    [switch]$BuildOnly,        # Build image only, do not start services
    [switch]$NoBuild,          # Skip build, use existing image
    [switch]$TestMode,         # Test mode with test configuration
    [switch]$Force,            # Force execution, skip confirmations
    [string]$Registry = "",    # Private registry address
    [string]$Version = "1.0.0" # Version number
)

# Set encoding and error handling
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$ErrorActionPreference = "Stop"

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    
    $validColors = @("Black", "DarkBlue", "DarkGreen", "DarkCyan", "DarkRed", "DarkMagenta", "DarkYellow", "Gray", "DarkGray", "Blue", "Green", "Cyan", "Red", "Magenta", "Yellow", "White")
    
    if ($Color -in $validColors) {
        Write-Host $Text -ForegroundColor $Color
    }
    else {
        Write-Host $Text -ForegroundColor White
    }
}

function Test-Prerequisites {
    Write-ColorText "Checking deployment prerequisites..." "Blue"
    
    # Check Docker
    try {
        $dockerVersion = docker --version
        Write-ColorText "✅ Docker version: $dockerVersion" "Green"
    }
    catch {
        Write-ColorText "❌ Docker not installed or not running" "Red"
        return $false
    }
    
    # Check Docker Compose
    try {
        $composeVersion = docker-compose --version
        Write-ColorText "✅ Docker Compose version: $composeVersion" "Green"
    }
    catch {
        Write-ColorText "❌ Docker Compose not installed" "Red"
        return $false
    }
    
    # Check required files
    $requiredFiles = @(
        "requirements_optimized.txt",
        "Dockerfile.secure.simplified", 
        "docker-compose.secure.yml"
    )
    
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-ColorText "✅ Found required file: $file" "Green"
        }
        else {
            Write-ColorText "❌ Missing required file: $file" "Red"
            return $false
        }
    }
    
    return $true
}

function Initialize-SecurityConfig {
    Write-ColorText "Initializing security configuration..." "Blue"
    
    # Create .env file
    if (-not (Test-Path ".env") -or $Force) {
        Write-ColorText "Creating secure environment configuration..." "Blue"
        
        $envContent = @"
# APS Secure Deployment Environment Configuration
# Please modify these values according to your actual situation

# Build information
BUILD_DATE=$(Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
VERSION=$Version
COMMIT_SHA=$(if (Get-Command git -ErrorAction SilentlyContinue) { git rev-parse HEAD 2>$null } else { "unknown" })

# Database configuration
MYSQL_ROOT_PASSWORD=$(New-Guid | Select-Object -ExpandProperty Guid | ForEach-Object { $_.Replace('-', '').Substring(0, 16) })
MYSQL_PASSWORD=$(New-Guid | Select-Object -ExpandProperty Guid | ForEach-Object { $_.Replace('-', '').Substring(0, 12) })

# Application keys
SECRET_KEY=$(New-Guid | Select-Object -ExpandProperty Guid | ForEach-Object { $_.Replace('-', '') })
JWT_SECRET=$(New-Guid | Select-Object -ExpandProperty Guid | ForEach-Object { $_.Replace('-', '') })

# Redis configuration
REDIS_PASSWORD=$(New-Guid | Select-Object -ExpandProperty Guid | ForEach-Object { $_.Replace('-', '').Substring(0, 16) })

# Registry configuration
DOCKER_REGISTRY=$Registry
"@
        
        $envContent | Out-File -FilePath ".env" -Encoding UTF8
        Write-ColorText "✅ Environment configuration generated: .env" "Green"
        Write-ColorText "⚠️ Please keep the passwords in .env file safe!" "Yellow"
    }
    
    # Create secrets directory if not exists
    if (-not (Test-Path "secrets")) {
        New-Item -ItemType Directory -Path "secrets" | Out-Null
        Write-ColorText "✅ Created secrets directory" "Green"
    }
    
    # Create .dockerignore file
    if (-not (Test-Path ".dockerignore") -or $Force) {
        $dockerignoreContent = @"
# Docker build ignore file - Prevent sensitive information leakage
*.md
*.txt
!requirements_optimized.txt
.git
.gitignore
.env
secrets/
logs/
downloads/
uploads/
instance/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.coverage
.pytest_cache/
node_modules/
.DS_Store
Thumbs.db
*.backup
requirements_backup_*.txt
dependency_audit_report_*.json
"@
        
        $dockerignoreContent | Out-File -FilePath ".dockerignore" -Encoding UTF8
        Write-ColorText "✅ Docker ignore file created: .dockerignore" "Green"
    }
}

function Build-SecureImage {
    Write-ColorText "Building secure Docker image..." "Blue"
    
    # Read environment variables
    if (Test-Path ".env") {
        Get-Content ".env" | ForEach-Object {
            if ($_ -match "^([^=]+)=(.*)$") {
                [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
            }
        }
    }
    
    $buildArgs = @(
        "--build-arg", "BUILD_DATE=$([Environment]::GetEnvironmentVariable('BUILD_DATE'))",
        "--build-arg", "VERSION=$([Environment]::GetEnvironmentVariable('VERSION'))",
        "--build-arg", "COMMIT_SHA=$([Environment]::GetEnvironmentVariable('COMMIT_SHA'))"
    )
    
    $imageName = if ($Registry) { "$Registry/aps-secure:$Version" } else { "aps-secure:$Version" }
    
    try {
        Write-ColorText "Starting to build image: $imageName" "Blue"
        & docker build -f Dockerfile.secure.simplified -t $imageName @buildArgs .
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ Secure image built successfully: $imageName" "Green"
            
            # Show image information
            $imageInfo = docker images $imageName --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
            Write-ColorText "Image information:" "Blue"
            Write-ColorText $imageInfo "White"
            
            return $true
        }
        else {
            Write-ColorText "❌ Image build failed" "Red"
            return $false
        }
    }
    catch {
        Write-ColorText "❌ Build process error: $_" "Red"
        return $false
    }
}

function Deploy-SecureStack {
    Write-ColorText "Deploying secure container stack..." "Blue"
    
    try {
        # Stop existing services
        Write-ColorText "Stopping existing services..." "Yellow"
        docker-compose -f docker-compose.secure.yml down 2>$null
        
        # Start secure service stack
        Write-ColorText "Starting secure service stack..." "Blue"
        docker-compose -f docker-compose.secure.yml up -d
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ Secure container stack deployed successfully!" "Green"
            
            # Wait for services to start
            Write-ColorText "Waiting for services to start..." "Yellow"
            Start-Sleep -Seconds 30
            
            # Check service status
            Write-ColorText "Service status check:" "Blue"
            docker-compose -f docker-compose.secure.yml ps
            
            # Test application access
            Test-ServiceAccess
            
            return $true
        }
        else {
            Write-ColorText "❌ Service deployment failed" "Red"
            return $false
        }
    }
    catch {
        Write-ColorText "❌ Deployment process error: $_" "Red"
        return $false
    }
}

function Test-ServiceAccess {
    Write-ColorText "Testing service access..." "Blue"
    
    $maxRetries = 10
    $retryCount = 0
    
    while ($retryCount -lt $maxRetries) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:5000" -TimeoutSec 5 -ErrorAction Stop
            Write-ColorText "✅ APS service access normal (HTTP $($response.StatusCode))" "Green"
            Write-ColorText "Application access URL: http://localhost:5000" "Cyan"
            return $true
        }
        catch {
            $retryCount++
            Write-ColorText "Waiting for service startup... ($retryCount/$maxRetries)" "Yellow"
            Start-Sleep -Seconds 5
        }
    }
    
    Write-ColorText "⚠️ Service access test timeout, please check manually" "Yellow"
    return $false
}

function Show-SecuritySummary {
    Write-ColorText "`n" + "="*60 "Blue"
    Write-ColorText "APS Security Deployment Summary" "Blue"
    Write-ColorText "="*60 "Blue"
    
    Write-ColorText "`nSecurity Features:" "Blue"
    Write-ColorText "   ✅ Source code compilation protection - Core algorithms compiled to bytecode" "Green"
    Write-ColorText "   ✅ Multi-stage Docker build - Source code not in runtime image" "Green"
    Write-ColorText "   ✅ Non-root user execution - Container runs as apsuser" "Green"
    Write-ColorText "   ✅ Read-only filesystem - Prevents runtime file tampering" "Green"
    Write-ColorText "   ✅ Network isolation - Frontend/backend network separation" "Green"
    Write-ColorText "   ✅ Minimal privileges - Removed unnecessary Linux capabilities" "Green"
    
    Write-ColorText "`nCore Algorithm Protection:" "Blue"
    Write-ColorText "   real_scheduling_service.py - Compilation protected" "Cyan"
    Write-ColorText "   algorithm_selector.py - Compilation protected" "Cyan"
    Write-ColorText "   multilevel_cache_manager.py - Compilation protected" "Cyan"
    Write-ColorText "   parallel_scheduling_engine.py - Compilation protected" "Cyan"
    
    Write-ColorText "`nService Information:" "Blue"
    Write-ColorText "   Web access: http://localhost:5000" "White"
    Write-ColorText "   Data persistence: Docker Volumes" "White"
    Write-ColorText "   Log location: Docker Logs" "White"
    
    Write-ColorText "`nCommon Commands:" "Blue"
    Write-ColorText "   View service status: docker-compose -f docker-compose.secure.yml ps" "White"
    Write-ColorText "   View app logs: docker-compose -f docker-compose.secure.yml logs -f aps-secure" "White"
    Write-ColorText "   Stop services: docker-compose -f docker-compose.secure.yml down" "White"
    Write-ColorText "   Restart services: docker-compose -f docker-compose.secure.yml restart aps-secure" "White"
    
    Write-ColorText "`n" + "="*60 "Blue"
    Write-ColorText "Your intelligent scheduling algorithms now have enterprise-level security protection!" "Green"
}

function Push-ToRegistry {
    if ($Registry) {
        Write-ColorText "Pushing image to private registry..." "Blue"
        
        $imageName = "$Registry/aps-secure:$Version"
        
        try {
            docker push $imageName
            
            if ($LASTEXITCODE -eq 0) {
                Write-ColorText "✅ Image pushed successfully: $imageName" "Green"
            }
            else {
                Write-ColorText "❌ Image push failed" "Red"
            }
        }
        catch {
            Write-ColorText "❌ Push process error: $_" "Red"
        }
    }
}

function Main {
    Write-ColorText "APS Automotive Chip Final Test Intelligent Scheduling Platform - Secure Deployment Tool" "Blue"
    Write-ColorText "Author: Claude AI Assistant" "Blue"
    Write-ColorText "="*60 "Blue"
    
    # Check prerequisites
    if (-not (Test-Prerequisites)) {
        Write-ColorText "❌ Prerequisites check failed, please install Docker and Docker Compose first" "Red"
        exit 1
    }
    
    # Initialize security configuration
    Initialize-SecurityConfig
    
    # Confirm execution
    if (-not $Force -and -not $BuildOnly) {
        Write-ColorText "`n⚠️ About to execute secure deployment, this will:" "Yellow"
        Write-ColorText "   • Compile core algorithms to bytecode" "White"
        Write-ColorText "   • Build multi-stage secure Docker image" "White"
        Write-ColorText "   • Deploy containerized service stack" "White"
        Write-ColorText "   • Configure enterprise-level security policies" "White"
        
        $confirm = Read-Host "`nConfirm to continue secure deployment? (y/N)"
        if ($confirm -ne "y") {
            Write-ColorText "❌ Deployment cancelled" "Yellow"
            exit 0
        }
    }
    
    $success = $true
    
    # Build secure image
    if (-not $NoBuild) {
        if (-not (Build-SecureImage)) {
            $success = $false
        }
        
        # Push to private registry
        if ($success -and $Registry) {
            Push-ToRegistry
        }
    }
    
    # Deploy services (unless build only)
    if ($success -and -not $BuildOnly) {
        if (-not (Deploy-SecureStack)) {
            $success = $false
        }
    }
    
    if ($success) {
        if ($BuildOnly) {
            Write-ColorText "✅ Secure image build completed!" "Green"
        }
        else {
            Show-SecuritySummary
        }
    }
    else {
        Write-ColorText "❌ Secure deployment failed, please check error messages" "Red"
        exit 1
    }
}

# Execute main function
try {
    Main
    Write-ColorText "`nSecure deployment task completed!" "Green"
}
catch {
    Write-ColorText "`nDeployment process interrupted: $_" "Red"
    exit 1
} 