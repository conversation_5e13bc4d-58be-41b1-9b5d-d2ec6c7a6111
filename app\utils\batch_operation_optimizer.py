from typing import List, Dict, Any, Optional, Generator
import logging
from app import db
from sqlalchemy import text
import time
import math

class BatchOperationOptimizer:
    """批量操作优化器"""
    
    def __init__(self, batch_size: int = 1000):
        self.batch_size = batch_size
        self.logger = logging.getLogger(__name__)
    
    def optimized_batch_save(self, data: List[Dict[str, Any]], table_name: str, 
                           insert_sql: str = None) -> bool:
        """优化的批量保存"""
        try:
            start_time = time.time()
            
            if not data:
                self.logger.warning(f"⚠️ 批量保存数据为空: {table_name}")
                return True
            
            # 1. 使用TRUNCATE替代DELETE（更快）
            self._truncate_table(table_name)
            
            # 2. 分批插入
            total_batches = self._calculate_batches(len(data))
            
            for batch_num, batch in enumerate(self._chunk_data(data), 1):
                self.logger.info(f"🔄 处理批次 {batch_num}/{total_batches} ({len(batch)} 条记录)")
                
                # 3. 批量插入
                if insert_sql:
                    self._execute_batch_insert_with_sql(batch, insert_sql)
                else:
                    self._execute_batch_insert(batch, table_name)
            
            execution_time = time.time() - start_time
            self.logger.info(f"✅ 批量保存完成: {len(data)} 条记录, 耗时: {execution_time:.2f}s")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 批量保存失败: {e}")
            raise
    
    def optimized_batch_insert(self, data: List[Dict[str, Any]], insert_sql: str) -> bool:
        """优化的批量插入（不清空表）"""
        try:
            start_time = time.time()
            
            if not data:
                self.logger.warning("⚠️ 批量插入数据为空")
                return True
            
            # 分批插入
            total_batches = self._calculate_batches(len(data))
            
            for batch_num, batch in enumerate(self._chunk_data(data), 1):
                self.logger.debug(f"🔄 插入批次 {batch_num}/{total_batches} ({len(batch)} 条记录)")
                self._execute_batch_insert_with_sql(batch, insert_sql)
            
            execution_time = time.time() - start_time
            self.logger.info(f"✅ 批量插入完成: {len(data)} 条记录, 耗时: {execution_time:.2f}s")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 批量插入失败: {e}")
            raise
    
    def optimized_batch_update(self, data: List[Dict[str, Any]], update_sql: str, 
                              key_field: str = 'id') -> bool:
        """优化的批量更新"""
        try:
            start_time = time.time()
            
            if not data:
                self.logger.warning("⚠️ 批量更新数据为空")
                return True
            
            # 分批更新
            total_batches = self._calculate_batches(len(data))
            updated_count = 0
            
            for batch_num, batch in enumerate(self._chunk_data(data), 1):
                self.logger.debug(f"🔄 更新批次 {batch_num}/{total_batches} ({len(batch)} 条记录)")
                
                # 执行批量更新
                for item in batch:
                    result = db.session.execute(text(update_sql), item)
                    if result.rowcount > 0:
                        updated_count += result.rowcount
            
            execution_time = time.time() - start_time
            self.logger.info(f"✅ 批量更新完成: {updated_count} 条记录更新, 耗时: {execution_time:.2f}s")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 批量更新失败: {e}")
            raise
    
    def optimized_batch_delete(self, ids: List[Any], table_name: str, 
                              key_field: str = 'id') -> bool:
        """优化的批量删除"""
        try:
            start_time = time.time()
            
            if not ids:
                self.logger.warning("⚠️ 批量删除ID列表为空")
                return True
            
            # 分批删除
            total_batches = self._calculate_batches(len(ids))
            deleted_count = 0
            
            for batch_num, batch_ids in enumerate(self._chunk_list(ids), 1):
                self.logger.debug(f"🔄 删除批次 {batch_num}/{total_batches} ({len(batch_ids)} 条记录)")
                
                # 构建IN条件删除SQL
                placeholders = ','.join([':id' + str(i) for i in range(len(batch_ids))])
                delete_sql = f"DELETE FROM {table_name} WHERE {key_field} IN ({placeholders})"
                
                # 构建参数字典
                params = {f'id{i}': batch_ids[i] for i in range(len(batch_ids))}
                
                result = db.session.execute(text(delete_sql), params)
                if result.rowcount > 0:
                    deleted_count += result.rowcount
            
            execution_time = time.time() - start_time
            self.logger.info(f"✅ 批量删除完成: {deleted_count} 条记录删除, 耗时: {execution_time:.2f}s")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 批量删除失败: {e}")
            raise
    
    def _truncate_table(self, table_name: str):
        """截断表（比DELETE更快）"""
        try:
            db.session.execute(text(f"TRUNCATE TABLE {table_name}"))
            self.logger.debug(f"🗑️ 表已截断: {table_name}")
        except Exception as e:
            # 如果TRUNCATE失败，回退到DELETE
            self.logger.warning(f"⚠️ TRUNCATE失败，回退到DELETE: {e}")
            db.session.execute(text(f"DELETE FROM {table_name}"))
            self.logger.debug(f"🗑️ 表已清空: {table_name}")
    
    def _execute_batch_insert(self, batch_data: List[Dict[str, Any]], table_name: str):
        """执行批量插入（自动构建SQL）"""
        if not batch_data:
            return
        
        # 从第一条记录获取字段名
        fields = list(batch_data[0].keys())
        placeholders = ', '.join([f':{field}' for field in fields])
        
        insert_sql = f"""
            INSERT INTO {table_name} ({', '.join(fields)})
            VALUES ({placeholders})
        """
        
        db.session.execute(text(insert_sql), batch_data)
    
    def _execute_batch_insert_with_sql(self, batch_data: List[Dict[str, Any]], insert_sql: str):
        """使用指定SQL执行批量插入"""
        if not batch_data:
            return
        
        db.session.execute(text(insert_sql), batch_data)
    
    def _chunk_data(self, data: List[Dict[str, Any]]) -> Generator[List[Dict[str, Any]], None, None]:
        """将数据分批"""
        for i in range(0, len(data), self.batch_size):
            yield data[i:i + self.batch_size]
    
    def _chunk_list(self, data: List[Any]) -> Generator[List[Any], None, None]:
        """将列表分批"""
        for i in range(0, len(data), self.batch_size):
            yield data[i:i + self.batch_size]
    
    def _calculate_batches(self, total_count: int) -> int:
        """计算批次数"""
        return math.ceil(total_count / self.batch_size)
    
    def set_batch_size(self, batch_size: int):
        """设置批次大小"""
        if batch_size > 0:
            self.batch_size = batch_size
            self.logger.info(f"📊 批次大小已设置为: {batch_size}")
        else:
            raise ValueError("批次大小必须大于0")
    
    def get_optimal_batch_size(self, data_size: int, memory_limit_mb: int = 100) -> int:
        """根据数据大小和内存限制计算最优批次大小"""
        # 简单估算：假设每条记录平均占用1KB内存
        estimated_record_size_kb = 1
        max_records_in_memory = (memory_limit_mb * 1024) // estimated_record_size_kb
        
        # 选择合适的批次大小
        optimal_size = min(max_records_in_memory, data_size, 5000)  # 最大不超过5000
        optimal_size = max(optimal_size, 100)  # 最小100
        
        self.logger.info(f"📊 计算最优批次大小: {optimal_size} (数据量: {data_size}, 内存限制: {memory_limit_mb}MB)")
        return optimal_size
    
    def analyze_performance(self, operation_name: str, data_size: int, 
                          execution_time: float) -> Dict[str, Any]:
        """分析批量操作性能"""
        if execution_time > 0:
            records_per_second = data_size / execution_time
            batches_per_second = self._calculate_batches(data_size) / execution_time
        else:
            records_per_second = 0
            batches_per_second = 0
        
        performance_metrics = {
            'operation': operation_name,
            'data_size': data_size,
            'batch_size': self.batch_size,
            'total_batches': self._calculate_batches(data_size),
            'execution_time': execution_time,
            'records_per_second': records_per_second,
            'batches_per_second': batches_per_second,
            'avg_time_per_batch': execution_time / self._calculate_batches(data_size) if data_size > 0 else 0
        }
        
        self.logger.info(f"📊 性能分析 - {operation_name}: {records_per_second:.1f} 记录/秒, {batches_per_second:.1f} 批次/秒")
        
        return performance_metrics
    
    def optimize_database_settings(self, table_name: str = None):
        """优化数据库设置以提升批量操作性能"""
        try:
            # 临时禁用一些约束以提升性能
            optimizations = [
                "SET SESSION autocommit = 0",
                "SET SESSION unique_checks = 0", 
                "SET SESSION foreign_key_checks = 0",
                "SET SESSION sql_log_bin = 0"
            ]
            
            for sql in optimizations:
                try:
                    db.session.execute(text(sql))
                    self.logger.debug(f"✅ 数据库优化设置: {sql}")
                except Exception as e:
                    self.logger.warning(f"⚠️ 数据库优化设置失败: {sql}, 错误: {e}")
            
            self.logger.info("🚀 数据库批量操作优化设置已启用")
            
        except Exception as e:
            self.logger.error(f"❌ 数据库优化设置失败: {e}")
    
    def restore_database_settings(self):
        """恢复数据库设置"""
        try:
            # 恢复默认设置
            restorations = [
                "SET SESSION autocommit = 1",
                "SET SESSION unique_checks = 1",
                "SET SESSION foreign_key_checks = 1", 
                "SET SESSION sql_log_bin = 1"
            ]
            
            for sql in restorations:
                try:
                    db.session.execute(text(sql))
                    self.logger.debug(f"✅ 数据库设置恢复: {sql}")
                except Exception as e:
                    self.logger.warning(f"⚠️ 数据库设置恢复失败: {sql}, 错误: {e}")
            
            self.logger.info("🔄 数据库设置已恢复默认值")
            
        except Exception as e:
            self.logger.error(f"❌ 数据库设置恢复失败: {e}")


# 全局实例
_batch_optimizer = None

def get_batch_optimizer(batch_size: int = 1000) -> BatchOperationOptimizer:
    """获取批量操作优化器实例"""
    global _batch_optimizer
    if _batch_optimizer is None or _batch_optimizer.batch_size != batch_size:
        _batch_optimizer = BatchOperationOptimizer(batch_size)
    return _batch_optimizer 