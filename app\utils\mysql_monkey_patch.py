#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL猴子补丁 - 修复硬编码localhost问题
通过动态替换pymysql.connect调用来解决硬编码问题
"""

import os
import pymysql
from functools import wraps
from typing import Any, Dict
import logging

logger = logging.getLogger(__name__)

# 保存原始的pymysql.connect函数
_original_pymysql_connect = pymysql.connect

def get_fixed_mysql_config(**kwargs) -> Dict[str, Any]:
    """
    获取修复后的MySQL配置
    将硬编码的localhost替换为环境变量配置
    """
    # 获取环境变量配置
    env_host = (
        os.environ.get('MYSQL_HOST') or 
        os.environ.get('DB_HOST') or 
        'host.docker.internal'
    )
    env_port = int(os.environ.get('MYSQL_PORT', os.environ.get('DB_PORT', 3306)))
    env_user = os.environ.get('MYSQL_USER', os.environ.get('DB_USER', 'root'))
    env_password = os.environ.get('MYSQL_PASSWORD', os.environ.get('DB_PASSWORD', 'WWWwww123!'))
    
    # 如果传入的是localhost，则替换为环境变量配置
    if kwargs.get('host') == 'localhost':
        kwargs['host'] = env_host
        logger.debug(f"🔧 MySQL猴子补丁: localhost -> {env_host}")
    
    # 如果没有指定主机，使用环境变量
    if 'host' not in kwargs:
        kwargs['host'] = env_host
    
    # 设置其他默认值
    kwargs.setdefault('port', env_port)
    kwargs.setdefault('user', env_user)
    kwargs.setdefault('password', env_password)
    kwargs.setdefault('charset', 'utf8mb4')
    
    return kwargs

def patched_pymysql_connect(*args, **kwargs):
    """
    修复后的pymysql.connect函数
    自动替换localhost为正确的主机地址
    """
    try:
        # 修复配置
        fixed_kwargs = get_fixed_mysql_config(**kwargs)
        
        # 调用原始函数
        return _original_pymysql_connect(*args, **fixed_kwargs)
        
    except Exception as e:
        # 如果连接失败，记录详细信息
        host = kwargs.get('host', 'unknown')
        database = kwargs.get('database', 'unknown')
        logger.warning(f"MySQL连接失败 ({host}/{database}): {e}")
        raise

def apply_mysql_monkey_patch():
    """
    应用MySQL猴子补丁
    将pymysql.connect替换为修复版本
    """
    # 替换pymysql模块中的connect函数
    pymysql.connect = patched_pymysql_connect
    
    # 同时替换Connection类的connect方法（如果存在）
    if hasattr(pymysql, 'Connection'):
        original_init = pymysql.Connection.__init__
        
        @wraps(original_init)
        def patched_init(self, *args, **kwargs):
            fixed_kwargs = get_fixed_mysql_config(**kwargs)
            return original_init(self, *args, **fixed_kwargs)
        
        pymysql.Connection.__init__ = patched_init
    
    logger.info("✅ MySQL猴子补丁已应用 - 硬编码localhost问题已修复")

def remove_mysql_monkey_patch():
    """
    移除MySQL猴子补丁
    恢复原始的pymysql.connect函数
    """
    pymysql.connect = _original_pymysql_connect
    logger.info("🔄 MySQL猴子补丁已移除")

def get_current_mysql_config() -> Dict[str, str]:
    """
    获取当前MySQL配置信息
    """
    return {
        'host': os.environ.get('MYSQL_HOST', os.environ.get('DB_HOST', 'host.docker.internal')),
        'port': os.environ.get('MYSQL_PORT', os.environ.get('DB_PORT', '3306')),
        'user': os.environ.get('MYSQL_USER', os.environ.get('DB_USER', 'root')),
        'password': '***' if os.environ.get('MYSQL_PASSWORD') else 'default',
        'charset': os.environ.get('MYSQL_CHARSET', 'utf8mb4')
    }

# 自动应用补丁（可选）
def auto_patch_on_import():
    """
    导入时自动应用补丁
    """
    try:
        apply_mysql_monkey_patch()
        config = get_current_mysql_config()
        logger.info(f"🔧 MySQL自动补丁已启用: {config['host']}:{config['port']}")
    except Exception as e:
        logger.error(f"❌ MySQL自动补丁失败: {e}")

# 如果环境变量MYSQL_AUTO_PATCH=1，则自动应用补丁
if os.environ.get('MYSQL_AUTO_PATCH', '0') == '1':
    auto_patch_on_import() 