<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮状态调试测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>定时任务按钮状态调试</h2>
        
        <div class="card">
            <div class="card-header">
                <h5>测试任务</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>任务名称：<span id="taskName">加载中...</span></h6>
                        <p>任务ID: <span id="taskId">3</span></p>
                        <p>当前状态：<span id="taskStatus" class="badge bg-secondary">未知</span></p>
                    </div>
                    <div class="col-md-6">
                        <div id="actionButtons">
                            <!-- 按钮将通过JavaScript生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5>调试日志</h5>
            </div>
            <div class="card-body">
                <div id="debugLog" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                    <!-- 日志将在这里显示 -->
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <button class="btn btn-primary" onclick="refreshTaskStatus()">刷新任务状态</button>
            <button class="btn btn-secondary" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentTaskId = '3';
        
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }
        
        // 显示通知
        function showNotification(title, message, type = 'info') {
            log(`📢 通知: ${title} - ${message} (${type})`);
        }
        
        // 更新任务状态显示
        function updateTaskStatusDisplay(status) {
            const statusElement = document.getElementById('taskStatus');
            const statusBadges = {
                'active': '<span class="badge bg-success">运行中</span>',
                'paused': '<span class="badge bg-warning">已暂停</span>',
                'completed': '<span class="badge bg-info">已完成</span>',
                'failed': '<span class="badge bg-danger">失败</span>'
            };
            
            statusElement.innerHTML = statusBadges[status] || `<span class="badge bg-secondary">${status}</span>`;
            
            // 更新按钮
            updateActionButtons(status);
        }
        
        // 更新操作按钮
        function updateActionButtons(status) {
            const buttonsDiv = document.getElementById('actionButtons');
            let html = '';
            
            log(`🔧 更新按钮，任务状态: ${status}`);
            
            if (status === 'active') {
                html = `
                    <button class="btn btn-warning me-2" onclick="pauseTask('${currentTaskId}')" data-task-id="${currentTaskId}">
                        <i class="fas fa-pause"></i> 暂停
                    </button>
                `;
                log(`🔧 显示暂停按钮`);
            } else if (status === 'paused') {
                html = `
                    <button class="btn btn-success me-2" onclick="resumeTask('${currentTaskId}')" data-task-id="${currentTaskId}">
                        <i class="fas fa-play"></i> 恢复
                    </button>
                `;
                log(`🔧 显示恢复按钮`);
            }
            
            html += `
                <button class="btn btn-danger" onclick="deleteTask('${currentTaskId}')" data-task-id="${currentTaskId}">
                    <i class="fas fa-trash"></i> 删除
                </button>
            `;
            
            buttonsDiv.innerHTML = html;
        }
        
        // 暂停任务
        async function pauseTask(taskId) {
            try {
                log(`🔧 开始暂停任务 ${taskId}`);
                
                const button = event.target.closest('button');
                if (button) {
                    button.disabled = true;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 暂停中...';
                    log(`🔧 按钮状态设置为暂停中...`);
                }
                
                const response = await fetch(`/api/v2/system/scheduled-tasks/${taskId}/pause`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                log(`🔧 暂停任务响应: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    showNotification('任务已暂停', result.message, 'success');
                    log(`🔧 暂停成功，开始刷新状态...`);
                    await refreshTaskStatus();
                } else {
                    showNotification('暂停失败', result.message, 'error');
                    if (button) {
                        button.disabled = false;
                        button.innerHTML = '<i class="fas fa-pause"></i> 暂停';
                    }
                }
            } catch (error) {
                log(`❌ 暂停任务失败: ${error.message}`);
                showNotification('暂停失败', '网络错误，请稍后重试', 'error');
            }
        }
        
        // 恢复任务
        async function resumeTask(taskId) {
            try {
                log(`🔧 开始恢复任务 ${taskId}`);
                
                const button = event.target.closest('button');
                if (button) {
                    button.disabled = true;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 恢复中...';
                    log(`🔧 按钮状态设置为恢复中...`);
                }
                
                const response = await fetch(`/api/v2/system/scheduled-tasks/${taskId}/resume`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                log(`🔧 恢复任务响应: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    showNotification('任务已恢复', result.message, 'success');
                    log(`🔧 恢复成功，开始刷新状态...`);
                    await refreshTaskStatus();
                } else {
                    showNotification('恢复失败', result.message, 'error');
                    if (button) {
                        button.disabled = false;
                        button.innerHTML = '<i class="fas fa-play"></i> 恢复';
                    }
                }
            } catch (error) {
                log(`❌ 恢复任务失败: ${error.message}`);
                showNotification('恢复失败', '网络错误，请稍后重试', 'error');
            }
        }
        
        // 删除任务
        async function deleteTask(taskId) {
            if (!confirm('确定要删除这个任务吗？')) {
                return;
            }
            
            try {
                log(`🔧 开始删除任务 ${taskId}`);
                
                const response = await fetch(`/api/v2/system/scheduled-tasks/${taskId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                log(`🔧 删除任务响应: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    showNotification('任务已删除', result.message, 'success');
                    updateTaskStatusDisplay('deleted');
                } else {
                    showNotification('删除失败', result.message, 'error');
                }
            } catch (error) {
                log(`❌ 删除任务失败: ${error.message}`);
                showNotification('删除失败', '网络错误，请稍后重试', 'error');
            }
        }
        
        // 刷新任务状态
        async function refreshTaskStatus() {
            try {
                log(`🔄 刷新任务状态...`);
                
                const timestamp = new Date().getTime();
                const response = await fetch(`/api/v2/system/scheduled-tasks?_t=${timestamp}`, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });
                
                const result = await response.json();
                log(`🔄 获取任务列表响应: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    const tasks = result.tasks || [];
                    const currentTask = tasks.find(t => t.id == currentTaskId);
                    
                    if (currentTask) {
                        log(`📋 找到任务 ${currentTaskId}，名称: ${currentTask.name}，状态: ${currentTask.status}`);

                        // 更新任务名称显示
                        document.getElementById('taskName').textContent = currentTask.name || '未知任务';

                        updateTaskStatusDisplay(currentTask.status);
                    } else {
                        log(`❌ 未找到任务 ${currentTaskId}`);
                        document.getElementById('taskName').textContent = '任务不存在';
                        updateTaskStatusDisplay('not_found');
                    }
                } else {
                    log(`❌ 获取任务列表失败: ${result.message}`);
                }
            } catch (error) {
                log(`❌ 刷新状态失败: ${error.message}`);
            }
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 页面加载完成，开始初始化...');
            refreshTaskStatus();
        });
    </script>
</body>
</html>
