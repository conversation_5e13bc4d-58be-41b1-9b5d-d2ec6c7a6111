#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理工具路由
提供数据库初始化和维护功能
"""

from flask import Blueprint, render_template, jsonify, request
from flask_login import login_required, current_user
from app.decorators import admin_required
from app.utils.init_unified_management import initialize_unified_management
import logging

logger = logging.getLogger(__name__)

# 创建蓝图
admin_tools_bp = Blueprint('admin_tools', __name__, url_prefix='/admin/tools')

@admin_tools_bp.route('/database-init')
@login_required
@admin_required
def database_init_page():
    """数据库初始化页面"""
    return render_template('admin/database_init.html')

@admin_tools_bp.route('/init-unified-management', methods=['POST'])
@login_required
@admin_required
def init_unified_management():
    """初始化统一批次管理数据库结构"""
    try:
        logger.info(f"用户 {current_user.username} 开始初始化统一批次管理数据库结构")
        
        # 执行初始化
        success = initialize_unified_management()
        
        if success:
            logger.info("统一批次管理数据库结构初始化成功")
            return jsonify({
                'success': True,
                'message': '统一批次管理数据库结构初始化成功',
                'details': [
                    '✅ et_wait_lot_extension扩展表已创建',
                    '✅ lotprioritydone表结构已扩展',
                    '✅ v_unified_lot_management视图已创建',
                    '✅ 现在可以使用统一批次管理功能'
                ]
            })
        else:
            logger.error("统一批次管理数据库结构初始化失败")
            return jsonify({
                'success': False,
                'message': '统一批次管理数据库结构初始化失败',
                'details': ['请检查日志获取详细错误信息']
            }), 500
            
    except Exception as e:
        logger.error(f"初始化统一批次管理数据库结构时发生错误: {e}")
        return jsonify({
            'success': False,
            'message': f'初始化失败: {str(e)}',
            'details': ['请联系技术支持']
        }), 500

@admin_tools_bp.route('/check-unified-management-status', methods=['GET'])
@login_required
def check_unified_management_status():
    """检查统一批次管理功能状态"""
    try:
        from sqlalchemy import text
        from app import db
        
        # 检查必要的表和视图是否存在
        checks = {
            'et_wait_lot_extension': False,
            'v_unified_lot_management': False,
            'lotprioritydone_extended': False
        }
        
        # 检查扩展表
        result = db.session.execute(text("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'et_wait_lot_extension'
        """))
        checks['et_wait_lot_extension'] = result.scalar() > 0
        
        # 检查视图
        result = db.session.execute(text("""
            SELECT COUNT(*) FROM information_schema.views 
            WHERE table_schema = DATABASE() AND table_name = 'v_unified_lot_management'
        """))
        checks['v_unified_lot_management'] = result.scalar() > 0
        
        # 检查lotprioritydone表是否有扩展字段
        result = db.session.execute(text("""
            SELECT COUNT(*) FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'lotprioritydone' 
            AND COLUMN_NAME = 'SCHEDULING_STATUS'
        """))
        checks['lotprioritydone_extended'] = result.scalar() > 0
        
        # 判断整体状态
        all_ready = all(checks.values())
        
        return jsonify({
            'success': True,
            'ready': all_ready,
            'checks': checks,
            'message': '统一批次管理功能已就绪' if all_ready else '统一批次管理功能未就绪'
        })
        
    except Exception as e:
        logger.error(f"检查统一批次管理状态时发生错误: {e}")
        return jsonify({
            'success': False,
            'ready': False,
            'message': f'状态检查失败: {str(e)}'
        }), 500
