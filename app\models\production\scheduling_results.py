from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

@dataclass
class SchedulingResultModel:
    """统一的排产结果数据模型"""
    
    # 基础字段 (19个现有字段)
    id: Optional[int] = None
    PRIORITY: int = 1
    HANDLER_ID: str = ""
    LOT_ID: str = ""
    LOT_TYPE: str = ""
    GOOD_QTY: int = 0
    PROD_ID: str = ""
    DEVICE: str = ""
    CHIP_ID: str = ""
    PKG_PN: str = ""
    PO_ID: str = ""
    STAGE: str = ""
    WIP_STATE: str = "QUEUE"
    PROC_STATE: str = "WAIT"
    HOLD_STATE: str = "N"
    FLOW_ID: str = ""
    FLOW_VER: str = ""
    RELEASE_TIME: Optional[datetime] = None
    FAC_ID: str = "FAC1"
    CREATE_TIME: Optional[datetime] = None
    
    # 扩展字段 (19个新增字段)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    match_type: str = ""
    comprehensive_score: float = 0.0
    processing_time: float = 0.0
    changeover_time: float = 0.0
    algorithm_version: str = ""
    priority_score: float = 0.0
    estimated_hours: float = 0.0
    equipment_status: str = ""
    FAMILY: str = ""
    SCHEDULING_STATUS: str = "SUCCESS"
    FAILURE_REASON: Optional[str] = None
    FAILURE_DETAILS: Optional[Dict] = None
    SESSION_ID: str = ""
    RESCUE_TIME: Optional[datetime] = None
    RESCUE_USER: Optional[str] = None
    STATUS_CHANGE_LOG: Optional[Dict] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {}
        for field in self.__dataclass_fields__.values():
            value = getattr(self, field.name)
            if isinstance(value, datetime):
                result[field.name] = value.isoformat() if value else None
            elif isinstance(value, dict) and value:
                result[field.name] = value
            else:
                result[field.name] = value
        return result
    
    @classmethod
    def from_algorithm_result(cls, lot_data: Dict[str, Any]) -> 'SchedulingResultModel':
        """从算法结果创建模型实例"""
        return cls(
            # 基础字段映射
            PRIORITY=cls._safe_int(lot_data.get('PRIORITY', 1)),
            HANDLER_ID=lot_data.get('HANDLER_ID', ''),
            LOT_ID=lot_data.get('LOT_ID', ''),
            LOT_TYPE=lot_data.get('LOT_TYPE', ''),
            GOOD_QTY=cls._safe_int(lot_data.get('GOOD_QTY', 0)),
            PROD_ID=lot_data.get('PROD_ID', ''),
            DEVICE=lot_data.get('DEVICE', ''),
            CHIP_ID=lot_data.get('CHIP_ID', ''),
            PKG_PN=lot_data.get('PKG_PN', ''),
            PO_ID=lot_data.get('PO_ID', ''),
            STAGE=lot_data.get('STAGE', ''),
            WIP_STATE=lot_data.get('WIP_STATE', 'QUEUE'),
            PROC_STATE=lot_data.get('PROC_STATE', 'WAIT'),
            HOLD_STATE=lot_data.get('HOLD_STATE', 'N'),
            FLOW_ID=lot_data.get('FLOW_ID', ''),
            FLOW_VER=lot_data.get('FLOW_VER', ''),
            RELEASE_TIME=cls._safe_datetime(lot_data.get('RELEASE_TIME')),
            FAC_ID=lot_data.get('FAC_ID', 'FAC1'),
            CREATE_TIME=cls._safe_datetime(lot_data.get('CREATE_TIME')),
            
            # 扩展字段映射（关键修复）
            created_at=datetime.now(),
            updated_at=datetime.now(),
            match_type=lot_data.get('match_type', ''),
            comprehensive_score=cls._safe_float(lot_data.get('comprehensive_score', 0.0)),
            processing_time=cls._safe_float(lot_data.get('processing_time', 0.0)),
            changeover_time=cls._safe_float(lot_data.get('changeover_time', 0.0)),
            algorithm_version=lot_data.get('algorithm_version', ''),
            priority_score=cls._safe_float(lot_data.get('priority_score', 0.0)),
            estimated_hours=cls._safe_float(lot_data.get('estimated_hours', 0.0)),
            equipment_status=lot_data.get('equipment_status', ''),
            FAMILY=lot_data.get('FAMILY', ''),
            SCHEDULING_STATUS='SUCCESS',
            SESSION_ID=lot_data.get('SESSION_ID', ''),
        )
    
    @staticmethod
    def _safe_int(value: Any) -> int:
        """安全的整数转换"""
        try:
            return int(value) if value is not None else 0
        except (ValueError, TypeError):
            return 0
    
    @staticmethod
    def _safe_float(value: Any) -> float:
        """安全的浮点数转换"""
        try:
            return float(value) if value is not None else 0.0
        except (ValueError, TypeError):
            return 0.0
    
    @staticmethod
    def _safe_datetime(value: Any) -> Optional[datetime]:
        """安全的日期时间转换"""
        if value is None:
            return None
        if isinstance(value, datetime):
            return value
        try:
            return datetime.fromisoformat(str(value))
        except:
            return None
    
    def validate(self) -> List[str]:
        """验证数据完整性"""
        errors = []
        
        # 必填字段验证
        required_fields = ['LOT_ID', 'GOOD_QTY', 'PROD_ID']
        for field in required_fields:
            if not getattr(self, field):
                errors.append(f"必填字段 {field} 不能为空")
        
        # 数据类型验证
        if self.GOOD_QTY < 0:
            errors.append("GOOD_QTY 不能为负数")
        
        if self.PRIORITY < 1:
            errors.append("PRIORITY 必须大于0")
        
        return errors


class SchedulingResultPersister:
    """排产结果持久化管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def save_results(self, results: List[SchedulingResultModel]) -> bool:
        """保存排产结果到数据库"""
        from app import db
        from sqlalchemy import text
        
        try:
            # 1. 数据验证
            validation_errors = []
            for i, result in enumerate(results):
                errors = result.validate()
                if errors:
                    validation_errors.extend([f"记录{i+1}: {error}" for error in errors])
            
            if validation_errors:
                raise ValueError(f"数据验证失败: {'; '.join(validation_errors)}")
            
            # 2. 清空表
            db.session.execute(text("DELETE FROM lotprioritydone"))
            
            # 3. 批量插入
            if results:
                insert_sql = self._get_complete_insert_sql()
                insert_data = [result.to_dict() for result in results]
                
                db.session.execute(text(insert_sql), insert_data)
            
            # 4. 验证插入结果
            count = db.session.execute(text("SELECT COUNT(*) FROM lotprioritydone")).scalar()
            if count != len(results):
                raise Exception(f"数据插入验证失败: 期望{len(results)}条, 实际{count}条")
            
            self.logger.info(f"✅ 排产结果保存成功: {len(results)}条记录")
            return True
                
        except Exception as e:
            self.logger.error(f"❌ 排产结果保存失败: {e}")
            raise
    
    def _get_complete_insert_sql(self) -> str:
        """获取完整的插入SQL"""
        return """
            INSERT INTO lotprioritydone (
                PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, DEVICE, CHIP_ID, PKG_PN, 
                PO_ID, STAGE, WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                RELEASE_TIME, FAC_ID, CREATE_TIME, created_at, updated_at,
                match_type, comprehensive_score, processing_time, changeover_time,
                algorithm_version, priority_score, estimated_hours, equipment_status,
                FAMILY, SCHEDULING_STATUS, FAILURE_REASON, FAILURE_DETAILS,
                SESSION_ID, RESCUE_TIME, RESCUE_USER, STATUS_CHANGE_LOG
            ) VALUES (
                :PRIORITY, :HANDLER_ID, :LOT_ID, :LOT_TYPE, :GOOD_QTY, :PROD_ID, :DEVICE, :CHIP_ID, :PKG_PN,
                :PO_ID, :STAGE, :WIP_STATE, :PROC_STATE, :HOLD_STATE, :FLOW_ID, :FLOW_VER,
                :RELEASE_TIME, :FAC_ID, :CREATE_TIME, :created_at, :updated_at,
                :match_type, :comprehensive_score, :processing_time, :changeover_time,
                :algorithm_version, :priority_score, :estimated_hours, :equipment_status,
                :FAMILY, :SCHEDULING_STATUS, :FAILURE_REASON, :FAILURE_DETAILS,
                :SESSION_ID, :RESCUE_TIME, :RESCUE_USER, :STATUS_CHANGE_LOG
            )
        """ 