import requests
import json

def test_history_api():
    base_url = "http://127.0.0.1:5000"
    
    # 创建会话
    session = requests.Session()
    
    try:
        print("🔍 测试定时调度历史API...")
        
        # 1. 登录
        print("📝 正在登录...")
        login_data = {
            'username': 'admin',
            'password': 'admin'
        }
        login_response = session.post(f"{base_url}/auth/login", data=login_data)
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return
        
        print("✅ 登录成功")
        
        # 2. 测试获取定时调度历史
        print("\n📋 测试获取定时调度历史...")
        history_response = session.get(f"{base_url}/api/v2/system/scheduled-tasks/history")
        
        print(f"状态码: {history_response.status_code}")
        print(f"响应头: {dict(history_response.headers)}")
        
        if history_response.status_code == 200:
            history_result = history_response.json()
            print("✅ 成功获取定时调度历史")
            print(f"响应数据: {json.dumps(history_result, indent=2, ensure_ascii=False)}")
            
            history_list = history_result.get('history', [])
            print(f"📊 找到 {len(history_list)} 条定时调度历史记录")
            
            for i, record in enumerate(history_list[:3]):  # 只显示前3条
                print(f"  {i+1}. 任务: {record.get('taskName')} - 状态: {record.get('status')} - 时间: {record.get('executionTime')}")
        else:
            print(f"❌ 获取历史失败: {history_response.status_code}")
            print(f"响应内容: {history_response.text}")
        
        # 3. 测试带参数的历史查询
        print(f"\n📋 测试带参数的历史查询...")
        params_response = session.get(f"{base_url}/api/v2/system/scheduled-tasks/history?per_page=5&task_id=3")
        
        print(f"状态码: {params_response.status_code}")
        if params_response.status_code == 200:
            params_result = params_response.json()
            print("✅ 成功获取带参数的历史")
            print(f"分页信息: {params_result.get('pagination', {})}")
            
            history_list = params_result.get('history', [])
            print(f"📊 找到 {len(history_list)} 条特定任务的历史记录")
        else:
            print(f"❌ 获取带参数历史失败: {params_response.status_code}")
            print(f"响应内容: {params_response.text}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_history_api()
