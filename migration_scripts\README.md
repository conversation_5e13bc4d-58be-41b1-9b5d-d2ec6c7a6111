# 统一排产数据架构迁移指南

## 📖 概述

本迁移脚本将实现统一排产数据架构，解决当前手动调整排产结果无法正常保存的问题。

### 🎯 迁移目标

- **统一数据存储**：将`scheduling_failed_lots`表数据迁移到扩展的`lotprioritydone`表
- **增强调整能力**：所有201个批次（173成功+28失败）均支持手动调整
- **简化架构**：从双表分离架构改为单表统一架构
- **向后兼容**：保持现有功能不受影响

## 📁 文件结构

```
migration_scripts/
├── unified_scheduling_migration.py    # 主迁移脚本
├── README.md                         # 使用说明（本文件）
└── test_migration_script.py          # 迁移测试脚本
```

## 🚀 使用步骤

### 1. 迁移前测试（必须）

```bash
# 运行迁移测试，确保环境准备就绪
python test_migration_script.py
```

测试内容：
- ✅ 数据库连接验证
- ✅ 表结构完整性检查
- ✅ 数据质量分析
- ✅ 字段映射验证
- ✅ 迁移风险评估

### 2. 执行迁移

```bash
# 执行实际迁移（需要确认）
python migration_scripts/unified_scheduling_migration.py
```

迁移流程：
1. 🔍 **预检查**：验证环境和数据完整性
2. 💾 **备份**：创建原始数据备份表
3. 🔧 **扩展**：为`lotprioritydone`表添加新字段
4. 📦 **迁移**：转移失败批次数据到统一表
5. ✅ **验证**：数据完整性检查
6. 📝 **记录**：生成迁移报告

### 3. 迁移后验证

```bash
# 检查迁移结果
mysql -u root -p -e "
SELECT 
    SCHEDULING_STATUS,
    COUNT(*) as count 
FROM aps.lotprioritydone 
GROUP BY SCHEDULING_STATUS;
"
```

## 🗄️ 数据模型变更

### 扩展字段

| 字段名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `SCHEDULING_STATUS` | ENUM('SUCCESS', 'FAILED', 'MANUAL_ADJUSTED') | 'SUCCESS' | 排产状态 |
| `FAILURE_REASON` | VARCHAR(500) | NULL | 失败原因 |
| `FAILURE_DETAILS` | TEXT | NULL | 失败详情 |
| `SESSION_ID` | VARCHAR(50) | NULL | 排产会话ID |

### 状态说明

- **SUCCESS**：排产成功批次（原有数据）
- **FAILED**：排产失败批次（从`scheduling_failed_lots`迁移）
- **MANUAL_ADJUSTED**：手动调整批次（用户操作后状态）

## 🔄 迁移数据映射

### 成功批次（无变更）
```sql
-- 原有数据保持不变，添加默认状态
UPDATE lotprioritydone SET SCHEDULING_STATUS = 'SUCCESS' WHERE SCHEDULING_STATUS IS NULL;
```

### 失败批次（迁移映射）
```sql
-- 从scheduling_failed_lots表迁移数据
INSERT INTO lotprioritydone (
    LOT_ID, DEVICE, STAGE, GOOD_QTY, ...
    SCHEDULING_STATUS, FAILURE_REASON, ...
) 
SELECT 
    sfl.lot_id, sfl.device, sfl.stage, sfl.good_qty, ...
    'FAILED', sfl.failure_reason, ...
FROM scheduling_failed_lots sfl
LEFT JOIN ET_WAIT_LOT ewl ON sfl.lot_id = ewl.LOT_ID;
```

## ⚠️ 安全机制

### 1. 自动备份
- 迁移前自动创建备份表：`lotprioritydone_backup_YYYYMMDD_HHMMSS`
- 如果存在失败表，也创建：`scheduling_failed_lots_backup_YYYYMMDD_HHMMSS`

### 2. 回滚机制
- 迁移失败时自动回滚
- 删除新添加的字段和索引
- 清理已迁移的数据

### 3. 完整性验证
- 数据总量一致性检查
- 关键字段非空验证
- 状态分布合理性检查

## 📊 预期效果

| 项目 | 迁移前 | 迁移后 | 改进 |
|------|--------|--------|------|
| 可调整批次 | 173个 | 201个 | +28个 (+16%) |
| 数据架构 | 双表分离 | 单表统一 | 简化架构 |
| 失败救援 | ❌ 不支持 | ✅ 支持 | 新增功能 |
| 前端复杂度 | 150行合并代码 | 单表查询 | -80%代码量 |

## 🚨 注意事项

### 执行前准备
1. **备份数据库**：虽然脚本有自动备份，建议手动全量备份
2. **停止服务**：建议在业务低峰期停止相关服务
3. **磁盘空间**：确保足够空间存储备份表
4. **权限确认**：确保数据库用户有ALTER TABLE权限

### 执行时监控
1. **查看日志**：迁移过程会生成详细日志文件
2. **监控进度**：大数据量时可能需要较长时间
3. **资源监控**：注意数据库CPU和内存使用情况

### 执行后验证
1. **数据完整性**：验证总记录数和状态分布
2. **功能测试**：测试手动调整功能
3. **性能测试**：验证查询性能是否正常

## 🔧 故障排除

### 常见问题

1. **字段已存在错误**
   ```
   ERROR: Duplicate column name 'SCHEDULING_STATUS'
   ```
   **解决**：脚本会自动跳过已存在的字段，这是正常情况

2. **权限不足错误**
   ```
   ERROR: Access denied for user 'xxx'@'localhost'
   ```
   **解决**：确保数据库用户有ALTER和INSERT权限

3. **磁盘空间不足**
   ```
   ERROR: No space left on device
   ```
   **解决**：清理磁盘空间或迁移到更大磁盘

### 回滚操作

如果迁移失败且自动回滚不成功：

```sql
-- 手动删除新添加的字段
ALTER TABLE lotprioritydone DROP COLUMN SCHEDULING_STATUS;
ALTER TABLE lotprioritydone DROP COLUMN FAILURE_REASON;
ALTER TABLE lotprioritydone DROP COLUMN FAILURE_DETAILS;
ALTER TABLE lotprioritydone DROP COLUMN SESSION_ID;

-- 删除新添加的索引
ALTER TABLE lotprioritydone DROP INDEX idx_scheduling_status;
ALTER TABLE lotprioritydone DROP INDEX idx_session_id;

-- 恢复原始数据（如果有备份表）
DROP TABLE lotprioritydone;
RENAME TABLE lotprioritydone_backup_YYYYMMDD_HHMMSS TO lotprioritydone;
```

## 📞 支持

如遇到问题，请：
1. 查看生成的日志文件：`migration_log_YYYYMMDD_HHMMSS.log`
2. 查看迁移记录文件：`migration_record_YYYYMMDD_HHMMSS.json`
3. 联系技术支持并提供详细错误信息

---

*版本：v1.0 | 创建时间：2025-01-14 | 负责人：AI Assistant* 