# 🔒 APS车规芯片终测智能调度平台 - 安全Docker Compose配置
# 企业级安全配置，防止源代码泄露
# 作者: Claude AI Assistant

networks:
  aps-internal:
    driver: bridge
    internal: false  # 允许外网访问Web服务
    ipam:
      config:
        - subnet: **********/16
  
  aps-backend:
    driver: bridge
    internal: true   # 🔒 后端服务内网隔离
    ipam:
      config:
        - subnet: **********/16

volumes:
  aps-db-data:
    driver: local
  aps-redis-data:
    driver: local
  aps-logs:
    driver: local
  aps-uploads:
    driver: local
  aps-downloads:
    driver: local

services:
  # ============================================
  # APS主应用服务 (安全容器)
  # ============================================
  aps-secure:
    build:
      context: .
      dockerfile: Dockerfile.secure.simplified
      args:
        BUILD_DATE: ${BUILD_DATE:-}
        VERSION: ${VERSION:-1.0.0}
        COMMIT_SHA: ${COMMIT_SHA:-}
    
    image: aps-secure:${VERSION:-latest}
    container_name: aps-secure-app
    hostname: aps-app
    
    # 🔒 安全配置
    read_only: true        # 只读文件系统
    tmpfs:
      - /tmp:noexec,nosuid,nodev,size=100m
      - /var/tmp:noexec,nosuid,nodev,size=50m
    
    security_opt:
      - no-new-privileges:true    # 🔒 禁止容器内提权
      - apparmor:unconfined      # 如需要可启用AppArmor
    
    cap_drop:
      - ALL                      # 🔒 移除所有Linux能力
    cap_add:
      - NET_BIND_SERVICE         # 🔒 只添加必要的端口绑定能力
    
    # 🔒 非root用户运行
    user: "1000:1000"
    
    # 🔒 资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # 🔒 环境变量 (敏感信息通过secrets管理)
    environment:
      - FLASK_ENV=production
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONHASHSEED=random
      - DATABASE_URL=mysql+pymysql://aps_user:${MYSQL_PASSWORD}@mysql:3306/aps
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - ALGORITHM_PROTECTION_LEVEL=HIGH
    
    # 🔒 端口映射
    ports:
      - "5000:5000"
    
    # 🔒 网络配置
    networks:
      - aps-internal
      - aps-backend
    
    # 🔒 卷挂载 (最小权限)
    volumes:
      - aps-logs:/app/logs:rw
      - aps-uploads:/app/uploads:rw
      - aps-downloads:/app/downloads:rw
      - type: tmpfs
        target: /app/instance
        tmpfs:
          size: 100M
    
    # 🔒 依赖服务
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    
    # 🔒 健康检查
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:5000')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 🔒 重启策略
    restart: unless-stopped
    
    # 🔒 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ============================================
  # MySQL数据库服务
  # ============================================
  mysql:
    image: mysql:8.0
    container_name: aps-mysql
    hostname: mysql
    
    # 🔒 安全配置
    security_opt:
      - no-new-privileges:true
    
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - DAC_OVERRIDE
      - SETGID
      - SETUID
    
    # 🔒 环境变量
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=aps
      - MYSQL_USER=aps_user
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_CHARSET=utf8mb4
      - MYSQL_COLLATION=utf8mb4_unicode_ci
    
    # 🔒 网络配置 (仅后端访问)
    networks:
      - aps-backend
    
    # 🔒 卷挂载
    volumes:
      - aps-db-data:/var/lib/mysql
      - ./init_db.sql:/docker-entrypoint-initdb.d/init_db.sql:ro
    
    # 🔒 健康检查
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    
    # 🔒 重启策略
    restart: unless-stopped
    
    # 🔒 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ============================================
  # Redis缓存服务
  # ============================================
  redis:
    image: redis:7-alpine
    container_name: aps-redis
    hostname: redis
    
    # 🔒 安全配置
    security_opt:
      - no-new-privileges:true
    
    cap_drop:
      - ALL
    cap_add:
      - SETGID
      - SETUID
    
    # 🔒 Redis配置
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --appendonly yes
      --appendfsync everysec
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    
    # 🔒 网络配置 (仅后端访问)
    networks:
      - aps-backend
    
    # 🔒 卷挂载
    volumes:
      - aps-redis-data:/data
    
    # 🔒 健康检查
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    # 🔒 重启策略
    restart: unless-stopped
    
    # 🔒 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

  # ============================================
  # Nginx反向代理 (可选)
  # ============================================
  nginx:
    image: nginx:alpine
    container_name: aps-nginx
    hostname: nginx
    
    # 🔒 安全配置
    security_opt:
      - no-new-privileges:true
    
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - DAC_OVERRIDE
      - NET_BIND_SERVICE
      - SETGID
      - SETUID
    
    # 🔒 端口映射
    ports:
      - "80:80"
      - "443:443"
    
    # 🔒 网络配置
    networks:
      - aps-internal
    
    # 🔒 配置文件
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    
    # 🔒 依赖服务
    depends_on:
      - aps-secure
    
    # 🔒 重启策略
    restart: unless-stopped
    
    # 🔒 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

# ============================================
# Secrets管理 (生产环境使用)
# ============================================
secrets:
  mysql_root_password:
    file: ./secrets/mysql_root_password.txt
  mysql_password:
    file: ./secrets/mysql_password.txt
  secret_key:
    file: ./secrets/secret_key.txt
  jwt_secret:
    file: ./secrets/jwt_secret.txt
  redis_password:
    file: ./secrets/redis_password.txt

# ============================================
# 使用说明
# ============================================
# 1. 创建环境变量文件 .env:
#    MYSQL_ROOT_PASSWORD=your_secure_root_password
#    MYSQL_PASSWORD=your_secure_user_password
#    SECRET_KEY=your_secret_key
#    JWT_SECRET=your_jwt_secret
#    REDIS_PASSWORD=your_redis_password
#    BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
#    VERSION=1.0.0
#    COMMIT_SHA=$(git rev-parse HEAD)
#
# 2. 构建和启动:
#    docker-compose -f docker-compose.secure.yml up --build -d
#
# 3. 查看日志:
#    docker-compose -f docker-compose.secure.yml logs -f aps-secure
#
# 4. 停止服务:
#    docker-compose -f docker-compose.secure.yml down
#
# 🔒 安全特性:
# ✅ 多层网络隔离
# ✅ 只读文件系统
# ✅ 非root用户运行
# ✅ 最小权限原则
# ✅ 资源限制
# ✅ 健康检查
# ✅ 日志轮转
# ✅ Secrets管理 