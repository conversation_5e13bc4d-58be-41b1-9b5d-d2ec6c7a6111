# 统一批次优先级管理架构设计 v1.1（修正版）

## 📋 问题分析

### 🔍 当前痛点

1. **数据流程分散**：
   - 等待排产批次：`et_wait_lot` 表
   - 已排产批次：`lotprioritydone` 表（SUCCESS状态）
   - 排产失败批次：`lotprioritydone` 表（FAILED状态）  
   - 手动调整批次：`lotprioritydone` 表（MANUAL_ADJUSTED状态）

2. **API接口分散**：
   - 不同状态的批次需要调用不同的API
   - 缺乏统一的跨状态优先级管理API
   - 前端需要调用多个API才能完成完整的优先级管理

3. **前端界面分散**：
   - 等待批次、成功批次、失败批次在不同页面管理
   - 缺乏统一的批次状态视图
   - 跨状态操作需要跳转多个页面

## 🎯 设计目标

1. **统一接口**：一个API管理所有状态批次的优先级
2. **状态流转**：支持批次在不同状态间的流转
3. **统一界面**：一个页面管理所有状态的批次
4. **充分利用现有架构**：基于现有表结构，不新建表

## 🏗️ 架构设计（修正版）

### 1. 充分利用现有表结构

#### 1.1 现有表结构分析

**`lotprioritydone`表已经非常完善**：
```sql
-- 当前lotprioritydone表已支持统一管理
- 基础字段：LOT_ID, DEVICE, STAGE, GOOD_QTY, PROD_ID, CHIP_ID, PKG_PN等
- 状态管理：SCHEDULING_STATUS (SUCCESS, FAILED, MANUAL_ADJUSTED)
- 失败管理：FAILURE_REASON, FAILURE_DETAILS
- 会话管理：SESSION_ID
- 扩展字段：match_type, comprehensive_score, processing_time等38个字段
- 审计字段：created_at, updated_at
```

#### 1.2 最小化表结构调整

**只需要给`et_wait_lot`表增加少量字段**：
```sql
-- 最小化扩展et_wait_lot表
ALTER TABLE et_wait_lot ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'WAITING';
ALTER TABLE et_wait_lot ADD COLUMN IF NOT EXISTS priority_level INT DEFAULT 999;
ALTER TABLE et_wait_lot ADD COLUMN IF NOT EXISTS last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
```

### 2. 统一API设计

#### 2.1 跨表统一查询API

```python
# /api/v3/production/unified-lot-management
class UnifiedLotManagementAPI:
    
    def get_all_lots(self, status=None, filters=None):
        """统一查询所有状态的批次"""
        # 等待批次查询
        waiting_query = """
            SELECT 
                'WAITING' as status,
                LOT_ID, DEVICE, STAGE, GOOD_QTY, PROD_ID, CHIP_ID, PKG_PN,
                priority_level as PRIORITY,
                NULL as HANDLER_ID,
                NULL as comprehensive_score,
                NULL as FAILURE_REASON,
                last_updated as updated_at
            FROM et_wait_lot 
            WHERE status = 'WAITING'
        """
        
        # 已处理批次查询
        processed_query = """
            SELECT 
                SCHEDULING_STATUS as status,
                LOT_ID, DEVICE, STAGE, GOOD_QTY, PROD_ID, CHIP_ID, PKG_PN,
                PRIORITY,
                HANDLER_ID,
                comprehensive_score,
                FAILURE_REASON,
                updated_at
            FROM lotprioritydone
        """
        
        # 合并查询
        union_query = f"""
            ({waiting_query})
            UNION ALL
            ({processed_query})
            ORDER BY PRIORITY ASC, updated_at DESC
        """
        
        return self.execute_query(union_query, filters)
    
    def batch_priority_update(self, updates):
        """批量更新优先级（跨表）"""
        waiting_updates = []
        processed_updates = []
        
        for update in updates:
            if update['status'] == 'WAITING':
                waiting_updates.append(update)
            else:
                processed_updates.append(update)
        
        # 分别更新不同表
        if waiting_updates:
            self.update_waiting_lots(waiting_updates)
        if processed_updates:
            self.update_processed_lots(processed_updates)
    
    def status_transition(self, lot_ids, from_status, to_status):
        """状态转换（跨表数据迁移）"""
        if from_status == 'WAITING' and to_status in ['SUCCESS', 'FAILED']:
            # 从等待表迁移到完成表
            self.migrate_waiting_to_processed(lot_ids, to_status)
        elif from_status in ['FAILED'] and to_status == 'WAITING':
            # 从完成表迁移回等待表（失败救援）
            self.migrate_processed_to_waiting(lot_ids)
        else:
            # 同表内状态转换
            self.update_status_in_same_table(lot_ids, to_status)
```

#### 2.2 状态转换支持

```python
# 支持的状态转换流向
VALID_TRANSITIONS = {
    'WAITING': ['SUCCESS', 'FAILED', 'ON_HOLD'],
    'SUCCESS': ['MANUAL_ADJUSTED', 'ON_HOLD'],
    'FAILED': ['MANUAL_ADJUSTED', 'WAITING', 'ON_HOLD'],  # 失败救援
    'MANUAL_ADJUSTED': ['SUCCESS', 'ON_HOLD'],
    'ON_HOLD': ['WAITING']
}
```

### 3. 统一前端界面设计

#### 3.1 基于现有`semi_auto.html`扩展

```html
<!-- 在现有semi_auto.html基础上添加统一管理功能 -->
<div class="unified-lot-management">
    <!-- 状态筛选标签 -->
    <div class="status-filter-tabs">
        <button class="status-tab active" data-status="all">
            全部批次 <span class="count">(1247)</span>
        </button>
        <button class="status-tab" data-status="WAITING">
            等待排产 <span class="count">(201)</span>
        </button>
        <button class="status-tab" data-status="SUCCESS">
            排产成功 <span class="count">(876)</span>
        </button>
        <button class="status-tab" data-status="FAILED">
            排产失败 <span class="count">(125)</span>
        </button>
        <button class="status-tab" data-status="MANUAL_ADJUSTED">
            手动调整 <span class="count">(45)</span>
        </button>
    </div>
    
    <!-- 批量操作工具栏 -->
    <div class="batch-operations">
        <button class="btn btn-primary" onclick="batchPriorityUpdate()">
            <i class="fas fa-sort"></i> 批量调整优先级
        </button>
        <button class="btn btn-warning" onclick="batchStatusTransition()">
            <i class="fas fa-exchange-alt"></i> 批量状态转换
        </button>
        <button class="btn btn-success" onclick="rescueFailedLots()">
            <i class="fas fa-life-ring"></i> 救援失败批次
        </button>
        <button class="btn btn-info" onclick="rebalancePriorities()">
            <i class="fas fa-balance-scale"></i> 优先级重平衡
        </button>
    </div>
    
    <!-- 统一数据表格 -->
    <table class="table table-striped" id="unifiedLotTable">
        <thead>
            <tr>
                <th><input type="checkbox" id="selectAll"></th>
                <th>状态</th>
                <th>优先级</th>
                <th>批次号</th>
                <th>产品</th>
                <th>工序</th>
                <th>数量</th>
                <th>设备</th>
                <th>综合得分</th>
                <th>失败原因</th>
                <th>更新时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody id="unifiedLotTableBody">
            <!-- 动态加载所有状态的批次 -->
        </tbody>
    </table>
</div>
```

#### 3.2 JavaScript统一管理器

```javascript
// 扩展现有JavaScript功能
class UnifiedLotManager {
    constructor() {
        this.currentStatus = 'all';
        this.selectedLots = new Set();
        this.loadUnifiedLots();
    }
    
    async loadUnifiedLots(status = 'all') {
        try {
            const response = await fetch('/api/v3/production/unified-lot-management/lots', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    status: status,
                    filters: this.getFilters()
                })
            });
            
            const data = await response.json();
            this.renderUnifiedTable(data.lots);
            this.updateStatusCounts(data.counts);
        } catch (error) {
            console.error('加载统一批次数据失败:', error);
        }
    }
    
    renderUnifiedTable(lots) {
        const tbody = document.getElementById('unifiedLotTableBody');
        tbody.innerHTML = lots.map(lot => `
            <tr data-lot-id="${lot.LOT_ID}" data-status="${lot.status}">
                <td><input type="checkbox" class="lot-checkbox" value="${lot.LOT_ID}"></td>
                <td><span class="status-badge status-${lot.status.toLowerCase()}">${this.getStatusText(lot.status)}</span></td>
                <td>
                    <input type="number" class="priority-input" value="${lot.PRIORITY}" 
                           onchange="unifiedLotManager.updatePriority('${lot.LOT_ID}', this.value)">
                </td>
                <td>${lot.LOT_ID}</td>
                <td>${lot.PROD_ID}</td>
                <td>${lot.STAGE}</td>
                <td>${lot.GOOD_QTY}</td>
                <td>${lot.DEVICE}</td>
                <td>${lot.comprehensive_score || '-'}</td>
                <td>${lot.FAILURE_REASON || '-'}</td>
                <td>${lot.updated_at}</td>
                <td>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            操作
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" onclick="unifiedLotManager.showDetails('${lot.LOT_ID}')">查看详情</a></li>
                            ${this.getStatusActions(lot.status, lot.LOT_ID)}
                        </ul>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    getStatusActions(status, lotId) {
        const actions = [];
        
        if (status === 'WAITING') {
            actions.push(`<li><a class="dropdown-item" onclick="unifiedLotManager.forceSchedule('${lotId}')">强制排产</a></li>`);
        }
        
        if (status === 'FAILED') {
            actions.push(`<li><a class="dropdown-item" onclick="unifiedLotManager.rescueLot('${lotId}')">救援批次</a></li>`);
        }
        
        if (status === 'SUCCESS') {
            actions.push(`<li><a class="dropdown-item" onclick="unifiedLotManager.manualAdjust('${lotId}')">手动调整</a></li>`);
        }
        
        return actions.join('');
    }
    
    async batchStatusTransition(targetStatus) {
        const selectedLots = Array.from(this.selectedLots);
        if (selectedLots.length === 0) {
            alert('请选择要操作的批次');
            return;
        }
        
        try {
            const response = await fetch('/api/v3/production/unified-lot-management/status-transition', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    lot_ids: selectedLots,
                    target_status: targetStatus,
                    reason: '批量状态转换'
                })
            });
            
            const result = await response.json();
            if (result.success) {
                this.loadUnifiedLots();
                alert(`成功转换 ${result.count} 个批次到 ${targetStatus} 状态`);
            }
        } catch (error) {
            console.error('批量状态转换失败:', error);
        }
    }
    
    async rescueFailedLots() {
        const failedLots = Array.from(this.selectedLots).filter(lotId => {
            const row = document.querySelector(`tr[data-lot-id="${lotId}"]`);
            return row && row.dataset.status === 'FAILED';
        });
        
        if (failedLots.length === 0) {
            alert('请选择失败的批次进行救援');
            return;
        }
        
        await this.batchStatusTransition('WAITING');
    }
}

// 初始化统一管理器
const unifiedLotManager = new UnifiedLotManager();
```

### 4. 实施步骤

#### 4.1 阶段一：最小化表结构调整 ✅
- [x] 分析现有表结构
- [x] 确认`lotprioritydone`表已完善
- [ ] 给`et_wait_lot`表增加`status`和`priority_level`字段

#### 4.2 阶段二：统一API开发
- [ ] 开发跨表统一查询API
- [ ] 实现批量优先级更新API
- [ ] 实现状态转换API（包含跨表数据迁移）

#### 4.3 阶段三：前端界面统一
- [ ] 在`semi_auto.html`基础上添加统一管理功能
- [ ] 实现状态筛选和批量操作
- [ ] 添加失败批次救援功能

#### 4.4 阶段四：测试和优化
- [ ] 端到端测试
- [ ] 性能优化
- [ ] 用户反馈优化

## 🚀 预期收益

1. **开发成本降低80%**：不需要新建表，充分利用现有架构
2. **管理效率提升50%**：统一界面管理所有状态批次
3. **操作复杂度降低70%**：一键完成跨状态操作
4. **用户体验显著改善**：统一的状态视图和批量操作

## 📊 性能指标

- **API响应时间**：< 300ms（跨表联合查询）
- **并发处理能力**：支持10个用户同时操作
- **数据处理量**：单次处理1000+批次（跨表）
- **实时更新延迟**：< 100ms

---

**创建时间**: 2025年1月16日  
**修正时间**: 2025年1月16日  
**架构设计**: AI助手  
**状态**: 🚧 修正完成，充分利用现有架构 