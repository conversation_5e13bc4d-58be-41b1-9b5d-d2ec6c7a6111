#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
渐进式数据库迁移 - 迁移验证器
负责验证数据完整性和功能测试
"""

import pymysql
import logging
import requests
import time
from migration_scripts.batch_configs import get_database_config, get_validation_config

class MigrationValidator:
    """迁移验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_config = get_database_config()
        self.validation_config = get_validation_config()
        self.connection_params = self.db_config['connection_params']
    
    def validate_batch(self, batch_config):
        """验证批次迁移结果"""
        try:
            self.logger.info(f"🔍 开始验证批次: {batch_config['name']}")
            
            # 1. 数据完整性检查
            integrity_passed = self.validate_data_integrity(batch_config['tables'])
            if not integrity_passed:
                self.logger.error("数据完整性验证失败")
                return False
            
            # 2. 表结构验证
            structure_passed = self.validate_table_structures(batch_config['tables'])
            if not structure_passed:
                self.logger.error("表结构验证失败")
                return False
            
            self.logger.info("✅ 批次验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"批次验证过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def validate_data_integrity(self, tables):
        """验证数据完整性"""
        try:
            self.logger.info("🔍 验证数据完整性...")
            
            for table in tables:
                if not self.validate_single_table_integrity(table):
                    return False
            
            self.logger.info("✅ 数据完整性验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"数据完整性验证失败: {e}")
            return False
    
    def validate_single_table_integrity(self, table_name):
        """验证单个表的数据完整性"""
        source_conn = None
        target_conn = None
        
        try:
            source_conn = self.get_connection(self.db_config['source_db'])
            target_conn = self.get_connection(self.db_config['target_db'])
            
            if not source_conn or not target_conn:
                return False
            
            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()
            
            # 检查源表是否存在
            source_cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            if not source_cursor.fetchone():
                self.logger.info(f"源表 {table_name} 不存在，跳过验证")
                return True
            
            # 检查目标表是否存在
            target_cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            if not target_cursor.fetchone():
                self.logger.error(f"目标表 {table_name} 不存在")
                return False
            
            # 1. 记录数量验证
            source_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            source_count = source_cursor.fetchone()[0]
            
            target_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            target_count = target_cursor.fetchone()[0]
            
            if source_count > target_count:
                self.logger.error(f"表 {table_name} 记录数不匹配: 源({source_count}) > 目标({target_count})")
                return False
            
            # 2. 字段完整性验证
            if not self.validate_field_completeness(table_name, source_conn, target_conn):
                return False
            
            self.logger.info(f"表 {table_name} 数据完整性验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"验证表 {table_name} 完整性时发生错误: {e}")
            return False
        
        finally:
            if source_conn:
                source_conn.close()
            if target_conn:
                target_conn.close()
    
    def validate_field_completeness(self, table_name, source_conn, target_conn):
        """验证字段完整性"""
        try:
            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()
            
            # 获取字段信息
            source_cursor.execute(f"DESCRIBE {table_name}")
            source_fields = {row[0]: row[1] for row in source_cursor.fetchall()}
            
            target_cursor.execute(f"DESCRIBE {table_name}")
            target_fields = {row[0]: row[1] for row in target_cursor.fetchall()}
            
            # 检查字段是否完整
            missing_fields = set(source_fields.keys()) - set(target_fields.keys())
            if missing_fields:
                self.logger.error(f"表 {table_name} 缺少字段: {missing_fields}")
                return False
            
            # 检查字段类型是否一致
            for field, field_type in source_fields.items():
                if field in target_fields and target_fields[field] != field_type:
                    self.logger.warning(f"表 {table_name} 字段 {field} 类型不一致: {field_type} != {target_fields[field]}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证表 {table_name} 字段完整性时发生错误: {e}")
            return False
    
    def validate_table_structures(self, tables):
        """验证表结构"""
        try:
            self.logger.info("🔍 验证表结构...")
            
            target_conn = self.get_connection(self.db_config['target_db'])
            if not target_conn:
                return False
            
            cursor = target_conn.cursor()
            
            for table in tables:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                if not cursor.fetchone():
                    # 检查是否是空表迁移
                    source_conn = self.get_connection(self.db_config['source_db'])
                    if source_conn:
                        source_cursor = source_conn.cursor()
                        source_cursor.execute(f"SHOW TABLES LIKE '{table}'")
                        if source_cursor.fetchone():
                            source_cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            if source_cursor.fetchone()[0] > 0:
                                self.logger.error(f"目标表 {table} 不存在，但源表有数据")
                                source_conn.close()
                                return False
                        source_conn.close()
                    
                    self.logger.info(f"表 {table} 在目标数据库中不存在（可能是空表）")
                    continue
                
                # 验证表结构完整性
                cursor.execute(f"DESCRIBE {table}")
                fields = cursor.fetchall()
                if not fields:
                    self.logger.error(f"表 {table} 结构为空")
                    return False
                
                self.logger.info(f"表 {table} 结构验证通过，{len(fields)} 个字段")
            
            target_conn.close()
            self.logger.info("✅ 表结构验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"表结构验证失败: {e}")
            return False
    
    def run_functionality_tests(self, test_list):
        """运行功能测试"""
        try:
            if not test_list:
                self.logger.info("无需执行功能测试")
                return True
            
            self.logger.info(f"🧪 开始功能测试，共 {len(test_list)} 项")
            
            passed_tests = 0
            for test_name in test_list:
                try:
                    result = self.execute_functionality_test(test_name)
                    if result:
                        passed_tests += 1
                        self.logger.info(f"✅ 测试 {test_name} 通过")
                    else:
                        self.logger.error(f"❌ 测试 {test_name} 失败")
                except Exception as e:
                    self.logger.error(f"❌ 测试 {test_name} 执行异常: {e}")
            
            success_rate = passed_tests / len(test_list)
            self.logger.info(f"📊 功能测试结果: {passed_tests}/{len(test_list)} 通过 ({success_rate:.1%})")
            
            # 要求至少80%的测试通过
            return success_rate >= 0.8
            
        except Exception as e:
            self.logger.error(f"功能测试执行失败: {e}")
            return False
    
    def execute_functionality_test(self, test_name):
        """执行具体的功能测试"""
        try:
            if test_name == 'test_basic_connectivity':
                return self.test_basic_connectivity()
            elif test_name == 'test_table_structure':
                return self.test_table_structure()
            elif test_name == 'test_system_settings_api':
                return self.test_system_settings_api()
            elif test_name == 'test_ai_settings_functionality':
                return self.test_ai_settings_functionality()
            elif test_name == 'test_scheduling_config':
                return self.test_scheduling_config()
            elif test_name == 'test_user_authentication':
                return self.test_user_authentication()
            elif test_name == 'test_permission_system':
                return self.test_permission_system()
            elif test_name == 'test_email_functionality':
                return self.test_email_functionality()
            else:
                self.logger.warning(f"未知的测试: {test_name}")
                return True  # 未知测试默认通过
                
        except Exception as e:
            self.logger.error(f"执行测试 {test_name} 时发生错误: {e}")
            return False
    
    def test_basic_connectivity(self):
        """测试基本数据库连接"""
        try:
            conn = self.get_connection(self.db_config['target_db'])
            if not conn:
                return False
            
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            conn.close()
            
            return result[0] == 1
        except:
            return False
    
    def test_table_structure(self):
        """测试表结构"""
        try:
            conn = self.get_connection(self.db_config['target_db'])
            if not conn:
                return False
            
            cursor = conn.cursor()
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            conn.close()
            
            return len(tables) > 0
        except:
            return False
    
    def test_system_settings_api(self):
        """测试系统设置API"""
        try:
            # 这里可以添加实际的API测试
            # 目前返回True作为占位符
            return True
        except:
            return False
    
    def test_ai_settings_functionality(self):
        """测试AI设置功能"""
        try:
            # 这里可以添加AI设置相关的测试
            return True
        except:
            return False
    
    def test_scheduling_config(self):
        """测试调度配置"""
        try:
            # 这里可以添加调度配置相关的测试
            return True
        except:
            return False
    
    def test_user_authentication(self):
        """测试用户认证"""
        try:
            # 这里可以添加用户认证相关的测试
            return True
        except:
            return False
    
    def test_permission_system(self):
        """测试权限系统"""
        try:
            # 这里可以添加权限系统相关的测试
            return True
        except:
            return False
    
    def test_email_functionality(self):
        """测试邮件功能"""
        try:
            # 这里可以添加邮件功能相关的测试
            return True
        except:
            return False
    
    def get_connection(self, database_name):
        """获取数据库连接"""
        try:
            conn = pymysql.connect(
                database=database_name,
                **self.connection_params
            )
            return conn
        except Exception as e:
            self.logger.error(f"连接数据库 {database_name} 失败: {e}")
            return None 