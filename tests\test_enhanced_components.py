import unittest
from unittest.mock import Mock, patch, MagicMock, call
import threading
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.utils.enhanced_scheduling_lock import EnhancedScheduling<PERSON>ock, LockContext, SchedulingLockManager
from app.utils.enhanced_transaction_manager import EnhancedTransactionManager
from app.utils.batch_operation_optimizer import BatchOperationOptimizer
from app.utils.scheduling_performance_monitor import SchedulingPerformanceMonitor


class TestEnhancedSchedulingLock(unittest.TestCase):
    """测试增强的排产锁管理"""
    
    def setUp(self):
        """设置测试环境"""
        self.lock_manager = EnhancedSchedulingLock()
    
    def test_lock_context_creation(self):
        """测试锁上下文创建"""
        context = LockContext(
            lock_id="test_lock_123",
            lock_key="test_key",
            algorithm="intelligent",
            user_id="test_user",
            optimization_target="balanced"
        )
        
        self.assertEqual(context.lock_id, "test_lock_123")
        self.assertEqual(context.algorithm, "intelligent")
        self.assertEqual(context.user_id, "test_user")
        self.assertIsInstance(context.get_lock_duration(), float)
        
        # 测试字典转换
        context_dict = context.to_dict()
        self.assertIn('lock_id', context_dict)
        self.assertIn('algorithm', context_dict)
        self.assertIn('duration', context_dict)
    
    def test_app_lock_acquisition(self):
        """测试应用级锁获取"""
        lock_key = "test_algorithm_user_target"
        lock_id = "test_lock_123"
        
        # 第一次获取应该成功
        success1 = self.lock_manager._acquire_app_lock(lock_key, lock_id, 1)
        self.assertTrue(success1)
        
        # 相同key的第二次获取应该失败
        success2 = self.lock_manager._acquire_app_lock(lock_key, "another_lock", 0.1)
        self.assertFalse(success2)
        
        # 释放锁后应该能再次获取
        self.lock_manager._release_app_lock(lock_key)
        success3 = self.lock_manager._acquire_app_lock(lock_key, "new_lock", 1)
        self.assertTrue(success3)
    
    def test_lock_status(self):
        """测试锁状态获取"""
        status = self.lock_manager.get_lock_status()
        self.assertIn('active_locks', status)
        self.assertIn('locks', status)
        self.assertIn('current_time', status)
        self.assertEqual(status['active_locks'], 0)
        
        # 获取一个锁后状态应该更新
        lock_key = "test_status_key"
        self.lock_manager._acquire_app_lock(lock_key, "test_lock", 1)
        
        status_after = self.lock_manager.get_lock_status()
        self.assertEqual(status_after['active_locks'], 1)
        self.assertIn(lock_key, status_after['locks'])
    
    def test_cleanup_expired_locks(self):
        """测试清理过期锁"""
        # 添加一个锁
        lock_key = "expired_lock_test"
        self.lock_manager._acquire_app_lock(lock_key, "test_lock", 1)
        
        # 手动设置为过期时间
        self.lock_manager.app_locks[lock_key]['acquired_time'] = time.time() - 400  # 400秒前
        
        # 清理过期锁
        cleaned_count = self.lock_manager.cleanup_expired_locks(max_age=300)  # 5分钟
        self.assertEqual(cleaned_count, 1)
        
        # 验证锁已被清理
        status = self.lock_manager.get_lock_status()
        self.assertEqual(status['active_locks'], 0)
    
    def test_force_release_lock(self):
        """测试强制释放锁"""
        lock_key = "force_release_test"
        self.lock_manager._acquire_app_lock(lock_key, "test_lock", 1)
        
        # 验证锁存在
        self.assertTrue(self.lock_manager.check_lock_exists(lock_key))
        
        # 强制释放
        success = self.lock_manager.force_release_lock(lock_key)
        self.assertTrue(success)
        
        # 验证锁已被释放
        self.assertFalse(self.lock_manager.check_lock_exists(lock_key))
        
        # 尝试释放不存在的锁
        success_nonexistent = self.lock_manager.force_release_lock("nonexistent_key")
        self.assertFalse(success_nonexistent)


class TestSchedulingLockManager(unittest.TestCase):
    """测试排产锁管理器兼容性API"""
    
    def setUp(self):
        """设置测试环境"""
        self.manager = SchedulingLockManager()
    
    def test_check_duplicate_execution(self):
        """测试重复执行检查"""
        # 没有活动锁时应该返回False
        result = self.manager.check_duplicate_execution("intelligent", "user1", "balanced")
        self.assertFalse(result)
        
        # 模拟活动锁
        lock_key = "intelligent_user1_balanced"
        self.manager.enhanced_lock._acquire_app_lock(lock_key, "test_lock", 1)
        
        # 有活动锁时应该返回True
        result_with_lock = self.manager.check_duplicate_execution("intelligent", "user1", "balanced")
        self.assertTrue(result_with_lock)


class TestEnhancedTransactionManager(unittest.TestCase):
    """测试增强的事务管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.transaction_manager = EnhancedTransactionManager()
    
    def test_get_current_transaction_id(self):
        """测试获取当前事务ID"""
        # 初始时应该没有事务ID
        tx_id = self.transaction_manager.get_current_transaction_id()
        self.assertIsNone(tx_id)
    
    @patch('app.utils.enhanced_transaction_manager.db')
    def test_create_backup_table(self, mock_db):
        """测试创建备份表"""
        mock_session = Mock()
        mock_db.session = mock_session
        
        backup_table = self.transaction_manager.create_backup_table("test_table", "20250116")
        
        self.assertEqual(backup_table, "test_table_backup_20250116")
        mock_session.execute.assert_called_once()
    
    @patch('app.utils.enhanced_transaction_manager.db')
    def test_drop_backup_table(self, mock_db):
        """测试删除备份表"""
        mock_session = Mock()
        mock_db.session = mock_session
        
        result = self.transaction_manager.drop_backup_table("test_backup_table")
        
        self.assertTrue(result)
        mock_session.execute.assert_called_once()
    
    @patch('app.utils.enhanced_transaction_manager.db')
    def test_get_table_count(self, mock_db):
        """测试获取表记录数"""
        mock_session = Mock()
        mock_db.session = mock_session
        mock_session.execute.return_value.scalar.return_value = 100
        
        count = self.transaction_manager.get_table_count("test_table")
        
        self.assertEqual(count, 100)
        mock_session.execute.assert_called_once()
    
    @patch('app.utils.enhanced_transaction_manager.db')
    def test_validate_table_integrity_success(self, mock_db):
        """测试表完整性验证成功"""
        mock_session = Mock()
        mock_db.session = mock_session
        mock_session.execute.return_value.scalar.return_value = 50
        mock_session.execute.return_value.rowcount = 1
        
        result = self.transaction_manager.validate_table_integrity("test_table", 50)
        
        self.assertTrue(result)
    
    @patch('app.utils.enhanced_transaction_manager.db')
    def test_validate_table_integrity_count_mismatch(self, mock_db):
        """测试表完整性验证计数不匹配"""
        mock_session = Mock()
        mock_db.session = mock_session
        mock_session.execute.return_value.scalar.return_value = 40
        
        result = self.transaction_manager.validate_table_integrity("test_table", 50)
        
        self.assertFalse(result)


class TestBatchOperationOptimizer(unittest.TestCase):
    """测试批量操作优化器"""
    
    def setUp(self):
        """设置测试环境"""
        self.optimizer = BatchOperationOptimizer(batch_size=10)
    
    def test_calculate_batches(self):
        """测试批次计算"""
        self.assertEqual(self.optimizer._calculate_batches(100), 10)
        self.assertEqual(self.optimizer._calculate_batches(95), 10)
        self.assertEqual(self.optimizer._calculate_batches(105), 11)
        self.assertEqual(self.optimizer._calculate_batches(0), 0)
    
    def test_chunk_data(self):
        """测试数据分批"""
        data = [{'id': i} for i in range(25)]
        
        chunks = list(self.optimizer._chunk_data(data))
        
        self.assertEqual(len(chunks), 3)  # 25 / 10 = 3 batches
        self.assertEqual(len(chunks[0]), 10)
        self.assertEqual(len(chunks[1]), 10)
        self.assertEqual(len(chunks[2]), 5)
    
    def test_chunk_list(self):
        """测试列表分批"""
        data = list(range(23))
        
        chunks = list(self.optimizer._chunk_list(data))
        
        self.assertEqual(len(chunks), 3)  # 23 / 10 = 3 batches
        self.assertEqual(len(chunks[0]), 10)
        self.assertEqual(len(chunks[1]), 10)
        self.assertEqual(len(chunks[2]), 3)
    
    def test_set_batch_size(self):
        """测试设置批次大小"""
        self.optimizer.set_batch_size(50)
        self.assertEqual(self.optimizer.batch_size, 50)
        
        # 测试无效批次大小
        with self.assertRaises(ValueError):
            self.optimizer.set_batch_size(0)
        
        with self.assertRaises(ValueError):
            self.optimizer.set_batch_size(-10)
    
    def test_get_optimal_batch_size(self):
        """测试获取最优批次大小"""
        # 小数据集
        optimal_size = self.optimizer.get_optimal_batch_size(50, 100)
        self.assertGreaterEqual(optimal_size, 100)  # 至少100
        self.assertLessEqual(optimal_size, 5000)    # 最多5000
        
        # 大数据集
        optimal_size_large = self.optimizer.get_optimal_batch_size(10000, 50)
        self.assertGreaterEqual(optimal_size_large, 100)
        self.assertLessEqual(optimal_size_large, 5000)
    
    def test_analyze_performance(self):
        """测试性能分析"""
        metrics = self.optimizer.analyze_performance("test_operation", 1000, 5.0)
        
        self.assertEqual(metrics['operation'], "test_operation")
        self.assertEqual(metrics['data_size'], 1000)
        self.assertEqual(metrics['execution_time'], 5.0)
        self.assertEqual(metrics['records_per_second'], 200.0)  # 1000 / 5
        self.assertGreater(metrics['batches_per_second'], 0)
    
    @patch('app.utils.batch_operation_optimizer.db')
    def test_optimize_database_settings(self, mock_db):
        """测试数据库设置优化"""
        mock_session = Mock()
        mock_db.session = mock_session
        
        self.optimizer.optimize_database_settings()
        
        # 验证调用了多个SET SESSION命令
        self.assertGreater(mock_session.execute.call_count, 0)
    
    @patch('app.utils.batch_operation_optimizer.db')
    def test_restore_database_settings(self, mock_db):
        """测试恢复数据库设置"""
        mock_session = Mock()
        mock_db.session = mock_session
        
        self.optimizer.restore_database_settings()
        
        # 验证调用了多个SET SESSION命令
        self.assertGreater(mock_session.execute.call_count, 0)


class TestSchedulingPerformanceMonitor(unittest.TestCase):
    """测试排产性能监控器"""
    
    def setUp(self):
        """设置测试环境"""
        self.monitor = SchedulingPerformanceMonitor()
    
    def test_monitor_execution_context(self):
        """测试执行监控上下文"""
        operation_name = "test_operation"
        
        with self.monitor.monitor_execution(operation_name, {'test': 'context'}):
            time.sleep(0.1)  # 模拟一些工作
        
        # 验证指标被记录
        summary = self.monitor.get_performance_summary(operation_name)
        self.assertGreater(len(summary), 0)
        self.assertEqual(summary['operation'], operation_name)
        self.assertEqual(summary['count'], 1)
        self.assertGreater(summary['avg_execution_time'], 0)
    
    def test_get_performance_summary_empty(self):
        """测试空操作的性能摘要"""
        summary = self.monitor.get_performance_summary("nonexistent_operation")
        self.assertEqual(len(summary), 0)
    
    def test_get_all_performance_summaries(self):
        """测试获取所有性能摘要"""
        # 添加一些测试数据
        with self.monitor.monitor_execution("operation1"):
            pass
        with self.monitor.monitor_execution("operation2"):
            pass
        
        summaries = self.monitor.get_all_performance_summaries()
        
        self.assertIn('operation1', summaries)
        self.assertIn('operation2', summaries)
    
    @patch('app.utils.scheduling_performance_monitor.psutil')
    def test_get_system_metrics(self, mock_psutil):
        """测试获取系统指标"""
        # 模拟psutil返回值
        mock_psutil.cpu_percent.return_value = 50.0
        mock_psutil.cpu_count.return_value = 4
        
        mock_memory = Mock()
        mock_memory.total = 8 * 1024 * 1024 * 1024  # 8GB
        mock_memory.available = 4 * 1024 * 1024 * 1024  # 4GB
        mock_memory.used = 4 * 1024 * 1024 * 1024  # 4GB
        mock_memory.percent = 50.0
        mock_psutil.virtual_memory.return_value = mock_memory
        
        mock_disk = Mock()
        mock_disk.total = 100 * 1024 * 1024 * 1024  # 100GB
        mock_disk.free = 50 * 1024 * 1024 * 1024   # 50GB
        mock_disk.used = 50 * 1024 * 1024 * 1024   # 50GB
        mock_disk.percent = 50.0
        mock_psutil.disk_usage.return_value = mock_disk
        
        metrics = self.monitor.get_system_metrics()
        
        self.assertIn('cpu', metrics)
        self.assertIn('memory', metrics)
        self.assertIn('disk', metrics)
        self.assertIn('process', metrics)
        self.assertEqual(metrics['cpu']['percent'], 50.0)
        self.assertEqual(metrics['memory']['percent'], 50.0)
    
    def test_clear_metrics(self):
        """测试清理指标"""
        # 添加一些测试数据
        with self.monitor.monitor_execution("test_clear"):
            pass
        
        # 验证数据存在
        summary = self.monitor.get_performance_summary("test_clear")
        self.assertGreater(len(summary), 0)
        
        # 清理指定操作的指标
        self.monitor.clear_metrics("test_clear")
        
        # 验证数据已清理
        summary_after = self.monitor.get_performance_summary("test_clear")
        self.assertEqual(len(summary_after), 0)
    
    def test_export_metrics_json(self):
        """测试导出JSON格式指标"""
        with self.monitor.monitor_execution("export_test"):
            pass
        
        json_data = self.monitor.export_metrics("export_test", "json")
        
        self.assertIsInstance(json_data, str)
        self.assertIn("export_test", json_data)
        self.assertIn("execution_time", json_data)
    
    def test_export_metrics_csv(self):
        """测试导出CSV格式指标"""
        with self.monitor.monitor_execution("csv_test"):
            pass
        
        csv_data = self.monitor.export_metrics("csv_test", "csv")
        
        self.assertIsInstance(csv_data, str)
        self.assertIn("timestamp,execution_time,memory_usage,cpu_time", csv_data)
    
    def test_check_performance_alerts(self):
        """测试性能告警检查"""
        # 模拟长时间执行的操作
        self.monitor.metrics["slow_operation"] = [{
            'execution_time': 35.0,  # 超过30秒阈值
            'memory_usage': 600.0,   # 超过500MB阈值
            'cpu_time': 30.0,
            'timestamp': time.time(),
            'operation': 'slow_operation',
            'context': {}
        }]
        
        alerts = self.monitor.check_performance_alerts("slow_operation")
        
        self.assertGreater(len(alerts), 0)
        # 应该有执行时间和内存使用的告警
        alert_types = [alert['type'] for alert in alerts]
        self.assertIn('execution_time', alert_types)
        self.assertIn('memory_usage', alert_types)


class TestIntegrationWorkflow(unittest.TestCase):
    """集成测试 - 测试组件间协作"""
    
    def test_complete_monitoring_workflow(self):
        """测试完整的监控工作流程"""
        monitor = SchedulingPerformanceMonitor()
        optimizer = BatchOperationOptimizer(batch_size=5)
        
        # 模拟一个完整的批量操作并监控
        test_data = [{'id': i, 'value': f'data_{i}'} for i in range(23)]
        
        with monitor.monitor_execution('batch_operation', {
            'data_size': len(test_data),
            'batch_size': optimizer.batch_size
        }):
            # 模拟批量处理
            for chunk in optimizer._chunk_data(test_data):
                time.sleep(0.01)  # 模拟处理时间
        
        # 验证监控结果
        summary = monitor.get_performance_summary('batch_operation')
        self.assertGreater(summary['count'], 0)
        self.assertGreater(summary['avg_execution_time'], 0)
        
        # 分析性能
        performance = optimizer.analyze_performance(
            'batch_operation',
            len(test_data),
            summary['avg_execution_time']
        )
        
        self.assertEqual(performance['data_size'], 23)
        self.assertGreater(performance['records_per_second'], 0)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2) 