#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
渐进式数据库迁移 - 主控制器
协调整个迁移过程，确保安全和可靠性
"""

import sys
import os
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from migration_scripts.batch_configs import (
    get_batch_config, get_all_batches, get_validation_config,
    get_rollback_config, get_database_config
)
from migration_scripts.data_migrator import DataMigrator
from migration_scripts.migration_validator import MigrationValidator
from migration_scripts.rollback_manager import RollbackManager
from migration_scripts.model_updater import ModelUpdater

class ProgressiveMigrator:
    """渐进式数据库迁移主控制器"""
    
    def __init__(self):
        self.setup_logging()
        self.data_migrator = DataMigrator()
        self.validator = MigrationValidator()
        self.rollback_manager = RollbackManager()
        self.model_updater = ModelUpdater()
        
        self.migration_state = {
            'current_batch': 0,
            'completed_batches': [],
            'failed_batches': [],
            'start_time': None,
            'end_time': None,
            'status': 'pending'
        }
    
    def setup_logging(self):
        """设置日志系统"""
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = log_dir / f'progressive_migration_{timestamp}.log'
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s | %(levelname)s | %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("渐进式数据库迁移系统初始化")
    
    def start_migration(self, start_batch=1, end_batch=None):
        """开始迁移过程"""
        try:
            self.migration_state['start_time'] = datetime.now()
            self.migration_state['status'] = 'running'
            
            self.logger.info("🚀 开始渐进式数据库迁移")
            self.logger.info(f"开始批次: {start_batch}")
            
            all_batches = get_all_batches()
            end_batch = end_batch or max(all_batches.keys())
            
            for batch_num in range(start_batch, end_batch + 1):
                if batch_num not in all_batches:
                    self.logger.warning(f"批次 {batch_num} 不存在，跳过")
                    continue
                
                success = self.execute_batch(batch_num)
                if not success:
                    self.logger.error(f"批次 {batch_num} 执行失败，停止迁移")
                    self.migration_state['status'] = 'failed'
                    return False
                
                self.migration_state['completed_batches'].append(batch_num)
                self.logger.info(f"✅ 批次 {batch_num} 完成")
            
            self.migration_state['end_time'] = datetime.now()
            self.migration_state['status'] = 'completed'
            self.logger.info("🎉 所有批次迁移完成")
            
            return True
            
        except Exception as e:
            self.logger.error(f"迁移过程发生错误: {e}")
            self.migration_state['status'] = 'error'
            import traceback
            traceback.print_exc()
            return False
    
    def execute_batch(self, batch_number):
        """执行单个批次的迁移"""
        try:
            batch_config = get_batch_config(batch_number)
            if not batch_config:
                self.logger.error(f"无法找到批次 {batch_number} 的配置")
                return False
            
            self.migration_state['current_batch'] = batch_number
            self.logger.info(f"📦 开始执行批次 {batch_number}: {batch_config['name']}")
            self.logger.info(f"描述: {batch_config['description']}")
            self.logger.info(f"风险级别: {batch_config['risk_level']}")
            
            # 步骤1: 创建备份
            self.logger.info("1️⃣ 创建数据备份...")
            backup_success = self.rollback_manager.create_backup(batch_config['tables'], batch_number)
            if not backup_success:
                self.logger.error("备份创建失败")
                return False
            
            # 步骤2: 执行数据迁移
            self.logger.info("2️⃣ 执行数据迁移...")
            migration_success = self.data_migrator.migrate_tables(
                batch_config['tables'], 
                batch_config.get('special_operation')
            )
            if not migration_success:
                self.logger.error("数据迁移失败")
                self.rollback_batch(batch_number)
                return False
            
            # 步骤3: 验证数据完整性
            self.logger.info("3️⃣ 验证数据完整性...")
            validation_success = self.validator.validate_batch(batch_config)
            if not validation_success:
                self.logger.error("数据验证失败")
                self.rollback_batch(batch_number)
                return False
            
            # 步骤4: 更新模型绑定（如果需要）
            if batch_config.get('models'):
                self.logger.info("4️⃣ 更新模型绑定...")
                model_update_success = self.model_updater.update_model_bindings(
                    batch_config['models']
                )
                if not model_update_success:
                    self.logger.warning("模型绑定更新失败，但不影响数据迁移")
            
            # 步骤5: 功能测试
            self.logger.info("5️⃣ 执行功能测试...")
            function_test_success = self.validator.run_functionality_tests(
                batch_config.get('validation_tests', [])
            )
            if not function_test_success:
                self.logger.error("功能测试失败")
                self.rollback_batch(batch_number)
                return False
            
            self.logger.info(f"✅ 批次 {batch_number} 执行成功")
            return True
            
        except Exception as e:
            self.logger.error(f"批次 {batch_number} 执行过程中发生错误: {e}")
            self.rollback_batch(batch_number)
            return False
    
    def rollback_batch(self, batch_number):
        """回滚指定批次"""
        self.logger.warning(f"🔄 开始回滚批次 {batch_number}")
        try:
            rollback_success = self.rollback_manager.rollback_batch(batch_number)
            if rollback_success:
                self.logger.info(f"✅ 批次 {batch_number} 回滚成功")
            else:
                self.logger.error(f"❌ 批次 {batch_number} 回滚失败")
            return rollback_success
        except Exception as e:
            self.logger.error(f"回滚过程中发生错误: {e}")
            return False
    
    def get_migration_status(self):
        """获取迁移状态"""
        return self.migration_state.copy()
    
    def pause_migration(self):
        """暂停迁移"""
        self.migration_state['status'] = 'paused'
        self.logger.info("⏸️ 迁移已暂停")
    
    def resume_migration(self):
        """恢复迁移"""
        if self.migration_state['status'] == 'paused':
            self.migration_state['status'] = 'running'
            self.logger.info("▶️ 迁移已恢复")
    
    def generate_report(self):
        """生成迁移报告"""
        report = {
            'migration_summary': self.migration_state,
            'completed_batches': len(self.migration_state['completed_batches']),
            'failed_batches': len(self.migration_state['failed_batches']),
            'duration': None
        }
        
        if self.migration_state['start_time'] and self.migration_state['end_time']:
            duration = self.migration_state['end_time'] - self.migration_state['start_time']
            report['duration'] = str(duration)
        
        return report

def main():
    """主函数 - 命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='渐进式数据库迁移工具')
    parser.add_argument('--start-batch', type=int, default=1, help='开始批次号')
    parser.add_argument('--end-batch', type=int, help='结束批次号')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式')
    parser.add_argument('--rollback', type=int, help='回滚指定批次')
    
    args = parser.parse_args()
    
    migrator = ProgressiveMigrator()
    
    if args.rollback:
        migrator.rollback_batch(args.rollback)
    elif args.dry_run:
        migrator.logger.info("🧪 试运行模式 - 不执行实际迁移")
        # 在这里可以添加试运行逻辑
    else:
        success = migrator.start_migration(args.start_batch, args.end_batch)
        if success:
            print("\n🎉 迁移完成！")
            report = migrator.generate_report()
            print(f"完成批次: {report['completed_batches']}")
            if report['duration']:
                print(f"总耗时: {report['duration']}")
        else:
            print("\n❌ 迁移失败！")
            sys.exit(1)

if __name__ == '__main__':
    main() 