#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据库连接助手
替代所有硬编码的数据库连接
"""

import pymysql
from config.enhanced_config import config
import logging

logger = logging.getLogger(__name__)

def get_unified_mysql_connection(database='aps'):
    """获取统一的MySQL连接"""
    try:
        connection = pymysql.connect(
            host=config.DB_HOST,
            port=config.DB_PORT,
            user=config.DB_USER,
            password=config.DB_PASSWORD,
            database=database,
            charset=config.DB_CHARSET,
            autocommit=False
        )
        logger.debug(f"✅ MySQL连接成功: {config.DB_HOST}:{config.DB_PORT}/{database}")
        return connection
    except Exception as e:
        logger.error(f"❌ MySQL连接失败: {e}")
        raise

def get_aps_connection():
    """获取APS业务数据库连接"""
    return get_unified_mysql_connection('aps')

def get_system_connection():
    """获取系统配置数据库连接（已迁移到aps）"""
    return get_unified_mysql_connection('aps')

# 向后兼容的别名
get_mysql_connection = get_unified_mysql_connection
