#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一批次管理数据库初始化工具
通过Flask应用上下文创建必要的数据库结构
"""

import logging
from sqlalchemy import text
from app import db

logger = logging.getLogger(__name__)

def create_extension_table():
    """创建et_wait_lot_extension扩展表"""
    try:
        logger.info("🔧 开始创建et_wait_lot_extension扩展表...")
        
        # 检查表是否已存在
        check_table_sql = """
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'et_wait_lot_extension'
        """
        
        result = db.session.execute(text(check_table_sql))
        if result.scalar() > 0:
            logger.info("✅ et_wait_lot_extension表已存在，跳过创建")
            return True
        
        # 创建扩展表
        create_table_sql = """
        CREATE TABLE et_wait_lot_extension (
            id INT AUTO_INCREMENT PRIMARY KEY,
            lot_id VARCHAR(255) NOT NULL COMMENT '批次号，关联et_wait_lot.LOT_ID',
            status VARCHAR(20) DEFAULT 'WAITING' COMMENT '批次状态',
            priority_level INT DEFAULT 999 COMMENT '优先级等级，数值越小优先级越高',
            manual_priority BOOLEAN DEFAULT FALSE COMMENT '是否手动设置优先级',
            rescue_count INT DEFAULT 0 COMMENT '救援次数',
            last_rescue_time TIMESTAMP NULL COMMENT '最后救援时间',
            notes TEXT COMMENT '备注信息',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_by VARCHAR(50) COMMENT '创建人',
            updated_by VARCHAR(50) COMMENT '更新人',
            
            UNIQUE KEY uk_lot_id (lot_id),
            KEY idx_status (status),
            KEY idx_priority_level (priority_level),
            KEY idx_updated_at (updated_at),
            KEY idx_rescue_count (rescue_count),
            KEY idx_status_priority (status, priority_level)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
        COMMENT='et_wait_lot扩展表 - 存储批次管理扩展信息'
        """
        
        db.session.execute(text(create_table_sql))
        logger.info("✅ et_wait_lot_extension表创建成功")
        
        # 为现有的et_wait_lot数据创建扩展记录
        logger.info("🔧 为现有数据创建扩展记录...")
        
        insert_extension_sql = """
        INSERT IGNORE INTO et_wait_lot_extension (lot_id, status, priority_level, created_by)
        SELECT 
            TRIM(LOT_ID) as lot_id,
            'WAITING',
            999,
            'system'
        FROM et_wait_lot
        WHERE LOT_ID IS NOT NULL 
        AND TRIM(LOT_ID) != ''
        AND GOOD_QTY > 0
        """
        
        result = db.session.execute(text(insert_extension_sql))
        affected_rows = result.rowcount
        logger.info(f"✅ 为 {affected_rows} 个现有批次创建了扩展记录")
        
        db.session.commit()
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建扩展表失败: {e}")
        db.session.rollback()
        return False

def create_unified_view():
    """创建v_unified_lot_management统一视图"""
    try:
        logger.info("🔧 开始创建v_unified_lot_management统一视图...")
        
        # 删除已存在的视图
        logger.info("🗑️ 删除已存在的统一视图...")
        db.session.execute(text("DROP VIEW IF EXISTS v_unified_lot_management"))
        
        # 创建统一视图
        create_view_sql = """
        CREATE VIEW v_unified_lot_management AS
        SELECT 
            'WAITING' as status,
            TRIM(w.LOT_ID) as LOT_ID,
            w.DEVICE,
            w.STAGE,
            CONVERT(w.GOOD_QTY, UNSIGNED) as GOOD_QTY,
            w.PROD_ID,
            w.CHIP_ID,
            w.PKG_PN,
            w.PO_ID,
            COALESCE(e.priority_level, 999) as PRIORITY,
            NULL as HANDLER_ID,
            NULL as comprehensive_score,
            NULL as FAILURE_REASON,
            NULL as match_type,
            NULL as processing_time,
            NULL as SESSION_ID,
            COALESCE(e.manual_priority, 0) as manual_priority,
            COALESCE(e.rescue_count, 0) as rescue_count,
            e.notes,
            'et_wait_lot' as source_table,
            COALESCE(e.updated_at, w.created_at) as updated_at,
            w.created_at as original_created_at
        FROM et_wait_lot w
        LEFT JOIN et_wait_lot_extension e ON TRIM(w.LOT_ID) COLLATE utf8mb4_unicode_ci = e.lot_id
        WHERE w.LOT_ID IS NOT NULL 
        AND TRIM(w.LOT_ID) != ''
        AND CONVERT(w.GOOD_QTY, UNSIGNED) > 0

        UNION ALL

        SELECT 
            IFNULL(SCHEDULING_STATUS, 'SUCCESS') as status,
            LOT_ID,
            DEVICE,
            STAGE,
            GOOD_QTY,
            PROD_ID,
            CHIP_ID,
            PKG_PN,
            PO_ID,
            PRIORITY,
            HANDLER_ID,
            comprehensive_score,
            FAILURE_REASON,
            match_type,
            processing_time,
            SESSION_ID,
            0 as manual_priority,
            0 as rescue_count,
            NULL as notes,
            'lotprioritydone' as source_table,
            updated_at,
            created_at as original_created_at
        FROM lotprioritydone
        WHERE LOT_ID IS NOT NULL
        AND TRIM(LOT_ID) != ''
        """
        
        db.session.execute(text(create_view_sql))
        logger.info("✅ v_unified_lot_management视图创建成功")
        
        db.session.commit()
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建统一视图失败: {e}")
        db.session.rollback()
        return False

def extend_lotprioritydone_table():
    """扩展lotprioritydone表结构"""
    try:
        logger.info("🔧 开始扩展lotprioritydone表结构...")
        
        # 检查字段是否已存在
        check_columns_sql = """
            SELECT COLUMN_NAME FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'lotprioritydone' 
            AND COLUMN_NAME IN ('SCHEDULING_STATUS', 'FAILURE_REASON', 'SESSION_ID')
        """
        
        result = db.session.execute(text(check_columns_sql))
        existing_columns = [row[0] for row in result.fetchall()]
        
        # 添加缺失的字段
        schema_changes = []
        
        if 'SCHEDULING_STATUS' not in existing_columns:
            schema_changes.append("""
                ALTER TABLE lotprioritydone 
                ADD COLUMN SCHEDULING_STATUS ENUM('SUCCESS', 'FAILED', 'MANUAL_ADJUSTED') 
                DEFAULT 'SUCCESS' 
                COMMENT '排产状态：SUCCESS=排产成功，FAILED=排产失败，MANUAL_ADJUSTED=手动调整'
            """)
        
        if 'FAILURE_REASON' not in existing_columns:
            schema_changes.append("""
                ALTER TABLE lotprioritydone 
                ADD COLUMN FAILURE_REASON VARCHAR(500) NULL 
                COMMENT '失败原因（仅失败批次使用）'
            """)
        
        if 'SESSION_ID' not in existing_columns:
            schema_changes.append("""
                ALTER TABLE lotprioritydone 
                ADD COLUMN SESSION_ID VARCHAR(50) NULL 
                COMMENT '排产会话ID'
            """)
        
        # 执行结构变更
        for change in schema_changes:
            db.session.execute(text(change))
            logger.info(f"✅ 执行结构变更: {change.strip()[:50]}...")
        
        # 添加索引
        try:
            db.session.execute(text("ALTER TABLE lotprioritydone ADD INDEX idx_scheduling_status (SCHEDULING_STATUS)"))
        except:
            pass  # 索引可能已存在
        
        try:
            db.session.execute(text("ALTER TABLE lotprioritydone ADD INDEX idx_session_id (SESSION_ID)"))
        except:
            pass  # 索引可能已存在
        
        db.session.commit()
        logger.info("✅ lotprioritydone表结构扩展完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 扩展lotprioritydone表失败: {e}")
        db.session.rollback()
        return False

def initialize_unified_management():
    """初始化统一批次管理数据库结构"""
    logger.info("🚀 开始初始化统一批次管理数据库结构...")
    
    success = True
    
    # 1. 扩展lotprioritydone表
    if not extend_lotprioritydone_table():
        success = False
    
    # 2. 创建扩展表
    if not create_extension_table():
        success = False
    
    # 3. 创建统一视图
    if not create_unified_view():
        success = False
    
    if success:
        logger.info("✅ 统一批次管理数据库结构初始化完成")
    else:
        logger.error("❌ 统一批次管理数据库结构初始化失败")
    
    return success

if __name__ == "__main__":
    # 如果直接运行，需要Flask应用上下文
    from app import create_app
    app = create_app()
    with app.app_context():
        initialize_unified_management()
