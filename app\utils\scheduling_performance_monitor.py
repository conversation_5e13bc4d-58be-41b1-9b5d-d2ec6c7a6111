import time
import psutil
import threading
from typing import Dict, Any, Optional, List
from contextlib import contextmanager
import logging
from datetime import datetime, timedelta
import os
import json

class SchedulingPerformanceMonitor:
    """排产性能监控"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metrics = {}
        self.lock = threading.Lock()
        self.process = psutil.Process()
        self.start_time = time.time()
        
    @contextmanager
    def monitor_execution(self, operation_name: str, context: Dict[str, Any] = None):
        """监控执行性能"""
        start_time = time.time()
        start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        start_cpu_times = self.process.cpu_times()
        
        try:
            self.logger.info(f"📊 开始监控: {operation_name}")
            yield
            
        finally:
            end_time = time.time()
            end_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            end_cpu_times = self.process.cpu_times()
            
            execution_time = end_time - start_time
            memory_usage = end_memory - start_memory
            cpu_time_used = (end_cpu_times.user - start_cpu_times.user) + (end_cpu_times.system - start_cpu_times.system)
            
            # 记录性能指标
            metrics = {
                'operation': operation_name,
                'execution_time': execution_time,
                'memory_usage': memory_usage,
                'cpu_time': cpu_time_used,
                'start_memory': start_memory,
                'end_memory': end_memory,
                'memory_peak': end_memory,
                'timestamp': time.time(),
                'context': context or {}
            }
            
            with self.lock:
                if operation_name not in self.metrics:
                    self.metrics[operation_name] = []
                self.metrics[operation_name].append(metrics)
                
                # 保留最近100条记录
                if len(self.metrics[operation_name]) > 100:
                    self.metrics[operation_name] = self.metrics[operation_name][-100:]
            
            self.logger.info(f"📊 监控完成: {operation_name}, 耗时: {execution_time:.2f}s, 内存: {memory_usage:.2f}MB, CPU: {cpu_time_used:.2f}s")
    
    def get_performance_summary(self, operation_name: str) -> Dict[str, Any]:
        """获取性能摘要"""
        with self.lock:
            if operation_name not in self.metrics:
                return {}
            
            records = self.metrics[operation_name]
            if not records:
                return {}
            
            execution_times = [r['execution_time'] for r in records]
            memory_usages = [r['memory_usage'] for r in records]
            cpu_times = [r['cpu_time'] for r in records]
            
            return {
                'operation': operation_name,
                'count': len(records),
                'avg_execution_time': sum(execution_times) / len(execution_times),
                'max_execution_time': max(execution_times),
                'min_execution_time': min(execution_times),
                'avg_memory_usage': sum(memory_usages) / len(memory_usages),
                'max_memory_usage': max(memory_usages),
                'avg_cpu_time': sum(cpu_times) / len(cpu_times),
                'max_cpu_time': max(cpu_times),
                'last_execution': records[-1]['timestamp']
            }
    
    def get_all_performance_summaries(self) -> Dict[str, Dict[str, Any]]:
        """获取所有操作的性能摘要"""
        summaries = {}
        with self.lock:
            for operation_name in self.metrics.keys():
                summaries[operation_name] = self.get_performance_summary(operation_name)
        return summaries
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统性能指标"""
        try:
            # CPU信息
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_total_gb = memory.total / 1024 / 1024 / 1024
            memory_available_gb = memory.available / 1024 / 1024 / 1024
            memory_used_gb = memory.used / 1024 / 1024 / 1024
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            disk_total_gb = disk.total / 1024 / 1024 / 1024
            disk_free_gb = disk.free / 1024 / 1024 / 1024
            disk_used_gb = disk.used / 1024 / 1024 / 1024
            
            # 进程信息
            process_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            process_cpu_percent = self.process.cpu_percent()
            
            return {
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count
                },
                'memory': {
                    'total_gb': memory_total_gb,
                    'available_gb': memory_available_gb,
                    'used_gb': memory_used_gb,
                    'percent': memory.percent
                },
                'disk': {
                    'total_gb': disk_total_gb,
                    'free_gb': disk_free_gb,
                    'used_gb': disk_used_gb,
                    'percent': disk.percent
                },
                'process': {
                    'memory_mb': process_memory,
                    'cpu_percent': process_cpu_percent,
                    'threads': self.process.num_threads(),
                    'uptime_seconds': time.time() - self.start_time
                },
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取系统指标失败: {e}")
            return {}
    
    def monitor_database_performance(self) -> Dict[str, Any]:
        """监控数据库性能"""
        try:
            from app import db
            from sqlalchemy import text
            
            # 获取数据库连接状态
            result = db.session.execute(text("""
                SHOW STATUS WHERE 
                Variable_name IN ('Connections', 'Threads_connected', 'Threads_running',
                                'Queries', 'Slow_queries', 'Questions',
                                'Innodb_buffer_pool_pages_total', 'Innodb_buffer_pool_pages_free')
            """))
            
            db_status = {}
            for row in result:
                db_status[row[0]] = row[1]
            
            # 获取数据库大小信息
            result = db.session.execute(text("""
                SELECT 
                    table_schema,
                    SUM(data_length + index_length) / 1024 / 1024 AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                GROUP BY table_schema
            """))
            
            db_size = 0
            for row in result:
                db_size = float(row[1])
            
            return {
                'connections': int(db_status.get('Connections', 0)),
                'threads_connected': int(db_status.get('Threads_connected', 0)),
                'threads_running': int(db_status.get('Threads_running', 0)),
                'queries': int(db_status.get('Queries', 0)),
                'slow_queries': int(db_status.get('Slow_queries', 0)),
                'database_size_mb': db_size,
                'buffer_pool_total': int(db_status.get('Innodb_buffer_pool_pages_total', 0)),
                'buffer_pool_free': int(db_status.get('Innodb_buffer_pool_pages_free', 0)),
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取数据库性能指标失败: {e}")
            return {}
    
    def analyze_performance_trends(self, operation_name: str, time_window_hours: int = 24) -> Dict[str, Any]:
        """分析性能趋势"""
        with self.lock:
            if operation_name not in self.metrics:
                return {}
            
            current_time = time.time()
            time_threshold = current_time - (time_window_hours * 3600)
            
            # 过滤时间窗口内的记录
            recent_records = [
                r for r in self.metrics[operation_name]
                if r['timestamp'] >= time_threshold
            ]
            
            if len(recent_records) < 2:
                return {}
            
            # 计算趋势
            execution_times = [r['execution_time'] for r in recent_records]
            memory_usages = [r['memory_usage'] for r in recent_records]
            
            # 简单的线性趋势分析
            n = len(execution_times)
            x_values = list(range(n))
            
            # 执行时间趋势
            exec_time_slope = self._calculate_trend_slope(x_values, execution_times)
            memory_slope = self._calculate_trend_slope(x_values, memory_usages)
            
            return {
                'operation': operation_name,
                'time_window_hours': time_window_hours,
                'records_count': n,
                'execution_time_trend': 'improving' if exec_time_slope < -0.01 else 'degrading' if exec_time_slope > 0.01 else 'stable',
                'memory_trend': 'improving' if memory_slope < -0.01 else 'degrading' if memory_slope > 0.01 else 'stable',
                'exec_time_slope': exec_time_slope,
                'memory_slope': memory_slope,
                'first_record_time': recent_records[0]['timestamp'],
                'last_record_time': recent_records[-1]['timestamp']
            }
    
    def _calculate_trend_slope(self, x_values: List[float], y_values: List[float]) -> float:
        """计算趋势斜率"""
        if len(x_values) != len(y_values) or len(x_values) < 2:
            return 0.0
        
        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x2 = sum(x * x for x in x_values)
        
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator == 0:
            return 0.0
        
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        return slope
    
    def generate_performance_report(self, output_file: str = None) -> Dict[str, Any]:
        """生成性能报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'system_metrics': self.get_system_metrics(),
            'database_metrics': self.monitor_database_performance(),
            'operation_summaries': self.get_all_performance_summaries(),
            'trends': {}
        }
        
        # 添加趋势分析
        for operation_name in self.metrics.keys():
            trends = self.analyze_performance_trends(operation_name)
            if trends:
                report['trends'][operation_name] = trends
        
        # 保存到文件
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
                self.logger.info(f"📊 性能报告已保存到: {output_file}")
            except Exception as e:
                self.logger.error(f"❌ 保存性能报告失败: {e}")
        
        return report
    
    def clear_metrics(self, operation_name: str = None):
        """清理性能指标"""
        with self.lock:
            if operation_name:
                if operation_name in self.metrics:
                    del self.metrics[operation_name]
                    self.logger.info(f"🗑️ 已清理操作指标: {operation_name}")
            else:
                self.metrics.clear()
                self.logger.info("🗑️ 已清理所有性能指标")
    
    def export_metrics(self, operation_name: str, format_type: str = 'json') -> str:
        """导出性能指标"""
        with self.lock:
            if operation_name not in self.metrics:
                return ""
            
            data = self.metrics[operation_name]
            
            if format_type.lower() == 'json':
                return json.dumps(data, indent=2)
            elif format_type.lower() == 'csv':
                # 简单的CSV格式
                if not data:
                    return ""
                
                headers = ['timestamp', 'execution_time', 'memory_usage', 'cpu_time']
                csv_lines = [','.join(headers)]
                
                for record in data:
                    row = [
                        str(record['timestamp']),
                        str(record['execution_time']),
                        str(record['memory_usage']),
                        str(record['cpu_time'])
                    ]
                    csv_lines.append(','.join(row))
                
                return '\n'.join(csv_lines)
            else:
                return str(data)
    
    def set_alert_thresholds(self, operation_name: str, max_execution_time: float = None,
                            max_memory_usage: float = None) -> bool:
        """设置性能告警阈值"""
        # 这里可以实现告警阈值设置逻辑
        # 目前只是记录日志
        self.logger.info(f"📊 设置性能告警阈值 - {operation_name}: "
                        f"执行时间: {max_execution_time}s, 内存: {max_memory_usage}MB")
        return True
    
    def check_performance_alerts(self, operation_name: str) -> List[Dict[str, Any]]:
        """检查性能告警"""
        alerts = []
        
        summary = self.get_performance_summary(operation_name)
        if not summary:
            return alerts
        
        # 示例告警规则
        if summary['avg_execution_time'] > 30:  # 超过30秒
            alerts.append({
                'type': 'execution_time',
                'severity': 'warning',
                'message': f"操作 {operation_name} 平均执行时间过长: {summary['avg_execution_time']:.2f}s",
                'value': summary['avg_execution_time'],
                'threshold': 30
            })
        
        if summary['max_memory_usage'] > 500:  # 超过500MB
            alerts.append({
                'type': 'memory_usage',
                'severity': 'warning',
                'message': f"操作 {operation_name} 内存使用过高: {summary['max_memory_usage']:.2f}MB",
                'value': summary['max_memory_usage'],
                'threshold': 500
            })
        
        return alerts


# 全局实例
_performance_monitor = None

def get_performance_monitor() -> SchedulingPerformanceMonitor:
    """获取性能监控器实例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = SchedulingPerformanceMonitor()
    return _performance_monitor 