#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试定时任务功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import SchedulingTasks
from datetime import datetime

def test_scheduled_tasks():
    """测试定时任务数据库功能"""
    app = create_app()
    
    with app.app_context():
        print("🔍 检查定时任务数据库表...")
        
        try:
            # 检查表是否存在
            result = db.session.execute("SHOW TABLES LIKE 'scheduling_tasks'")
            table_exists = result.fetchone() is not None
            
            if table_exists:
                print("✅ scheduling_tasks 表存在")
                
                # 查询现有任务
                tasks = SchedulingTasks.query.all()
                print(f"📋 当前数据库中有 {len(tasks)} 个定时任务")
                
                for task in tasks:
                    print(f"  - {task.task_name} ({task.task_type}) - 启用: {task.enabled}")
                
                # 测试创建任务
                print("\n🧪 测试创建定时任务...")
                test_task = SchedulingTasks(
                    task_name="测试任务",
                    task_type="daily",
                    schedule_config='{"name": "测试任务", "type": "daily", "hour": 9, "minute": 0}',
                    enabled=True,
                    created_by="test_user"
                )
                
                db.session.add(test_task)
                db.session.commit()
                print(f"✅ 测试任务创建成功，ID: {test_task.id}")
                
                # 删除测试任务
                db.session.delete(test_task)
                db.session.commit()
                print("🗑️ 测试任务已删除")
                
            else:
                print("❌ scheduling_tasks 表不存在")
                print("🔧 尝试创建表...")
                db.create_all()
                print("✅ 数据库表已创建")
                
        except Exception as e:
            print(f"❌ 数据库操作失败: {e}")
            
        print("\n🔍 检查后端调度器服务...")
        try:
            scheduler_service = app.extensions.get('background_scheduler')
            if scheduler_service:
                print("✅ 后端调度器服务已注册")
                print(f"📊 调度器运行状态: {scheduler_service.is_running}")
                print(f"📋 内存中任务数量: {len(scheduler_service.tasks)}")
            else:
                print("❌ 后端调度器服务未注册")
        except Exception as e:
            print(f"❌ 检查调度器服务失败: {e}")

if __name__ == "__main__":
    test_scheduled_tasks()
