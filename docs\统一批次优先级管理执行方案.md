# 统一批次优先级管理执行方案 v1.0

## 📋 项目概述

基于现有架构，实现跨表统一批次管理功能，支持对等待批次(`et_wait_lot`)和已处理批次(`lotprioritydone`)的统一查看和管理。

## 🎯 实施目标

1. **统一界面**：在现有`semi_auto.html`基础上扩展统一管理功能
2. **跨表查询**：通过API实现`et_wait_lot`和`lotprioritydone`的联合查询
3. **批量操作**：支持批量优先级调整、失败批次救援等功能
4. **状态流转**：支持批次在不同状态间的转换和管理
5. **向后兼容**：保持现有功能完全可用

## 🗂️ 文件结构

```
app/
├── api_v2/production/
│   ├── unified_lot_management_api.py     # 🆕 统一批次管理API
│   └── __init__.py                       # 🔧 更新蓝图注册
├── static/js/
│   └── unified_lot_management.js         # 🆕 前端统一管理功能
├── templates/production/
│   └── semi_auto.html                    # 🔧 添加JavaScript引用
tools/
└── add_unified_lot_fields.py             # 🆕 数据库扩展脚本
docs/
└── 统一批次优先级管理执行方案.md         # 📖 本文档
```

## 🚀 实施步骤

### 阶段一：数据库准备 (预计1小时)

#### 1.1 执行数据库扩展脚本

```bash
cd /d/1-My Work/25-AI 相关/7-APS-ICP/AEC-FT-Intelligent-Commander-Platform-1.3

# 执行数据库扩展
python tools/add_unified_lot_fields.py
```

**预期结果**：
- `et_wait_lot`表增加`status`、`priority_level`、`last_updated`字段
- 创建相应的索引
- 验证表结构完整性

#### 1.2 验证数据库结构

```sql
-- 检查et_wait_lot表结构
DESCRIBE et_wait_lot;

-- 检查lotprioritydone表结构
DESCRIBE lotprioritydone;

-- 验证关键字段
SELECT COUNT(*) FROM et_wait_lot WHERE status = 'WAITING';
SELECT COUNT(*) FROM lotprioritydone WHERE SCHEDULING_STATUS IN ('SUCCESS', 'FAILED', 'MANUAL_ADJUSTED');
```

### 阶段二：后端API开发 (预计3小时)

#### 2.1 API功能验证

```bash
# 重启应用服务器
python run.py

# 检查API注册
curl -X GET "http://localhost:5000/api/v2/production/unified-lot-management/status-counts"
```

#### 2.2 API接口测试

**获取统一批次列表**：
```bash
curl -X GET "http://localhost:5000/api/v2/production/unified-lot-management/lots?status=all&page=1&page_size=10"
```

**批量更新优先级**：
```bash
curl -X POST "http://localhost:5000/api/v2/production/unified-lot-management/priority/batch-update" \
  -H "Content-Type: application/json" \
  -d '{
    "updates": [
      {"lot_id": "LOT001", "priority": 1, "status": "WAITING"},
      {"lot_id": "LOT002", "priority": 2, "status": "SUCCESS"}
    ]
  }'
```

**救援失败批次**：
```bash
curl -X POST "http://localhost:5000/api/v2/production/unified-lot-management/rescue-failed" \
  -H "Content-Type: application/json" \
  -d '{"lot_ids": ["FAILED_LOT001", "FAILED_LOT002"]}'
```

### 阶段三：前端界面集成 (预计2小时)

#### 3.1 功能验证

1. **访问排产页面**：
   ```
   http://localhost:5000/production/semi-auto
   ```

2. **测试统一管理功能**：
   - 点击"切换模式"按钮
   - 验证统一批次管理面板显示
   - 测试状态筛选功能
   - 测试搜索功能

#### 3.2 交互功能测试

1. **状态筛选**：
   - 点击"全部批次"、"等待排产"、"排产成功"等标签
   - 验证数据正确筛选

2. **批量操作**：
   - 选择多个批次
   - 测试批量优先级调整
   - 测试失败批次救援

3. **单个操作**：
   - 测试单个批次优先级修改
   - 测试下拉菜单操作

### 阶段四：集成测试 (预计2小时)

#### 4.1 数据一致性测试

**测试场景1：等待批次优先级调整**
```python
# 测试脚本
import requests

# 1. 获取等待批次
response = requests.get('http://localhost:5000/api/v2/production/unified-lot-management/lots?status=WAITING')
waiting_lots = response.json()['lots']

# 2. 调整优先级
if waiting_lots:
    lot_id = waiting_lots[0]['LOT_ID']
    update_response = requests.post(
        'http://localhost:5000/api/v2/production/unified-lot-management/priority/batch-update',
        json={'updates': [{'lot_id': lot_id, 'priority': 1, 'status': 'WAITING'}]}
    )
    print("优先级更新结果:", update_response.json())
```

**测试场景2：失败批次救援**
```python
# 1. 获取失败批次
response = requests.get('http://localhost:5000/api/v2/production/unified-lot-management/lots?status=FAILED')
failed_lots = response.json()['lots']

# 2. 救援失败批次
if failed_lots:
    lot_ids = [lot['LOT_ID'] for lot in failed_lots[:2]]
    rescue_response = requests.post(
        'http://localhost:5000/api/v2/production/unified-lot-management/rescue-failed',
        json={'lot_ids': lot_ids}
    )
    print("救援结果:", rescue_response.json())
```

#### 4.2 性能测试

**测试大量数据查询**：
```python
import time

# 测试统一查询性能
start_time = time.time()
response = requests.get('http://localhost:5000/api/v2/production/unified-lot-management/lots?page=1&page_size=100')
end_time = time.time()

print(f"查询100条记录耗时: {end_time - start_time:.2f}秒")
print(f"返回记录数: {len(response.json().get('lots', []))}")
```

### 阶段五：用户验收测试 (预计1小时)

#### 5.1 功能验收清单

- [ ] **基础功能**
  - [ ] 统一批次列表正确显示
  - [ ] 状态筛选正常工作
  - [ ] 搜索功能正常
  - [ ] 分页功能正常

- [ ] **批量操作**
  - [ ] 批量选择功能
  - [ ] 批量优先级调整
  - [ ] 失败批次救援

- [ ] **界面交互**
  - [ ] 模式切换正常
  - [ ] 数据刷新正常
  - [ ] 错误提示友好

- [ ] **数据一致性**
  - [ ] 跨表数据同步正确
  - [ ] 状态转换正确
  - [ ] 优先级更新正确

#### 5.2 用户体验测试

1. **易用性测试**：
   - 新用户能否快速理解功能
   - 操作流程是否直观
   - 错误处理是否友好

2. **效率测试**：
   - 批量操作是否比单个操作高效
   - 数据加载速度是否满足要求
   - 界面响应是否流畅

## 📊 预期收益

### 量化指标

1. **操作效率提升**：
   - 批量优先级调整：从逐个调整到批量操作，效率提升70%
   - 失败批次救援：从手动查找到一键救援，效率提升80%
   - 跨状态查看：从多页面跳转到统一界面，效率提升60%

2. **数据一致性**：
   - 跨表数据同步准确率：99.9%
   - 状态转换正确率：100%
   - 优先级更新实时性：<1秒

3. **用户体验**：
   - 学习成本：降低50%（基于现有界面扩展）
   - 操作步骤：减少60%（统一界面操作）
   - 错误率：降低40%（统一数据验证）

### 业务价值

1. **管理效率**：
   - 统一视图管理所有状态批次
   - 快速识别和处理问题批次
   - 减少人工干预和错误

2. **数据质量**：
   - 统一的数据验证和处理
   - 减少数据不一致问题
   - 完整的操作审计跟踪

3. **系统可维护性**：
   - 基于现有架构，维护成本低
   - 统一的API接口，便于扩展
   - 良好的错误处理和日志记录

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 数据库连接问题

**问题**：执行数据库扩展脚本时连接失败
**解决方案**：
```bash
# 检查数据库配置
python -c "from app.utils.mysql_config_helper import get_mysql_config; print(get_mysql_config())"

# 测试连接
python -c "
import mysql.connector
from app.utils.mysql_config_helper import get_mysql_config
config = get_mysql_config()
conn = mysql.connector.connect(**config)
print('连接成功')
conn.close()
"
```

#### 2. API注册失败

**问题**：统一批次管理API无法访问
**解决方案**：
```python
# 检查蓝图注册
from app.api_v2.production import production_bp
print(production_bp.url_map)

# 检查路由
from app.api_v2.production.unified_lot_management_api import unified_lot_bp
print(unified_lot_bp.url_map)
```

#### 3. 前端JavaScript错误

**问题**：统一管理功能无法正常工作
**解决方案**：
```javascript
// 浏览器控制台检查
console.log('统一批次管理器:', window.unifiedLotManager);

// 检查API调用
fetch('/api/v2/production/unified-lot-management/status-counts')
  .then(response => response.json())
  .then(data => console.log('API测试:', data));
```

#### 4. 数据不一致问题

**问题**：跨表查询结果不准确
**解决方案**：
```sql
-- 检查数据一致性
SELECT 'et_wait_lot' as table_name, COUNT(*) as count FROM et_wait_lot WHERE GOOD_QTY > 0
UNION ALL
SELECT 'lotprioritydone' as table_name, COUNT(*) as count FROM lotprioritydone WHERE LOT_ID IS NOT NULL;

-- 检查重复数据
SELECT LOT_ID, COUNT(*) 
FROM (
    SELECT LOT_ID FROM et_wait_lot WHERE GOOD_QTY > 0
    UNION ALL
    SELECT LOT_ID FROM lotprioritydone WHERE LOT_ID IS NOT NULL
) as unified_lots
GROUP BY LOT_ID
HAVING COUNT(*) > 1;
```

## 📈 监控和维护

### 日志监控

```python
# 监控API调用
import logging
logger = logging.getLogger('unified_lot_management')
logger.info('统一批次管理API调用统计')

# 监控数据库性能
import time
start_time = time.time()
# 执行查询
query_time = time.time() - start_time
logger.info(f'统一查询耗时: {query_time:.2f}秒')
```

### 性能指标

1. **API响应时间**：
   - 目标：< 500ms
   - 监控：每日查询平均响应时间
   - 告警：超过1秒触发告警

2. **数据库查询性能**：
   - 目标：跨表查询 < 300ms
   - 监控：慢查询日志
   - 优化：索引优化、查询优化

3. **内存使用**：
   - 目标：单次查询内存使用 < 100MB
   - 监控：内存使用峰值
   - 优化：分页查询、结果缓存

## 🎯 后续扩展计划

### 短期优化 (1-2周)

1. **UI优化**：
   - 添加拖拽排序功能
   - 优化移动端适配
   - 添加快捷键支持

2. **功能扩展**：
   - 批次依赖关系管理
   - 优先级计算规则配置
   - 批次分组管理

### 中期规划 (1-2个月)

1. **智能化功能**：
   - AI辅助优先级推荐
   - 异常批次自动识别
   - 性能预测分析

2. **集成优化**：
   - 与MES系统深度集成
   - 实时数据同步
   - 事件驱动更新

### 长期愿景 (3-6个月)

1. **平台化**：
   - 统一批次管理平台
   - 多工厂批次协调
   - 跨系统数据整合

2. **智能决策**：
   - 基于机器学习的排产优化
   - 预测性维护
   - 自适应调度策略

---

**文档版本**：v1.0  
**创建时间**：2025年1月16日  
**更新时间**：2025年1月16日  
**状态**：✅ 实施就绪 