#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建v_unified_lot_management统一视图
合并et_wait_lot和lotprioritydone的数据，提供统一的批次管理接口
"""

import logging
import mysql.connector
from mysql.connector import Error
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_mysql_config():
    """获取MySQL数据库配置"""
    return {
        'host': os.environ.get('DB_HOST', 'localhost'),
        'port': int(os.environ.get('DB_PORT', 3306)),
        'user': os.environ.get('DB_USER', 'root'),
        'password': os.environ.get('DB_PASSWORD', 'WWWwww123!'),
        'database': os.environ.get('DB_NAME', 'aps'),
        'charset': os.environ.get('DB_CHARSET', 'utf8mb4')
    }

def create_unified_view():
    """创建统一批次管理视图"""
    try:
        config = get_mysql_config()
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        logger.info("🔧 开始创建v_unified_lot_management统一视图...")
        
        # 检查必要的表是否存在
        required_tables = ['et_wait_lot', 'et_wait_lot_extension', 'lotprioritydone']
        for table in required_tables:
            cursor.execute(f"""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = DATABASE() AND table_name = '{table}'
            """)
            
            if cursor.fetchone()[0] == 0:
                logger.error(f"❌ 必要的表 {table} 不存在")
                return False
        
        # 删除已存在的视图
        logger.info("🗑️ 删除已存在的统一视图...")
        cursor.execute("DROP VIEW IF EXISTS v_unified_lot_management")
        
        # 创建统一视图
        # 在JOIN条件中使用COLLATE解决排序规则冲突
        create_view_sql = """
        CREATE VIEW v_unified_lot_management AS
        SELECT 
            'WAITING' as status,
            TRIM(w.LOT_ID) as LOT_ID,
            w.DEVICE,
            w.STAGE,
            CONVERT(w.GOOD_QTY, UNSIGNED) as GOOD_QTY,
            w.PROD_ID,
            w.CHIP_ID,
            w.PKG_PN,
            w.PO_ID,
            COALESCE(e.priority_level, 999) as PRIORITY,
            NULL as HANDLER_ID,
            NULL as comprehensive_score,
            NULL as FAILURE_REASON,
            NULL as match_type,
            NULL as processing_time,
            NULL as SESSION_ID,
            COALESCE(e.manual_priority, 0) as manual_priority,
            COALESCE(e.rescue_count, 0) as rescue_count,
            e.notes,
            'et_wait_lot' as source_table,
            COALESCE(e.updated_at, w.created_at) as updated_at,
            w.created_at as original_created_at
        FROM et_wait_lot w
        LEFT JOIN et_wait_lot_extension e ON TRIM(w.LOT_ID) COLLATE utf8mb4_unicode_ci = e.lot_id
        WHERE w.LOT_ID IS NOT NULL 
        AND TRIM(w.LOT_ID) != ''
        AND CONVERT(w.GOOD_QTY, UNSIGNED) > 0

        UNION ALL

        SELECT 
            IFNULL(SCHEDULING_STATUS, 'SUCCESS') as status,
            LOT_ID,
            DEVICE,
            STAGE,
            GOOD_QTY,
            PROD_ID,
            CHIP_ID,
            PKG_PN,
            PO_ID,
            PRIORITY,
            HANDLER_ID,
            comprehensive_score,
            FAILURE_REASON,
            match_type,
            processing_time,
            SESSION_ID,
            0 as manual_priority,
            0 as rescue_count,
            NULL as notes,
            'lotprioritydone' as source_table,
            updated_at,
            created_at as original_created_at
        FROM lotprioritydone
        WHERE LOT_ID IS NOT NULL
        AND TRIM(LOT_ID) != ''
        """
        
        cursor.execute(create_view_sql)
        logger.info("✅ v_unified_lot_management视图创建成功")
        
        # 提交事务
        conn.commit()
        logger.info("✅ 统一视图创建完成")
        
        return True
        
    except Error as e:
        logger.error(f"❌ 创建统一视图失败: {e}")
        return False
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def verify_unified_view():
    """验证统一视图"""
    try:
        config = get_mysql_config()
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        logger.info("🔍 验证统一视图...")
        
        # 检查视图是否存在
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.views 
            WHERE table_schema = DATABASE() AND table_name = 'v_unified_lot_management'
        """)
        
        if cursor.fetchone()[0] == 0:
            logger.error("❌ 统一视图不存在")
            return False
        
        # 检查视图数据统计
        cursor.execute("SELECT COUNT(*) FROM v_unified_lot_management")
        total_count = cursor.fetchone()[0]
        
        # 按状态统计
        cursor.execute("""
            SELECT 
                status,
                COUNT(*) as count,
                source_table
            FROM v_unified_lot_management 
            GROUP BY status, source_table
            ORDER BY status, source_table
        """)
        
        status_stats = cursor.fetchall()
        logger.info("📊 统一视图数据统计:")
        logger.info(f"   - 总记录数: {total_count}")
        
        for stat in status_stats:
            logger.info(f"   - {stat[0]} ({stat[2]}): {stat[1]} 条")
        
        # 检查字段完整性
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(LOT_ID) as has_lot_id,
                COUNT(DEVICE) as has_device,
                COUNT(STAGE) as has_stage,
                COUNT(GOOD_QTY) as has_qty
            FROM v_unified_lot_management
        """)
        
        field_stats = cursor.fetchone()
        logger.info("📊 字段完整性:")
        logger.info(f"   - 总记录: {field_stats[0]}")
        logger.info(f"   - 有LOT_ID: {field_stats[1]}")
        logger.info(f"   - 有DEVICE: {field_stats[2]}")
        logger.info(f"   - 有STAGE: {field_stats[3]}")
        logger.info(f"   - 有GOOD_QTY: {field_stats[4]}")
        
        # 检查样本数据
        cursor.execute("""
            SELECT 
                status,
                LOT_ID,
                DEVICE,
                STAGE,
                PRIORITY,
                source_table
            FROM v_unified_lot_management 
            ORDER BY 
                CASE status 
                    WHEN 'WAITING' THEN 1
                    WHEN 'SUCCESS' THEN 2
                    WHEN 'FAILED' THEN 3
                    WHEN 'MANUAL_ADJUSTED' THEN 4
                    ELSE 5
                END,
                PRIORITY ASC
            LIMIT 5
        """)
        
        samples = cursor.fetchall()
        logger.info("📊 样本数据:")
        for sample in samples:
            logger.info(f"   - {sample[0]}: {sample[1]} | {sample[2]} | {sample[3]} | P:{sample[4]} | {sample[5]}")
        
        # 检查性能
        cursor.execute("EXPLAIN SELECT * FROM v_unified_lot_management WHERE status = 'WAITING' LIMIT 10")
        explain_result = cursor.fetchall()
        logger.info("📊 查询执行计划:")
        for row in explain_result:
            logger.info(f"   - {row}")
        
        return True
        
    except Error as e:
        logger.error(f"❌ 验证统一视图失败: {e}")
        return False
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_unified_view_queries():
    """测试统一视图常用查询"""
    try:
        config = get_mysql_config()
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        logger.info("🧪 测试统一视图查询...")
        
        # 测试查询1：按状态筛选
        test_queries = [
            ("按状态筛选等待批次", "SELECT COUNT(*) FROM v_unified_lot_management WHERE status = 'WAITING'"),
            ("按状态筛选成功批次", "SELECT COUNT(*) FROM v_unified_lot_management WHERE status = 'SUCCESS'"),
            ("按优先级排序", "SELECT LOT_ID, PRIORITY FROM v_unified_lot_management WHERE status = 'WAITING' ORDER BY PRIORITY ASC LIMIT 3"),
            ("按设备筛选", "SELECT COUNT(*) FROM v_unified_lot_management WHERE DEVICE LIKE '%TR1%'"),
            ("手动优先级批次", "SELECT COUNT(*) FROM v_unified_lot_management WHERE manual_priority = TRUE"),
        ]
        
        for test_name, query in test_queries:
            try:
                cursor.execute(query)
                result = cursor.fetchall()
                logger.info(f"✅ {test_name}: {len(result)} 条结果")
                if result and len(result) <= 3:  # 只显示少量结果
                    for row in result:
                        logger.info(f"     {row}")
            except Error as e:
                logger.error(f"❌ {test_name} 查询失败: {e}")
        
        return True
        
    except Error as e:
        logger.error(f"❌ 测试查询失败: {e}")
        return False
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚀 开始创建v_unified_lot_management统一视图...")
    
    # 创建统一视图
    if create_unified_view():
        print("✅ 统一视图创建成功")
        
        # 验证统一视图
        if verify_unified_view():
            print("✅ 统一视图验证成功")
            
            # 测试查询
            if test_unified_view_queries():
                print("✅ 统一视图查询测试成功")
            else:
                print("❌ 统一视图查询测试失败")
        else:
            print("❌ 统一视图验证失败")
    else:
        print("❌ 统一视图创建失败") 