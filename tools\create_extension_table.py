#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建et_wait_lot_extension扩展表
为et_wait_lot表提供扩展管理功能，不修改原表结构
"""

import logging
import mysql.connector
from mysql.connector import Error
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_mysql_config():
    """获取MySQL数据库配置"""
    return {
        'host': os.environ.get('DB_HOST', 'localhost'),
        'port': int(os.environ.get('DB_PORT', 3306)),
        'user': os.environ.get('DB_USER', 'root'),
        'password': os.environ.get('DB_PASSWORD', 'WWWwww123!'),
        'database': os.environ.get('DB_NAME', 'aps'),
        'charset': os.environ.get('DB_CHARSET', 'utf8mb4')
    }

def create_extension_table():
    """创建et_wait_lot_extension扩展表"""
    try:
        # 获取数据库配置
        config = get_mysql_config()
        
        # 连接数据库
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        logger.info("🔧 开始创建et_wait_lot_extension扩展表...")
        
        # 检查et_wait_lot表是否存在
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'et_wait_lot'
        """)
        
        if cursor.fetchone()[0] == 0:
            logger.error("❌ et_wait_lot表不存在，无法创建扩展表")
            return False
        
        # 删除已存在的扩展表（如果存在）
        logger.info("🗑️ 删除已存在的扩展表...")
        cursor.execute("DROP TABLE IF EXISTS et_wait_lot_extension")
        
        # 创建扩展表 - 调整lot_id字段类型以匹配et_wait_lot.LOT_ID
        # 由于et_wait_lot.LOT_ID是TEXT类型，我们使用VARCHAR(255)以便建立索引
        create_table_sql = """
        CREATE TABLE et_wait_lot_extension (
            id INT AUTO_INCREMENT PRIMARY KEY,
            lot_id VARCHAR(255) NOT NULL COMMENT '批次号，关联et_wait_lot.LOT_ID',
            status VARCHAR(20) DEFAULT 'WAITING' COMMENT '批次状态',
            priority_level INT DEFAULT 999 COMMENT '优先级等级，数值越小优先级越高',
            manual_priority BOOLEAN DEFAULT FALSE COMMENT '是否手动设置优先级',
            rescue_count INT DEFAULT 0 COMMENT '救援次数',
            last_rescue_time TIMESTAMP NULL COMMENT '最后救援时间',
            notes TEXT COMMENT '备注信息',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_by VARCHAR(50) COMMENT '创建人',
            updated_by VARCHAR(50) COMMENT '更新人',
            
            -- 约束和索引
            UNIQUE KEY uk_lot_id (lot_id),
            KEY idx_status (status),
            KEY idx_priority_level (priority_level),
            KEY idx_updated_at (updated_at),
            KEY idx_rescue_count (rescue_count),
            KEY idx_status_priority (status, priority_level)
            
            -- 注意：由于et_wait_lot.LOT_ID是TEXT类型，无法创建外键约束
            -- 数据一致性通过应用层面保证
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
        COMMENT='et_wait_lot扩展表 - 存储批次管理扩展信息'
        """
        
        cursor.execute(create_table_sql)
        logger.info("✅ et_wait_lot_extension表创建成功")
        
        # 为现有的et_wait_lot数据创建扩展记录
        logger.info("🔧 为现有数据创建扩展记录...")
        
        # 插入现有批次的扩展记录
        insert_extension_sql = """
        INSERT IGNORE INTO et_wait_lot_extension (lot_id, status, priority_level, created_by)
        SELECT 
            TRIM(LOT_ID) as lot_id,
            'WAITING',
            999,
            'system'
        FROM et_wait_lot
        WHERE LOT_ID IS NOT NULL 
        AND TRIM(LOT_ID) != ''
        AND GOOD_QTY > 0
        """
        
        cursor.execute(insert_extension_sql)
        affected_rows = cursor.rowcount
        logger.info(f"✅ 为 {affected_rows} 个现有批次创建了扩展记录")
        
        # 创建数据一致性检查的触发器（模拟外键约束）
        logger.info("🔧 创建数据一致性检查触发器...")
        
        # 删除已存在的触发器
        cursor.execute("DROP TRIGGER IF EXISTS et_wait_lot_extension_consistency_check")
        
        # 创建插入/更新时的数据一致性检查触发器
        trigger_sql = """
        CREATE TRIGGER et_wait_lot_extension_consistency_check
        BEFORE INSERT ON et_wait_lot_extension
        FOR EACH ROW
        BEGIN
            DECLARE lot_exists INT DEFAULT 0;
            
            SELECT COUNT(*) INTO lot_exists 
            FROM et_wait_lot 
            WHERE TRIM(LOT_ID) = NEW.lot_id 
            AND GOOD_QTY > 0;
            
            IF lot_exists = 0 THEN
                SIGNAL SQLSTATE '45000' 
                SET MESSAGE_TEXT = 'LOT_ID not found in et_wait_lot or GOOD_QTY <= 0';
            END IF;
        END
        """
        
        cursor.execute(trigger_sql)
        logger.info("✅ 数据一致性检查触发器创建成功")
        
        # 提交事务
        conn.commit()
        logger.info("✅ 扩展表创建完成")
        
        return True
        
    except Error as e:
        logger.error(f"❌ 创建扩展表失败: {e}")
        if conn:
            conn.rollback()
        return False
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def verify_extension_table():
    """验证扩展表结构和数据"""
    try:
        config = get_mysql_config()
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        logger.info("🔍 验证扩展表结构...")
        
        # 检查表结构
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'et_wait_lot_extension'
            ORDER BY ORDINAL_POSITION
        """)
        
        columns = cursor.fetchall()
        if not columns:
            logger.error("❌ 扩展表不存在或无法访问")
            return False
            
        logger.info("📋 et_wait_lot_extension表结构:")
        for col in columns:
            logger.info(f"   - {col[0]} ({col[1]}) {'NULL' if col[2] == 'YES' else 'NOT NULL'} DEFAULT {col[3]} - {col[4]}")
        
        # 检查索引
        cursor.execute("""
            SELECT INDEX_NAME, COLUMN_NAME, NON_UNIQUE
            FROM information_schema.statistics 
            WHERE table_schema = DATABASE() 
            AND table_name = 'et_wait_lot_extension'
            ORDER BY INDEX_NAME, SEQ_IN_INDEX
        """)
        
        indexes = cursor.fetchall()
        logger.info("📋 扩展表索引:")
        for idx in indexes:
            unique_str = "UNIQUE" if idx[2] == 0 else "INDEX"
            logger.info(f"   - {unique_str} {idx[0]} ({idx[1]})")
        
        # 检查触发器
        cursor.execute("""
            SELECT TRIGGER_NAME, EVENT_MANIPULATION, ACTION_TIMING
            FROM information_schema.triggers
            WHERE trigger_schema = DATABASE() 
            AND event_object_table = 'et_wait_lot_extension'
        """)
        
        triggers = cursor.fetchall()
        if triggers:
            logger.info("📋 数据一致性触发器:")
            for trigger in triggers:
                logger.info(f"   - {trigger[0]} ({trigger[2]} {trigger[1]})")
        
        # 检查数据统计
        cursor.execute("SELECT COUNT(*) FROM et_wait_lot_extension")
        extension_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM et_wait_lot WHERE GOOD_QTY > 0 AND LOT_ID IS NOT NULL AND TRIM(LOT_ID) != ''")
        wait_lot_count = cursor.fetchone()[0]
        
        logger.info(f"📊 数据统计:")
        logger.info(f"   - et_wait_lot有效记录: {wait_lot_count}")
        logger.info(f"   - 扩展表记录: {extension_count}")
        
        if extension_count == wait_lot_count:
            logger.info("✅ 扩展表数据完整")
        else:
            logger.warning(f"⚠️ 扩展表数据不完整，差异: {wait_lot_count - extension_count}")
        
        # 检查样本数据
        cursor.execute("""
            SELECT e.lot_id, e.status, e.priority_level, w.DEVICE, w.GOOD_QTY
            FROM et_wait_lot_extension e
            LEFT JOIN et_wait_lot w ON TRIM(w.LOT_ID) = e.lot_id
            LIMIT 3
        """)
        
        samples = cursor.fetchall()
        if samples:
            logger.info("📊 关联数据样本:")
            for sample in samples:
                logger.info(f"   - LOT_ID: {sample[0]}, STATUS: {sample[1]}, PRIORITY: {sample[2]}, DEVICE: {sample[3]}, QTY: {sample[4]}")
        
        return True
        
    except Error as e:
        logger.error(f"❌ 验证扩展表失败: {e}")
        return False
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚀 开始创建et_wait_lot_extension扩展表...")
    
    # 创建扩展表
    if create_extension_table():
        print("✅ 扩展表创建成功")
        
        # 验证扩展表
        if verify_extension_table():
            print("✅ 扩展表验证成功")
        else:
            print("❌ 扩展表验证失败")
    else:
        print("❌ 扩展表创建失败") 