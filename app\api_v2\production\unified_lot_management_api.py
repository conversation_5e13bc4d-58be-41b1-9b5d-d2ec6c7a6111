#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一批次管理API - 基于扩展表架构
使用v_unified_lot_management视图和et_wait_lot_extension扩展表
提供统一的批次管理接口，不修改MES同步的et_wait_lot表
"""

import logging
from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from sqlalchemy import text
from app import db
from app.utils.db_helper import get_mysql_connection
from app.utils.api_config import get_api_route

logger = logging.getLogger(__name__)

# 创建蓝图
unified_lot_bp = Blueprint('unified_lot_management', __name__)

class UnifiedLotManagementService:
    """统一批次管理服务 - 基于扩展表架构"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_unified_lots_view(self, status_filter=None, search_text=None, page=1, page_size=50):
        """获取统一的批次视图（使用v_unified_lot_management视图）"""
        try:
            # 使用统一视图简化查询
            base_query = "SELECT * FROM v_unified_lot_management"
            
            # 构建条件
            conditions = []
            params = {}
            
            if status_filter and status_filter != 'all':
                conditions.append("status = :status_filter")
                params['status_filter'] = status_filter
            
            if search_text:
                conditions.append("""
                    (LOT_ID LIKE :search_text OR 
                     DEVICE LIKE :search_text OR 
                     PROD_ID LIKE :search_text OR 
                     HANDLER_ID LIKE :search_text)
                """)
                params['search_text'] = f'%{search_text}%'
            
            # 构建WHERE子句
            where_clause = ""
            if conditions:
                where_clause = " WHERE " + " AND ".join(conditions)
            
            # 添加排序和分页
            order_clause = """
                ORDER BY 
                    CASE status 
                        WHEN 'WAITING' THEN 1
                        WHEN 'SUCCESS' THEN 2
                        WHEN 'FAILED' THEN 3
                        WHEN 'MANUAL_ADJUSTED' THEN 4
                        ELSE 5
                    END,
                    PRIORITY ASC,
                    updated_at DESC
            """
            
            # 计算分页
            offset = (page - 1) * page_size
            limit_clause = f" LIMIT {page_size} OFFSET {offset}"
            
            # 构建最终查询
            final_query = base_query + where_clause + order_clause + limit_clause
            
            # 执行查询
            result = db.session.execute(text(final_query), params)
            
            # 获取列名
            columns = result.keys()
            
            # 转换为字典格式
            lots = []
            for row in result:
                lot_dict = dict(zip(columns, row))
                # 确保数据类型正确
                if lot_dict.get('GOOD_QTY'):
                    lot_dict['GOOD_QTY'] = int(lot_dict['GOOD_QTY'])
                if lot_dict.get('PRIORITY'):
                    lot_dict['PRIORITY'] = int(lot_dict['PRIORITY'])
                if lot_dict.get('manual_priority'):
                    lot_dict['manual_priority'] = bool(lot_dict['manual_priority'])
                
                lots.append(lot_dict)
            
            # 获取总计数
            count_query = f"SELECT COUNT(*) as total FROM v_unified_lot_management{where_clause}"
            count_result = db.session.execute(text(count_query), params)
            total = count_result.scalar()
            
            self.logger.info(f"统一视图查询成功: {len(lots)} 条记录，总数 {total}")
            
            return {
                'success': True,
                'data': lots,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total': total,
                    'pages': (total + page_size - 1) // page_size
                }
            }
            
        except Exception as e:
            self.logger.error(f"统一视图查询失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'pagination': {'page': page, 'page_size': page_size, 'total': 0, 'pages': 0}
            }
    
    def _get_status_counts(self):
        """获取各状态的批次统计"""
        try:
            query = """
                SELECT 
                    status,
                    COUNT(*) as count
                FROM v_unified_lot_management 
                GROUP BY status
            """
            
            result = db.session.execute(text(query))
            
            counts = {
                'WAITING': 0,
                'SUCCESS': 0,
                'FAILED': 0,
                'MANUAL_ADJUSTED': 0,
                'total': 0
            }
            
            for row in result:
                status, count = row
                if status in counts:
                    counts[status] = count
                counts['total'] += count
            
            return counts
            
        except Exception as e:
            self.logger.error(f"获取状态统计失败: {e}")
            return {'WAITING': 0, 'SUCCESS': 0, 'FAILED': 0, 'MANUAL_ADJUSTED': 0, 'total': 0}
    
    def batch_priority_update(self, updates, user_id):
        """批量更新优先级（使用扩展表）"""
        try:
            self.logger.info(f"用户 {user_id} 开始批量更新优先级: {len(updates)} 个批次")
            
            success_count = 0
            error_count = 0
            errors = []
            
            for update in updates:
                lot_id = update.get('lot_id')
                new_priority = update.get('priority')
                
                if not lot_id or new_priority is None:
                    error_count += 1
                    errors.append(f"批次 {lot_id}: 缺少必要参数")
                    continue
                
                try:
                    # 检查批次是否存在于等待队列
                    check_query = """
                        SELECT COUNT(*) FROM v_unified_lot_management 
                        WHERE LOT_ID = :lot_id AND status = 'WAITING'
                    """
                    
                    check_result = db.session.execute(text(check_query), {'lot_id': lot_id})
                    if check_result.scalar() == 0:
                        error_count += 1
                        errors.append(f"批次 {lot_id}: 不在等待队列中")
                        continue
                    
                    # 更新或插入扩展表记录
                    upsert_query = """
                        INSERT INTO et_wait_lot_extension (lot_id, priority_level, manual_priority, updated_by, updated_at)
                        VALUES (:lot_id, :priority, 1, :user_id, NOW())
                        ON DUPLICATE KEY UPDATE 
                            priority_level = VALUES(priority_level),
                            manual_priority = VALUES(manual_priority),
                            updated_by = VALUES(updated_by),
                            updated_at = VALUES(updated_at)
                    """
                    
                    db.session.execute(text(upsert_query), {
                        'lot_id': lot_id,
                        'priority': new_priority,
                        'user_id': user_id
                    })
                    
                    success_count += 1
                    self.logger.info(f"批次 {lot_id} 优先级更新为 {new_priority}")
                    
                except Exception as e:
                    error_count += 1
                    error_msg = f"批次 {lot_id}: {str(e)}"
                    errors.append(error_msg)
                    self.logger.error(error_msg)
            
            # 提交事务
            db.session.commit()
            
            self.logger.info(f"批量优先级更新完成: 成功 {success_count}, 失败 {error_count}")
            
            return {
                'success': True,
                'message': f'批量优先级更新完成',
                'results': {
                    'success_count': success_count,
                    'error_count': error_count,
                    'errors': errors
                }
            }
            
        except Exception as e:
            db.session.rollback()
            self.logger.error(f"批量优先级更新失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'results': {'success_count': 0, 'error_count': len(updates), 'errors': [str(e)]}
            }
    
    def rescue_failed_lots(self, lot_ids, user_id):
        """救援失败批次（从lotprioritydone移到扩展表）"""
        try:
            self.logger.info(f"用户 {user_id} 开始救援失败批次: {len(lot_ids)} 个批次")
            
            success_count = 0
            error_count = 0
            errors = []
            
            for lot_id in lot_ids:
                try:
                    # 检查批次是否为失败状态
                    check_query = """
                        SELECT COUNT(*) FROM lotprioritydone 
                        WHERE LOT_ID = :lot_id AND SCHEDULING_STATUS = 'FAILED'
                    """
                    
                    check_result = db.session.execute(text(check_query), {'lot_id': lot_id})
                    if check_result.scalar() == 0:
                        error_count += 1
                        errors.append(f"批次 {lot_id}: 不是失败状态")
                        continue
                    
                    # 获取失败批次的原始信息
                    failed_lot_query = """
                        SELECT LOT_ID, DEVICE, STAGE, GOOD_QTY, PROD_ID, CHIP_ID, PKG_PN, PO_ID
                        FROM lotprioritydone 
                        WHERE LOT_ID = :lot_id AND SCHEDULING_STATUS = 'FAILED'
                    """
                    
                    failed_lot_result = db.session.execute(text(failed_lot_query), {'lot_id': lot_id})
                    failed_lot = failed_lot_result.fetchone()
                    
                    if not failed_lot:
                        error_count += 1
                        errors.append(f"批次 {lot_id}: 无法获取失败批次信息")
                        continue
                    
                    # 检查et_wait_lot中是否存在对应记录
                    wait_lot_check = """
                        SELECT COUNT(*) FROM et_wait_lot 
                        WHERE TRIM(LOT_ID) = :lot_id
                    """
                    
                    wait_lot_result = db.session.execute(text(wait_lot_check), {'lot_id': lot_id})
                    
                    if wait_lot_result.scalar() > 0:
                        # 如果et_wait_lot中存在，创建或更新扩展表记录
                        rescue_query = """
                            INSERT INTO et_wait_lot_extension (
                                lot_id, status, priority_level, rescue_count, last_rescue_time, 
                                notes, updated_by, updated_at, created_by, created_at
                            )
                            VALUES (:lot_id, 'WAITING', 1, 1, NOW(), :notes, :user_id, NOW(), :user_id, NOW())
                            ON DUPLICATE KEY UPDATE 
                                status = 'WAITING',
                                priority_level = 1,
                                rescue_count = rescue_count + 1,
                                last_rescue_time = NOW(),
                                notes = CONCAT(IFNULL(notes, ''), :notes),
                                updated_by = VALUES(updated_by),
                                updated_at = VALUES(updated_at)
                        """
                        
                        rescue_notes = f"\\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 失败批次救援"
                        
                        db.session.execute(text(rescue_query), {
                            'lot_id': lot_id,
                            'notes': rescue_notes,
                            'user_id': user_id
                        })
                        
                        # 标记lotprioritydone中的记录为已救援
                        mark_rescued_query = """
                            UPDATE lotprioritydone 
                            SET SCHEDULING_STATUS = 'RESCUED',
                                updated_at = NOW()
                            WHERE LOT_ID = :lot_id AND SCHEDULING_STATUS = 'FAILED'
                        """
                        
                        db.session.execute(text(mark_rescued_query), {'lot_id': lot_id})
                        
                        success_count += 1
                        self.logger.info(f"批次 {lot_id} 救援成功")
                    else:
                        error_count += 1
                        errors.append(f"批次 {lot_id}: 在et_wait_lot中不存在，无法救援")
                    
                except Exception as e:
                    error_count += 1
                    error_msg = f"批次 {lot_id}: {str(e)}"
                    errors.append(error_msg)
                    self.logger.error(error_msg)
            
            # 提交事务
            db.session.commit()
            
            self.logger.info(f"失败批次救援完成: 成功 {success_count}, 失败 {error_count}")
            
            return {
                'success': True,
                'message': f'失败批次救援完成',
                'results': {
                    'success_count': success_count,
                    'error_count': error_count,
                    'errors': errors
                }
            }
            
        except Exception as e:
            db.session.rollback()
            self.logger.error(f"失败批次救援失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'results': {'success_count': 0, 'error_count': len(lot_ids), 'errors': [str(e)]}
            }

# 初始化服务
unified_service = UnifiedLotManagementService()

# API路由定义
@unified_lot_bp.route(get_api_route('production/unified-lot-management/lots'), methods=['GET'])
@login_required
def get_unified_lots():
    """获取统一的批次列表"""
    try:
        # 获取请求参数
        status_filter = request.args.get('status', 'all')
        search_text = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 50))
        
        # 调用服务获取数据
        result = unified_service.get_unified_lots_view(
            status_filter=status_filter,
            search_text=search_text,
            page=page,
            page_size=page_size
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"获取统一批次列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': [],
            'pagination': {'page': 1, 'page_size': 50, 'total': 0, 'pages': 0}
        }), 500

@unified_lot_bp.route(get_api_route('production/unified-lot-management/priority/batch-update'), methods=['POST'])
@login_required
def batch_update_priority():
    """批量更新优先级"""
    try:
        data = request.get_json()
        updates = data.get('updates', [])
        user_id = current_user.username if current_user.is_authenticated else 'anonymous'
        
        if not updates:
            return jsonify({
                'success': False,
                'error': '更新数据不能为空',
                'results': {'success_count': 0, 'error_count': 0, 'errors': ['更新数据不能为空']}
            }), 400
        
        result = unified_service.batch_priority_update(updates, user_id)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"批量更新优先级失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'results': {'success_count': 0, 'error_count': 0, 'errors': [str(e)]}
        }), 500

@unified_lot_bp.route(get_api_route('production/unified-lot-management/rescue-failed'), methods=['POST'])
@login_required
def rescue_failed_lots():
    """救援失败批次"""
    try:
        data = request.get_json()
        lot_ids = data.get('lot_ids', [])
        user_id = current_user.username if current_user.is_authenticated else 'anonymous'
        
        if not lot_ids:
            return jsonify({
                'success': False,
                'error': '批次ID列表不能为空',
                'results': {'success_count': 0, 'error_count': 0, 'errors': ['批次ID列表不能为空']}
            }), 400
        
        result = unified_service.rescue_failed_lots(lot_ids, user_id)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"救援失败批次失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'results': {'success_count': 0, 'error_count': 0, 'errors': [str(e)]}
        }), 500

@unified_lot_bp.route(get_api_route('production/unified-lot-management/status-counts'), methods=['GET'])
@login_required
def get_status_counts():
    """获取各状态的批次统计"""
    try:
        counts = unified_service._get_status_counts()
        return jsonify({
            'success': True,
            'data': counts
        })
        
    except Exception as e:
        logger.error(f"获取状态统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': {'WAITING': 0, 'SUCCESS': 0, 'FAILED': 0, 'MANUAL_ADJUSTED': 0, 'total': 0}
        }), 500

@unified_lot_bp.route(get_api_route('production/unified-lot-management/extension/update'), methods=['POST'])
@login_required
def update_extension_record():
    """更新扩展表记录"""
    try:
        data = request.get_json()
        lot_id = data.get('lot_id')
        updates = data.get('updates', {})
        user_id = current_user.username if current_user.is_authenticated else 'anonymous'
        
        if not lot_id:
            return jsonify({
                'success': False,
                'error': '批次ID不能为空'
            }), 400
        
        # 构建更新字段
        update_fields = []
        params = {'lot_id': lot_id, 'user_id': user_id}
        
        if 'priority_level' in updates:
            update_fields.append("priority_level = :priority_level")
            params['priority_level'] = updates['priority_level']
            update_fields.append("manual_priority = 1")
        
        if 'notes' in updates:
            update_fields.append("notes = :notes")
            params['notes'] = updates['notes']
        
        if 'status' in updates:
            update_fields.append("status = :status")
            params['status'] = updates['status']
        
        if not update_fields:
            return jsonify({
                'success': False,
                'error': '没有要更新的字段'
            }), 400
        
        # 执行更新
        update_query = f"""
            INSERT INTO et_wait_lot_extension (lot_id, updated_by, updated_at, created_by, created_at)
            VALUES (:lot_id, :user_id, NOW(), :user_id, NOW())
            ON DUPLICATE KEY UPDATE 
                {', '.join(update_fields)},
                updated_by = VALUES(updated_by),
                updated_at = NOW()
        """
        
        db.session.execute(text(update_query), params)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'批次 {lot_id} 扩展信息更新成功'
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新扩展记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 导出蓝图
__all__ = ['unified_lot_bp'] 