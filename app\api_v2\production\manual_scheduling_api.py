#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动排产API接口 - 优化版
对接智能排产算法提供前端调用，统一使用execute_optimized_scheduling算法
"""

import logging
import time
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user

# 导入增强的并发控制和事务管理
from app.utils.enhanced_scheduling_lock import get_scheduling_lock_manager, get_scheduling_history_manager

# 导入性能监控
from app.utils.scheduling_performance_monitor import get_performance_monitor

logger = logging.getLogger(__name__)

def _convert_priority_to_int(priority_value) -> int:
    """
    将优先级值转换为整数
    
    Args:
        priority_value: 优先级值（可能是字符串、数字或None）
        
    Returns:
        int: 转换后的整数优先级（默认为1）
    """
    if priority_value is None:
        return 1
        
    if isinstance(priority_value, (int, float)):
        return int(priority_value)
        
    if isinstance(priority_value, str):
        priority_str = priority_value.strip()
        if not priority_str or priority_str.lower() in ['n', 'none', '']:
            return 1
        try:
            return int(float(priority_str))
        except (ValueError, TypeError):
            return 1
            
    return 1

def save_scheduling_results_enhanced(scheduled_lots, session_id):
    """增强的排产结果保存逻辑 - 简化版"""
    from app import db
    from sqlalchemy import text
    
    try:
        # 1. 清空现有数据
        db.session.execute(text("DELETE FROM lotprioritydone"))
        logger.info("✅ 已清空现有排产数据")
        
        # 2. 批量插入新数据
        saved_count = 0
        for lot in scheduled_lots:
            # 添加会话ID
            lot['SESSION_ID'] = session_id
            
            # 准备插入数据
            insert_query = text("""
                INSERT INTO lotprioritydone (
                    PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                    PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE,
                    WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                    RELEASE_TIME, FAC_ID, CREATE_TIME,
                    comprehensive_score, processing_time, changeover_time,
                    algorithm_version, match_type, priority_score,
                    estimated_hours, equipment_status, selection_reason,
                    SCHEDULING_STATUS, FAILURE_REASON, FAILURE_DETAILS, SESSION_ID
                ) VALUES (
                    :priority, :handler_id, :lot_id, :lot_type, :good_qty,
                    :prod_id, :device, :chip_id, :pkg_pn, :po_id, :stage,
                    :wip_state, :proc_state, :hold_state, :flow_id, :flow_ver,
                    :release_time, :fac_id, NOW(),
                    :comprehensive_score, :processing_time, :changeover_time,
                    :algorithm_version, :match_type, :priority_score,
                    :estimated_hours, :equipment_status, :selection_reason,
                    :scheduling_status, :failure_reason, :failure_details, :session_id
                )
            """)
            
            # 准备数据
            insert_data = {
                'priority': lot.get('PRIORITY', saved_count + 1),
                'handler_id': lot.get('HANDLER_ID', ''),
                'lot_id': lot.get('LOT_ID', ''),
                'lot_type': lot.get('LOT_TYPE', ''),
                'good_qty': lot.get('GOOD_QTY', 0),
                'prod_id': lot.get('PROD_ID', ''),
                'device': lot.get('DEVICE', ''),
                'chip_id': lot.get('CHIP_ID', ''),
                'pkg_pn': lot.get('PKG_PN', ''),
                'po_id': lot.get('PO_ID', ''),
                'stage': lot.get('STAGE', ''),
                'wip_state': lot.get('WIP_STATE', ''),
                'proc_state': lot.get('PROC_STATE', ''),
                'hold_state': lot.get('HOLD_STATE', 0),
                'flow_id': lot.get('FLOW_ID', ''),
                'flow_ver': lot.get('FLOW_VER', ''),
                'release_time': lot.get('RELEASE_TIME') or None,
                'fac_id': lot.get('FAC_ID', ''),
                'comprehensive_score': lot.get('comprehensive_score', 0.0),
                'processing_time': lot.get('processing_time', 0.0),
                'changeover_time': lot.get('changeover_time', 0.0),
                'algorithm_version': lot.get('algorithm_version', 'v3.0-enhanced'),
                'match_type': lot.get('match_type', ''),
                'priority_score': lot.get('priority_score', 0.0),
                'estimated_hours': lot.get('estimated_hours', 0.0),
                'equipment_status': lot.get('equipment_status', ''),
                'selection_reason': lot.get('selection_reason', ''),
                'scheduling_status': 'SUCCESS',
                'failure_reason': '',
                'failure_details': None,  # 修复：使用NULL而不是空字符串
                'session_id': session_id
            }
            
            # 执行插入
            db.session.execute(insert_query, insert_data)
            saved_count += 1
        
        # 3. 提交事务
        db.session.commit()
        
        # 4. 验证插入结果
        count = db.session.execute(text("SELECT COUNT(*) FROM lotprioritydone")).scalar()
        if count != len(scheduled_lots):
            logger.warning(f"⚠️ 数据插入验证警告: 期望{len(scheduled_lots)}条, 实际{count}条")
        
        logger.info(f"✅ 增强排产结果保存成功: {saved_count}条记录")
        return True
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 增强排产结果保存异常: {e}")
        raise

# 创建蓝图
manual_scheduling_api = Blueprint('manual_scheduling_api', __name__)

@manual_scheduling_api.route('/api/v2/production/execute-manual-scheduling', methods=['POST'])
@login_required
def execute_manual_scheduling():
    """
    执行智能排产算法 - 优化版
    
    🚀 核心改进：统一使用execute_optimized_scheduling算法
    📊 增强调试：详细记录批次处理过程
    🔧 错误优化：完善异常处理和失败追踪
    
    数据输入源:
    - ET_WAIT_LOT: 待排产批次
    - EQP_STATUS: 设备状态
    - ET_UPH_EQP: 产能数据
    - ET_FT_TEST_SPEC: 测试规范
    - et_recipe_file: 设备配方文件
    
    请求体:
    {
        "algorithm": "intelligent|deadline|product|value",
        "optimization_target": "balanced|makespan|efficiency",
        "auto_mode": false,
        "time_limit": 30,
        "population_size": 100
    }
    
    返回:
    {
        "success": true,
        "message": "排产完成",
        "schedule": [...],
        "metrics": {...},
        "execution_time": 2.5
    }
    
    输出目标: lotprioritydone表（已排产批次）
    """
    # 关键修复：将服务导入和初始化移入函数内部，打破循环导入
    from app.services.real_scheduling_service import RealSchedulingService
    from app.services.data_source_manager import DataSourceManager
    from app.models.production.scheduling_history import get_history_manager, SchedulingHistory
    from sqlalchemy import text
    from app import db
    
    try:
        start_time = time.time()
        data = request.get_json()
        
        # 提取参数
        algorithm = data.get('algorithm', 'intelligent')
        optimization_target = data.get('optimization_target', 'balanced')
        auto_mode = data.get('auto_mode', False)
        user_id = current_user.username if current_user.is_authenticated else 'system'
        
        logger.info(f"🚀 开始优化版智能排产 - 策略: {algorithm}, 目标: {optimization_target}, 用户: {user_id}")
        
        # 🔧 获取增强的排产锁和历史记录管理器
        lock_manager = get_scheduling_lock_manager()
        history_mgr = get_scheduling_history_manager()
        performance_monitor = get_performance_monitor()
        
        # 性能监控：整个API请求
        with performance_monitor.monitor_execution('manual_scheduling_api', {
            'algorithm': algorithm,
            'optimization_target': optimization_target,
            'user_id': user_id,
            'auto_mode': auto_mode
        }):
        
        # 🔧 检查重复执行（60秒内不允许重复相同参数的排产）
        if history_mgr.check_duplicate_execution(algorithm, user_id, optimization_target):
            return jsonify({
                'success': False,
                'message': '检测到重复的排产请求，请稍后重试',
                'error': 'DUPLICATE_REQUEST'
            }), 429
        
            # 🔧 使用增强的排产锁确保同时只有一个排产任务执行
        with lock_manager.acquire_scheduling_lock(algorithm, user_id, optimization_target, {
            'auto_mode': auto_mode,
            'time_limit': data.get('time_limit', 30),
            'population_size': data.get('population_size', 100)
        }) as context:
            logger.info(f"🔒 排产锁已获取: {context.lock_id}")
            
            # 🔥 创建排产历史记录
            history_manager = get_history_manager()
            history_record = SchedulingHistory.create_new_record(
                algorithm=algorithm,
                optimization_target=optimization_target,
                user_id=user_id,
                parameters={
                    'auto_mode': auto_mode,
                    'time_limit': data.get('time_limit', 30),
                    'population_size': data.get('population_size', 100),
                    'lock_id': context.lock_id,  # 添加锁ID作为历史记录标识
                    'api_version': 'v2_optimized'  # 标识优化版API
                }
            )
            logger.info(f"📝 创建排产历史记录: {history_record.history_id}")
            
            # 初始化服务
            rs = RealSchedulingService()
            manager = DataSourceManager()
            
            # 📊 预统计待排产批次数量
            wait_lots_preview, _ = manager.get_wait_lot_data()
            initial_lots_count = len(wait_lots_preview)
            logger.info(f"📋 预获取待排产批次: {initial_lots_count} 个")
            
            # 2. 🚀 核心改进：调用优化版排产算法
            logger.info(f"🧠 执行优化版排产算法 - 策略: {algorithm}")
            
            try:
                # 🔧 Task 1.1: 添加user_id参数支持，实现策略权重动态配置集成
                current_user_id = current_user.username if current_user.is_authenticated else None
                
                # 🚀 关键修复：使用execute_optimized_scheduling替代execute_real_scheduling
                logger.info(f"🎯 调用execute_optimized_scheduling算法")
                    
                    # 性能监控：排产算法执行
                    with performance_monitor.monitor_execution('scheduling_algorithm', {
                        'algorithm': algorithm,
                        'optimization_target': optimization_target,
                        'user_id': current_user_id,
                        'initial_lots_count': initial_lots_count
                    }):
                scheduling_result = rs.execute_optimized_scheduling(
                    algorithm=algorithm, 
                    user_id=current_user_id, 
                    optimization_target=optimization_target
                )
                
                # 🔧 兼容性处理：支持新旧两种返回格式
                if isinstance(scheduling_result, dict) and 'schedule' in scheduling_result:
                    # 新格式：包含完整统计信息
                    scheduled_lots = scheduling_result['schedule']
                    result_metrics = scheduling_result['metrics']
                    logger.info(f"✅ 获得新格式结果 - 排产数据包含完整统计信息")
                elif isinstance(scheduling_result, list):
                    # 旧格式：直接返回列表，需要构造统计信息
                    scheduled_lots = scheduling_result
                    result_metrics = {
                        'total_batches': initial_lots_count,  # 使用预获取的批次数量
                        'scheduled_batches': len(scheduled_lots),
                        'failed_batches': initial_lots_count - len(scheduled_lots),
                        'success_rate': f'{len(scheduled_lots)/initial_lots_count*100:.1f}%' if initial_lots_count > 0 else '0%',
                        'execution_time': 0.0,
                        'algorithm': algorithm,
                        'optimization_target': optimization_target
                    }
                    logger.info(f"✅ 兼容旧格式结果 - 构造统计信息: {result_metrics}")
                else:
                    # 异常情况：返回空结果
                    scheduled_lots = []
                    result_metrics = {
                        'total_batches': initial_lots_count,
                        'scheduled_batches': 0,
                        'failed_batches': initial_lots_count,
                        'success_rate': '0%',
                        'execution_time': 0.0,
                        'algorithm': algorithm,
                        'optimization_target': optimization_target
                    }
                    logger.warning(f"⚠️ 异常格式结果，返回空排产结果")
                
                logger.info(f"✅ 优化版排产算法执行完成 - 策略: {algorithm}, 优化目标: {optimization_target}, 用户: {current_user_id}")
                logger.info(f"📊 排产统计: 成功={result_metrics['scheduled_batches']}/{result_metrics['total_batches']} ({result_metrics['success_rate']})")
                
            except Exception as e:
                logger.error(f"❌ 优化版排产算法执行失败: {e}", exc_info=True)
                
                # 🔥 标记排产历史记录为失败
                try:
                    history_record.complete_error(f'优化版排产算法执行失败: {str(e)}')
                    history_manager.save_history(history_record)
                    logger.info(f"📝 排产失败历史记录已保存: {history_record.history_id}")
                except Exception as hist_error:
                    logger.error(f"❌ 保存失败历史记录异常: {hist_error}")
                
                return jsonify({
                    'success': False,
                    'message': f'优化版排产算法执行失败: {str(e)}',
                    'schedule': [],
                    'algorithm_used': 'execute_optimized_scheduling',
                    'error_details': str(e)
                }), 500
            
                # 3. 保存排产结果到数据库（使用增强的保存逻辑）
            if scheduled_lots:
                try:
                    logger.info(f"💾 开始保存 {len(scheduled_lots)} 条排产记录到数据库")
                    
                        # 性能监控：数据库保存操作
                        with performance_monitor.monitor_execution('database_save', {
                            'record_count': len(scheduled_lots),
                            'session_id': context.lock_id,
                            'algorithm': algorithm
                        }):
                            # 使用增强的保存逻辑
                            session_id = context.lock_id  # 使用锁ID作为会话ID
                            save_success = save_scheduling_results_enhanced(scheduled_lots, session_id)
                        
                        if not save_success:
                            raise Exception("增强排产结果保存失败")
                        
                        logger.info("✅ 增强版排产记录保存成功")
                    
                except Exception as e:
                        logger.error(f"❌ 保存增强版排产记录失败: {e}")
                    
                    # 🔥 标记排产历史记录为失败（保存失败）
                    try:
                            history_record.complete_error(f'增强版排产结果保存失败: {str(e)}')
                        history_manager.save_history(history_record)
                        logger.info(f"📝 排产保存失败历史记录已保存: {history_record.history_id}")
                    except Exception as hist_error:
                        logger.error(f"❌ 保存失败历史记录异常: {hist_error}")
                    
                    return jsonify({
                        'success': False,
                            'message': f'增强版排产计算成功但保存失败: {str(e)}',
                        'schedule': scheduled_lots,
                        'algorithm_used': 'execute_optimized_scheduling'
                    }), 500
            
            # 4. 计算执行时间和统计信息
            api_execution_time = time.time() - start_time
            
            # 策略名称映射
            strategy_names = {
                'intelligent': '智能综合策略',
                'deadline': '交期优先策略', 
                'product': '产品优先策略',
                'value': '产值优先策略'
            }
            
            # 🔧 使用优化版排产算法返回的真实统计数据
            metrics = {
                'algorithm': algorithm,
                'algorithm_used': 'execute_optimized_scheduling',  # 标识使用的算法
                'strategy_name': strategy_names.get(algorithm, algorithm),
                'optimization_target': optimization_target,
                'total_batches': result_metrics['total_batches'],
                'scheduled_batches': result_metrics['scheduled_batches'],
                'failed_batches': result_metrics['failed_batches'],
                'success_rate': result_metrics['success_rate'],
                'user_id': user_id,
                'core_execution_time': result_metrics.get('execution_time', 0),  # 核心算法执行时间
                'api_total_time': api_execution_time,  # API总耗时（包含保存等操作）
                'api_version': 'v2_optimized'
            }
            
            logger.info(f"🎉 优化版智能排产完成 - 成功: {result_metrics['scheduled_batches']}/{result_metrics['total_batches']} ({result_metrics['success_rate']}), 核心算法耗时: {result_metrics.get('execution_time', 0):.2f}s, API总耗时: {api_execution_time:.2f}s")
            
            # 🔥 完成排产历史记录（成功）
            history_saved = False
            try:
                history_record.set_input_summary(
                    wait_lots_count=result_metrics['total_batches'],
                    equipment_count=0,  # TODO: 从数据源获取设备数量
                    uph_data_count=0,   # TODO: 从数据源获取UPH数据数量
                    test_specs_count=0  # TODO: 从数据源获取测试规范数量
                )
                
                # 🔥 保存完整的排产结果数据，而不仅仅是统计摘要
                history_record.output_summary = {
                    'scheduled_lots_count': result_metrics['scheduled_batches'],
                    'total_good_qty': sum(lot.get('GOOD_QTY', 0) for lot in scheduled_lots),
                    'equipment_utilization': {},  # TODO: 计算设备利用率
                    'timestamp': datetime.now().isoformat(),
                    'algorithm_used': 'execute_optimized_scheduling',  # 记录使用的算法
                    # 🔥 关键修复：保存完整的排产结果列表
                    'schedule_results': scheduled_lots  # 这是前端需要的实际数据
                }
                history_record.results_count = result_metrics['scheduled_batches']
                
                # 🔧 修复：使用实际的算法执行时间而不是API总时间
                history_record.end_time = datetime.now()
                history_record.status = 'COMPLETED'
                
                # 🔧 修复：手动设置算法执行时间，确保duration_seconds正确
                algorithm_duration = result_metrics.get('execution_time', 0)
                
                # 如果算法时间太小（<0.01秒），使用API总时间
                if algorithm_duration < 0.01:
                    algorithm_duration = api_execution_time
                
                # 通过调整start_time来确保duration正确
                history_record.start_time = history_record.end_time - timedelta(seconds=algorithm_duration)
                
                logger.info(f"📝 优化版排产历史记录完成设置 - 算法耗时: {algorithm_duration:.2f}s, API总耗时: {api_execution_time:.2f}s")
                
                # 保存到数据库
                if history_manager.save_history(history_record):
                    history_saved = True
                    logger.info(f"✅ 优化版排产历史记录保存成功: {history_record.history_id} (包含{len(scheduled_lots)}条排产结果)")
                else:
                    logger.error(f"❌ 优化版排产历史记录保存失败: {history_record.history_id}")
            except Exception as e:
                logger.error(f"❌ 处理优化版排产历史记录失败: {e}")
            
            # 5. 返回结果
            return jsonify({
                'success': True,
                'message': f'优化版智能排产完成，成功排产 {result_metrics["scheduled_batches"]}/{result_metrics["total_batches"]} 个批次 ({result_metrics["success_rate"]})',
                'schedule': scheduled_lots,
                'metrics': metrics,
                'execution_time': api_execution_time,
                'failed_lots': []  # 🔧 修复：failed_lots已移除
            })
        
    except Exception as e:
        logger.error(f"❌ 智能排产失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'智能排产失败: {str(e)}',
            'schedule': [],
            'error': str(e)
        }), 500


@manual_scheduling_api.route('/api/production/save-priority-done', methods=['POST'])
@login_required
def save_priority_done():
    """保存排产优先级"""
    try:
        data = request.get_json()
        # 保存逻辑
        return jsonify({
            'success': True,
            'message': '排产优先级保存成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'保存失败: {str(e)}'
        }), 500


@manual_scheduling_api.route('/api/production/schedule-status', methods=['GET'])
@login_required
def get_schedule_status():
    """获取排产状态"""
    try:
        # 获取状态逻辑
        return jsonify({
            'success': True,
            'status': 'ready',
            'message': '系统就绪'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取状态失败: {str(e)}'
        }), 500


@manual_scheduling_api.route('/api/production/clear-schedule', methods=['POST'])
@login_required
def clear_schedule():
    """清空排产结果"""
    try:
        from app import db
        from sqlalchemy import text
        
        # 清空排产结果表
        db.session.execute(text("DELETE FROM lotprioritydone"))
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '排产结果已清空'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清空失败: {str(e)}'
        }), 500 