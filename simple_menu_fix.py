#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User, UserPermission
from app.config.menu_config import get_all_menu_ids

def fix_user_permissions():
    """修复用户权限"""
    print('修复用户权限...')
    
    users = User.query.all()
    all_menu_ids = get_all_menu_ids()
    
    for user in users:
        if user.role in ['admin', 'boss']:
            # 给管理员和boss所有权限
            current_perms = set(user.get_permissions())
            all_perms = set(all_menu_ids)
            missing_perms = all_perms - current_perms
            
            if missing_perms:
                print(f'为 {user.username} 添加 {len(missing_perms)} 个权限')
                for menu_id in missing_perms:
                    existing = UserPermission.query.filter_by(
                        username=user.username, 
                        menu_id=menu_id
                    ).first()
                    
                    if not existing:
                        permission = UserPermission(
                            username=user.username,
                            menu_id=menu_id,
                            granted_by='system_fix'
                        )
                        db.session.add(permission)
                
                try:
                    db.session.commit()
                    print(f'成功为 {user.username} 添加权限')
                except Exception as e:
                    print(f'添加权限失败: {e}')
                    db.session.rollback()

def update_context_processor():
    """更新上下文处理器"""
    print('更新上下文处理器...')
    
    context_file = 'app/context_processors.py'
    
    try:
        with open(context_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有错误处理
        if 'except Exception' not in content:
            # 备份原文件
            with open(f'{context_file}.backup', 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 在inject_menu_data函数周围添加try-except
            updated_content = content.replace(
                'def inject_menu_data():',
                '''def inject_menu_data():
        """注入菜单相关数据到所有模板"""
        try:
            return _inject_menu_data_impl()
        except Exception as e:
            from flask import current_app
            current_app.logger.error(f"菜单数据注入失败: {e}")
            from app.config.menu_config import MENU_CONFIG_VERSION
            return {
                'user_menu_html': '<div class="alert alert-warning text-center" style="margin: 20px;"><i class="fas fa-exclamation-triangle"></i><br><small>菜单配置加载失败，请刷新页面重试</small><br><button class="btn btn-sm btn-primary mt-2" onclick="location.reload()"><i class="fas fa-redo"></i> 重新加载</button></div>',
                'user_permissions': [],
                'menu_config_version': MENU_CONFIG_VERSION,
                'menu_error': str(e)
            }
    
    def _inject_menu_data_impl():'''
            )
            
            with open(context_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print('已更新上下文处理器')
        else:
            print('上下文处理器已有错误处理')
    
    except Exception as e:
        print(f'更新上下文处理器失败: {e}')

def main():
    print('开始简化菜单修复...\n')
    
    app_result = create_app()
    if isinstance(app_result, tuple):
        app = app_result[0]
    else:
        app = app_result
    
    with app.app_context():
        fix_user_permissions()
        update_context_processor()
        
        print('\n修复完成！请重启应用。')

if __name__ == '__main__':
    main() 