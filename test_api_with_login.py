#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试定时任务API（带登录）
"""

import requests
import json

def test_api_with_login():
    """测试定时任务API"""
    base_url = "http://127.0.0.1:5000"
    
    # 创建会话
    session = requests.Session()
    
    try:
        print("🔍 测试定时任务API...")
        
        # 1. 登录
        print("📝 正在登录...")
        login_data = {
            'username': 'admin',
            'password': 'admin'
        }
        
        login_response = session.post(f"{base_url}/auth/login", data=login_data)
        if login_response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return
        
        # 2. 测试获取定时任务列表
        print("\n📋 测试获取定时任务列表...")
        tasks_response = session.get(f"{base_url}/api/v2/system/scheduled-tasks")
        
        print(f"状态码: {tasks_response.status_code}")
        print(f"响应头: {dict(tasks_response.headers)}")
        
        if tasks_response.status_code == 200:
            try:
                tasks_data = tasks_response.json()
                print(f"✅ 成功获取任务列表")
                print(f"响应数据: {json.dumps(tasks_data, indent=2, ensure_ascii=False)}")
                
                if tasks_data.get('success'):
                    tasks = tasks_data.get('tasks', [])
                    print(f"📋 找到 {len(tasks)} 个定时任务")
                    for task in tasks:
                        print(f"  - {task.get('name')} ({task.get('type')})")
                else:
                    print(f"❌ API返回错误: {tasks_data.get('message')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {tasks_response.text[:500]}")
        else:
            print(f"❌ 请求失败: {tasks_response.status_code}")
            print(f"响应内容: {tasks_response.text[:500]}")
        
        # 3. 测试获取任务状态
        print("\n📊 测试获取任务状态...")
        status_response = session.get(f"{base_url}/api/v2/system/scheduled-tasks/status")
        
        print(f"状态码: {status_response.status_code}")
        
        if status_response.status_code == 200:
            try:
                status_data = status_response.json()
                print(f"✅ 成功获取任务状态")
                print(f"响应数据: {json.dumps(status_data, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {status_response.text[:500]}")
        else:
            print(f"❌ 请求失败: {status_response.status_code}")
            print(f"响应内容: {status_response.text[:500]}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_api_with_login()
