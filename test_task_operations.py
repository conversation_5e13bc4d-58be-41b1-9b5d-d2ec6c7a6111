import requests
import json

def test_task_operations():
    base_url = "http://127.0.0.1:5000"
    
    # 创建会话
    session = requests.Session()
    
    try:
        print("🔍 测试定时任务操作...")
        
        # 1. 登录
        print("📝 正在登录...")
        login_data = {
            'username': 'admin',
            'password': 'admin'
        }
        login_response = session.post(f"{base_url}/auth/login", data=login_data)
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return
        
        print("✅ 登录成功")
        
        # 2. 获取任务列表
        print("\n📋 获取任务列表...")
        tasks_response = session.get(f"{base_url}/api/v2/system/scheduled-tasks")
        tasks_result = tasks_response.json()
        
        if not tasks_result.get('success'):
            print(f"❌ 获取任务列表失败: {tasks_result.get('message')}")
            return
        
        tasks = tasks_result.get('tasks', [])
        if not tasks:
            print("❌ 没有找到任务")
            return
        
        task = tasks[0]
        task_id = task['id']
        print(f"✅ 找到任务: {task['name']} (ID: {task_id})")
        print(f"   当前状态: {task['status']}")
        
        # 3. 测试暂停任务
        print(f"\n⏸️ 测试暂停任务 {task_id}...")
        pause_response = session.post(f"{base_url}/api/v2/system/scheduled-tasks/{task_id}/pause")

        print(f"状态码: {pause_response.status_code}")
        print(f"响应文本: {pause_response.text}")

        if pause_response.text:
            pause_result = pause_response.json()
            print(f"响应: {json.dumps(pause_result, indent=2, ensure_ascii=False)}")

        # 4. 获取更新后的任务状态
        print(f"\n📋 获取更新后的任务状态...")
        updated_response = session.get(f"{base_url}/api/v2/system/scheduled-tasks")
        updated_result = updated_response.json()

        if updated_result.get('success'):
            updated_task = next((t for t in updated_result['tasks'] if t['id'] == task_id), None)
            if updated_task:
                print(f"✅ 任务状态已更新: {updated_task['status']}")
            else:
                print("❌ 未找到更新后的任务")

        # 5. 测试恢复任务
        print(f"\n▶️ 测试恢复任务 {task_id}...")
        resume_response = session.post(f"{base_url}/api/v2/system/scheduled-tasks/{task_id}/resume")

        print(f"状态码: {resume_response.status_code}")
        print(f"响应文本: {resume_response.text}")

        if resume_response.text:
            resume_result = resume_response.json()
            print(f"响应: {json.dumps(resume_result, indent=2, ensure_ascii=False)}")
        
        print(f"状态码: {resume_response.status_code}")
        print(f"响应: {json.dumps(resume_result, indent=2, ensure_ascii=False)}")
        
        # 6. 最终状态检查
        print(f"\n📋 最终状态检查...")
        final_response = session.get(f"{base_url}/api/v2/system/scheduled-tasks")
        final_result = final_response.json()
        
        if final_result.get('success'):
            final_task = next((t for t in final_result['tasks'] if t['id'] == task_id), None)
            if final_task:
                print(f"✅ 最终任务状态: {final_task['status']}")
            else:
                print("❌ 未找到最终任务")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_task_operations()
