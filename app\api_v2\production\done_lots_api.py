#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
已排产批次API接口 - 统一数据模型版本
提供lotprioritydone表的统一数据查询、操作接口
支持SUCCESS/FAILED/MANUAL_ADJUSTED状态的批次管理
"""

import logging
import os
import re
from flask import Blueprint, request, jsonify
from sqlalchemy import text
from app import db
from app.utils.api_config import get_api_route
from flask_login import login_required
from app.utils.db_manager import get_mysql_connection

logger = logging.getLogger(__name__)

def generate_suggestion(failure_reason, failure_details):
    """生成失败原因的建议解决方案"""
    if "配置需求获取失败" in failure_reason:
        return "请在ET_FT_TEST_SPEC表中补充对应器件和工序的测试规范"
    elif "无合适设备" in failure_reason:
        return "请检查设备状态和配置匹配，确保有可用的设备"
    elif "设备ID无效" in failure_reason:
        return "请检查设备配置，确保HANDLER_ID字段正确"
    elif "算法执行异常" in failure_reason:
        return "请检查系统日志，可能需要技术支持"
    elif "测试规范缺失" in failure_reason:
        return "请在ET_FT_TEST_SPEC表中补充对应器件和工序的测试规范"
    elif "配置" in failure_reason or "config" in failure_reason.lower():
        return "请检查配置设置和参数是否正确"
    elif "设备" in failure_reason or "不兼容" in failure_reason:
        return "请检查设备兼容性和状态"
    else:
        return "请联系技术支持进行详细分析"

def _check_schema_compatibility():
    """检查数据库schema是否支持统一数据模型"""
    try:
        # 检查是否存在新的统一模型字段
        check_query = text("""
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'lotprioritydone' 
            AND column_name = 'SCHEDULING_STATUS'
        """)
        result = db.session.execute(check_query)
        has_status_field = result.scalar() > 0
        
        return has_status_field
    except Exception as e:
        logger.warning(f"Schema检查失败: {e}")
        return False

# 创建蓝图 - 使用配置化的URL前缀
done_lots_bp = Blueprint('done_lots_api', __name__)

# 兼容性导出
done_lots_api = done_lots_bp

@done_lots_api.route(get_api_route('production/done-lots'), methods=['GET'])
def get_unified_lots_data():
    """
    获取统一的已排产数据（包含所有状态：SUCCESS/FAILED/MANUAL_ADJUSTED）
    支持分页、筛选、排序
    """
    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 50))
        status_filter = request.args.get('status', '')  # 可选状态筛选
        
        # 计算偏移量
        offset = (page - 1) * size
        
        # 检查schema兼容性
        is_unified_schema = _check_schema_compatibility()
        
        if is_unified_schema:
            # 🔥 使用新的统一数据模型
            logger.info("使用统一数据模型进行查询")
            
            # 构建WHERE条件
            where_conditions = []
            params = {'size': size, 'offset': offset}
            
            if status_filter:
                where_conditions.append("SCHEDULING_STATUS = :status_filter")
                params['status_filter'] = status_filter
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
        
        # 查询总数
            count_query = text(f"""
                SELECT COUNT(*) FROM lotprioritydone 
                {where_clause}
            """)
            total_result = db.session.execute(count_query, params)
            total = total_result.scalar() or 0
            
            # 🔥 计算统一统计信息（包含所有状态）
            stats_query = text(f"""
                SELECT 
                    COUNT(*) as total_records,
                    SUM(GOOD_QTY) as total_quantity,
                    COUNT(DISTINCT HANDLER_ID) as handler_count,
                    AVG(CASE WHEN comprehensive_score > 0 THEN comprehensive_score ELSE NULL END) as avg_score,
                    SUM(CASE WHEN SCHEDULING_STATUS = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
                    SUM(CASE WHEN SCHEDULING_STATUS = 'FAILED' THEN 1 ELSE 0 END) as failed_count,
                    SUM(CASE WHEN SCHEDULING_STATUS = 'MANUAL_ADJUSTED' THEN 1 ELSE 0 END) as manual_adjusted_count
                FROM lotprioritydone 
                {where_clause}
            """)
            
            stats_result = db.session.execute(stats_query, params)
            stats_row = stats_result.fetchone()
            
            # 构建统计信息
            statistics = {
                'total_records': stats_row[0] or 0,
                'total_quantity': int(stats_row[1] or 0),
                'handler_count': stats_row[2] or 0,
                'avg_score': round(float(stats_row[3] or 0), 1),
                'success_count': stats_row[4] or 0,
                'failed_count': stats_row[5] or 0,
                'manual_adjusted_count': stats_row[6] or 0
            }
            
            # 查询数据
            data_query = text(f"""
                SELECT 
                    id, PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                    PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE,
                    WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                    RELEASE_TIME, FAC_ID, CREATE_TIME,
                    match_type, comprehensive_score, processing_time, changeover_time,
                    algorithm_version, priority_score,
                    -- 统一模型新字段
                    SCHEDULING_STATUS, FAILURE_REASON, FAILURE_DETAILS, SESSION_ID
                FROM lotprioritydone 
                {where_clause}
                ORDER BY 
                    CASE WHEN SCHEDULING_STATUS = 'FAILED' THEN 0 ELSE 1 END,
                    PRIORITY ASC, CREATE_TIME DESC
                LIMIT :size OFFSET :offset
            """)
            
            result = db.session.execute(data_query, params)
            
            # 构建返回数据
            records = []
            for row in result.fetchall():
                record = {
                    'id': row[0],  
                    'PRIORITY': row[1] or '',
                    'HANDLER_ID': row[2] or '',
                    'LOT_ID': row[3] or '',
                    'LOT_TYPE': row[4] or '',
                    'GOOD_QTY': row[5] or 0,
                    'PROD_ID': row[6] or '',
                    'DEVICE': row[7] or '',
                    'CHIP_ID': row[8] or '',
                    'PKG_PN': row[9] or '',
                    'PO_ID': row[10] or '',
                    'STAGE': row[11] or '',
                    'WIP_STATE': row[12] or '',
                    'PROC_STATE': row[13] or '',
                    'HOLD_STATE': row[14] or 0,
                    'FLOW_ID': row[15] or '',
                    'FLOW_VER': row[16] or '',
                    'RELEASE_TIME': row[17] or '',
                    'FAC_ID': row[18] or '',
                    'CREATE_TIME': row[19] or '',
                    'match_type': row[20] or '',
                    'comprehensive_score': row[21] or 0.0,
                    'processing_time': row[22] or 0.0,
                    'changeover_time': row[23] or 0.0,
                    'algorithm_version': row[24] or '',
                    'priority_score': row[25] or 0.0,
                    # 🔥 统一模型字段
                    'SCHEDULING_STATUS': row[26] or 'SUCCESS',
                    'FAILURE_REASON': row[27] or '',
                    'FAILURE_DETAILS': row[28] or '',
                    'SESSION_ID': row[29] or ''
                }
                
                # 为失败批次添加建议
                if record['SCHEDULING_STATUS'] == 'FAILED' and record['FAILURE_REASON']:
                    record['suggestion'] = generate_suggestion(record['FAILURE_REASON'], record['FAILURE_DETAILS'])
                
                records.append(record)
        
        else:
            # 🔥 兼容模式：使用原有的双表查询逻辑
            logger.info("使用兼容模式，查询原有数据结构")
            
            # 查询成功批次（lotprioritydone表）
            count_query = text("SELECT COUNT(*) FROM lotprioritydone")
            total_result = db.session.execute(count_query)
            total = total_result.scalar() or 0
        
        # 计算统计信息
        stats_query = text("""
                SELECT 
                    COUNT(*) as total_records,
                    SUM(GOOD_QTY) as total_quantity,
                    COUNT(DISTINCT HANDLER_ID) as handler_count,
                    AVG(CASE WHEN comprehensive_score > 0 THEN comprehensive_score ELSE NULL END) as avg_score
                FROM lotprioritydone 
                WHERE HANDLER_ID IS NOT NULL AND HANDLER_ID != ''
            """)
            
        stats_result = db.session.execute(stats_query)
        stats_row = stats_result.fetchone()
            
            # 查询成功批次数据
        data_query = text("""
                SELECT 
                    id, PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                    PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE,
                    WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                    RELEASE_TIME, FAC_ID, CREATE_TIME,
                        match_type, comprehensive_score, processing_time, changeover_time,
                        algorithm_version, priority_score
                FROM lotprioritydone 
                ORDER BY PRIORITY ASC, CREATE_TIME DESC
                LIMIT :size OFFSET :offset
            """)
            
        result = db.session.execute(data_query, {'size': size, 'offset': offset})
            
            # 构建成功批次数据
        records = []
        for row in result.fetchall():
                records.append({
                        'id': row[0],
                    'PRIORITY': row[1] or '',
                    'HANDLER_ID': row[2] or '',
                    'LOT_ID': row[3] or '',
                    'LOT_TYPE': row[4] or '',
                    'GOOD_QTY': row[5] or 0,
                    'PROD_ID': row[6] or '',
                    'DEVICE': row[7] or '',
                    'CHIP_ID': row[8] or '',
                    'PKG_PN': row[9] or '',
                    'PO_ID': row[10] or '',
                    'STAGE': row[11] or '',
                    'WIP_STATE': row[12] or '',
                    'PROC_STATE': row[13] or '',
                    'HOLD_STATE': row[14] or 0,
                    'FLOW_ID': row[15] or '',
                    'FLOW_VER': row[16] or '',
                    'RELEASE_TIME': row[17] or '',
                    'FAC_ID': row[18] or '',
                    'CREATE_TIME': row[19] or '',
                        'match_type': row[20] or '',
                        'comprehensive_score': row[21] or 0.0,
                        'processing_time': row[22] or 0.0,
                        'changeover_time': row[23] or 0.0,
                        'algorithm_version': row[24] or '',
                        'priority_score': row[25] or 0.0,
                        'SCHEDULING_STATUS': 'SUCCESS'  # 兼容模式下默认为成功
                    })
                
                # 兼容模式下的统计信息
                statistics = {
                    'total_records': stats_row[0] or 0,
                    'total_quantity': int(stats_row[1] or 0),
                    'handler_count': stats_row[2] or 0,
                    'avg_score': round(float(stats_row[3] or 0), 1),
                    'success_count': stats_row[0] or 0,
                    'failed_count': 0,  # 兼容模式下暂不统计失败数
                    'manual_adjusted_count': 0
                }
            
            # 计算分页信息
        total_pages = (total + size - 1) // size
            
        logger.info(f"📊 统一批次查询: 第{page}页, {size}条/页, 共{total}条记录, 统计: {statistics}")
            
        return jsonify({
                'success': True,
                'data': records,
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': total,
                    'total_pages': total_pages
                },
                'statistics': statistics,
                'schema_info': {
                    'is_unified_schema': is_unified_schema,
                    'supports_failed_lots': is_unified_schema
                }
            })
            
    except Exception as e:
        logger.error(f"❌ 获取统一批次数据失败: {e}")
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}'
        }), 500

@done_lots_api.route(get_api_route('production/done-lots/columns'), methods=['GET'])
def get_unified_lots_columns():
    """获取统一的已排产表列信息（包含状态字段）"""
    try:
        # 检查schema兼容性
        is_unified_schema = _check_schema_compatibility()
        
        # 基础列信息
        columns = [
            {'name': 'SCHEDULING_STATUS', 'label': '状态', 'type': 'string', 'sortable': True},
            {'name': 'PRIORITY', 'label': '执行优先级', 'type': 'number', 'sortable': True},
            {'name': 'comprehensive_score', 'label': '综合评分', 'type': 'number', 'sortable': True},
            {'name': 'HANDLER_ID', 'label': '分拣机ID', 'type': 'string', 'sortable': True},
            {'name': 'LOT_ID', 'label': '批次号', 'type': 'string', 'sortable': True},
            {'name': 'LOT_TYPE', 'label': '批次类型', 'type': 'string', 'sortable': True},
            {'name': 'GOOD_QTY', 'label': '良品数量', 'type': 'number', 'sortable': True},
            {'name': 'PROD_ID', 'label': '产品ID', 'type': 'string', 'sortable': True},
            {'name': 'DEVICE', 'label': '器件名称', 'type': 'string', 'sortable': True},
            {'name': 'CHIP_ID', 'label': '芯片ID', 'type': 'string', 'sortable': True},
            {'name': 'PKG_PN', 'label': '封装', 'type': 'string', 'sortable': True},
            {'name': 'PO_ID', 'label': '订单号', 'type': 'string', 'sortable': True},
            {'name': 'STAGE', 'label': '工序', 'type': 'string', 'sortable': True},
            {'name': 'processing_time', 'label': '预计加工时间(h)', 'type': 'number', 'sortable': True},
            {'name': 'changeover_time', 'label': '改机时间(min)', 'type': 'number', 'sortable': True},
            {'name': 'WIP_STATE', 'label': 'WIP状态', 'type': 'string', 'sortable': True},
            {'name': 'PROC_STATE', 'label': '流程状态', 'type': 'string', 'sortable': True},
            {'name': 'HOLD_STATE', 'label': '扣留状态', 'type': 'number', 'sortable': True},
            {'name': 'FLOW_ID', 'label': '流程ID', 'type': 'string', 'sortable': True},
            {'name': 'FLOW_VER', 'label': '流程版本', 'type': 'string', 'sortable': True},
            {'name': 'RELEASE_TIME', 'label': '释放时间', 'type': 'datetime', 'sortable': True},
            {'name': 'FAC_ID', 'label': '工厂ID', 'type': 'string', 'sortable': True},
            {'name': 'CREATE_TIME', 'label': '创建时间', 'type': 'datetime', 'sortable': True},
            {'name': 'priority_score', 'label': '优先级评分', 'type': 'number', 'sortable': True},
            {'name': 'algorithm_version', 'label': '算法版本', 'type': 'string', 'sortable': True},
            {'name': 'match_type', 'label': '匹配类型', 'type': 'string', 'sortable': True}
        ]
        
        # 如果支持统一模型，添加额外字段
        if is_unified_schema:
            columns.extend([
                {'name': 'FAILURE_REASON', 'label': '失败原因', 'type': 'string', 'sortable': True},
                {'name': 'SESSION_ID', 'label': '会话ID', 'type': 'string', 'sortable': True}
            ])
        
        return jsonify({
            'success': True,
            'columns': columns,
            'schema_info': {
                'is_unified_schema': is_unified_schema,
                'supports_status_management': is_unified_schema
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 获取列信息失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取列信息失败: {str(e)}'
        }), 500

@done_lots_api.route(get_api_route('production/done-lots/move-to-waiting'), methods=['POST'])
def move_lots_to_waiting():
    """将已排产批次移回待排产状态（支持所有状态的批次）"""
    try:
        data = request.get_json()
        lot_ids = data.get('ids', [])
        
        if not lot_ids:
            return jsonify({
                'success': False,
                'message': '请选择要移动的批次'
            }), 400
        
        # 获取要移动的批次信息
        placeholders = ','.join([':id_%d' % i for i in range(len(lot_ids))])
        params = {'id_%d' % i: lot_id for i, lot_id in enumerate(lot_ids)}
        
        # 🔥 支持统一模型：获取批次信息（包含状态）
        is_unified_schema = _check_schema_compatibility()
        
        if is_unified_schema:
            select_query = text(f"""
                SELECT LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID,
                       FLOW_ID, FLOW_VER, FAC_ID, RELEASE_TIME, SCHEDULING_STATUS
                FROM lotprioritydone 
                WHERE PRIORITY IN ({placeholders})
            """)
        else:
            select_query = text(f"""
                SELECT LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID,
                       FLOW_ID, FLOW_VER, FAC_ID, RELEASE_TIME
                FROM lotprioritydone 
                WHERE PRIORITY IN ({placeholders})
            """)
        
        result = db.session.execute(select_query, params)
        lots_to_move = result.fetchall()
        
        if not lots_to_move:
            return jsonify({
                'success': False,
                'message': '未找到指定的批次'
            }), 404
        
        # 将批次信息重新插入到ET_WAIT_LOT表
        moved_count = 0
        for lot in lots_to_move:
            try:
                insert_query = text("""
                    INSERT INTO ET_WAIT_LOT (
                        LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID,
                        WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                        FAC_ID, RELEASE_TIME, CREATE_TIME
                    ) VALUES (
                        :lot_id, :device, :stage, :good_qty, :pkg_pn, :chip_id,
                        'WAIT', 'UNASSIGNED', 0, :flow_id, :flow_ver,
                        :fac_id, :release_time, NOW()
                    )
                """)
                
                db.session.execute(insert_query, {
                    'lot_id': lot[0],
                    'device': lot[1],
                    'stage': lot[2],
                    'good_qty': lot[3],
                    'pkg_pn': lot[4],
                    'chip_id': lot[5],
                    'flow_id': lot[6],
                    'flow_ver': lot[7],
                    'fac_id': lot[8],
                    'release_time': lot[9]
                })
                moved_count += 1
                
            except Exception as e:
                logger.warning(f"移动批次 {lot[0]} 失败: {e}")
                continue
        
        # 从已排产表中删除这些批次
        if moved_count > 0:
            delete_query = text(f"""
                DELETE FROM lotprioritydone 
                WHERE PRIORITY IN ({placeholders})
            """)
            db.session.execute(delete_query, params)
        
        db.session.commit()
        
        logger.info(f"✅ 成功移动 {moved_count} 个批次到待排产状态")
        
        return jsonify({
            'success': True,
            'message': f'成功移动 {moved_count} 个批次到待排产状态',
            'moved_count': moved_count
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 移动批次失败: {e}")
        return jsonify({
            'success': False,
            'message': f'移动失败: {str(e)}'
        }), 500

@done_lots_api.route(get_api_route('production/done-lots/delete'), methods=['POST'])
def delete_lotprioritydone_records():
    """删除已排产记录（支持所有状态）"""
    try:
        data = request.get_json()
        ids = data.get('ids', [])
        
        if not ids:
            return jsonify({
                'success': False,
                'message': '请选择要删除的记录'
            }), 400
        
        # 构建删除语句
        placeholders = ','.join([':id_%d' % i for i in range(len(ids))])
        params = {'id_%d' % i: record_id for i, record_id in enumerate(ids)}
        
        delete_query = text(f"""
            DELETE FROM lotprioritydone 
            WHERE PRIORITY IN ({placeholders})
        """)
        
        result = db.session.execute(delete_query, params)
        deleted_count = result.rowcount
        
        db.session.commit()
        
        logger.info(f"✅ 成功删除 {deleted_count} 条已排产记录")
        
        return jsonify({
            'success': True,
            'message': f'成功删除 {deleted_count} 条记录',
            'deleted_count': deleted_count
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 删除已排产记录失败: {e}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }), 500

@done_lots_api.route(get_api_route('production/save-and-publish-schedule'), methods=['POST'])
@login_required
def save_and_publish_schedule():
    """保存并发布排产结果（支持统一数据模型）"""
    try:
        data = request.get_json()
        schedule = data.get('schedule', [])
        metrics = data.get('metrics', {})
        publish_status = data.get('publish_status', 'DRAFT')
        
        if not schedule:
            return jsonify({
                'success': False,
                'message': '没有可保存的排产数据'
            }), 400
        
        # 检查schema兼容性
        is_unified_schema = _check_schema_compatibility()
        
        saved_count = 0
        
        try:
            # 如果是发布状态，先清空现有数据
            if publish_status == 'PUBLISHED':
                delete_query = text("DELETE FROM lotprioritydone")
                db.session.execute(delete_query)
                logger.info("✅ 已清空现有排产数据，准备保存新数据")
            
            # 批量插入新数据
            for item in schedule:
                if is_unified_schema:
                    # 🔥 使用统一数据模型保存
                    insert_query = text("""
                        INSERT INTO lotprioritydone (
                            PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                            PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE,
                            WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                            RELEASE_TIME, FAC_ID, CREATE_TIME,
                            comprehensive_score, processing_time, changeover_time,
                            algorithm_version, match_type, priority_score,
                            SCHEDULING_STATUS, FAILURE_REASON, FAILURE_DETAILS, SESSION_ID
                        ) VALUES (
                            :priority, :handler_id, :lot_id, :lot_type, :good_qty,
                            :prod_id, :device, :chip_id, :pkg_pn, :po_id, :stage,
                            :wip_state, :proc_state, :hold_state, :flow_id, :flow_ver,
                            :release_time, :fac_id, NOW(),
                            :comprehensive_score, :processing_time, :changeover_time,
                            :algorithm_version, :match_type, :priority_score,
                            :scheduling_status, :failure_reason, :failure_details, :session_id
                        )
                    """)
                    
                    insert_data = {
                        'priority': item.get('PRIORITY') or saved_count + 1,
                        'handler_id': item.get('HANDLER_ID', ''),
                        'lot_id': item.get('LOT_ID', ''),
                        'lot_type': item.get('LOT_TYPE', ''),
                        'good_qty': item.get('GOOD_QTY', 0),
                        'prod_id': item.get('PROD_ID', ''),
                        'device': item.get('DEVICE', ''),
                        'chip_id': item.get('CHIP_ID', ''),
                        'pkg_pn': item.get('PKG_PN', ''),
                        'po_id': item.get('PO_ID', ''),
                        'stage': item.get('STAGE', ''),
                        'wip_state': item.get('WIP_STATE', ''),
                        'proc_state': item.get('PROC_STATE', ''),
                        'hold_state': item.get('HOLD_STATE', 0),
                        'flow_id': item.get('FLOW_ID', ''),
                        'flow_ver': item.get('FLOW_VER', ''),
                        'release_time': item.get('RELEASE_TIME') or None,
                        'fac_id': item.get('FAC_ID', ''),
                        'comprehensive_score': item.get('comprehensive_score', 0.0),
                        'processing_time': item.get('processing_time', 0.0),
                        'changeover_time': item.get('changeover_time', 0.0),
                        'algorithm_version': metrics.get('algorithm', 'enhanced_heuristic'),
                        'match_type': item.get('match_type', ''),
                        'priority_score': item.get('priority_score', 0.0),
                        'scheduling_status': item.get('SCHEDULING_STATUS', 'SUCCESS'),
                        'failure_reason': item.get('FAILURE_REASON', ''),
                        'failure_details': item.get('FAILURE_DETAILS', ''),
                        'session_id': item.get('SESSION_ID', '')
                    }
                else:
                    # 兼容模式：使用原有字段
                    insert_query = text("""
                        INSERT INTO lotprioritydone (
                            PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                            PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE,
                            WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                            RELEASE_TIME, FAC_ID, CREATE_TIME,
                            comprehensive_score, processing_time, changeover_time,
                            algorithm_version, match_type, priority_score
                        ) VALUES (
                            :priority, :handler_id, :lot_id, :lot_type, :good_qty,
                            :prod_id, :device, :chip_id, :pkg_pn, :po_id, :stage,
                            :wip_state, :proc_state, :hold_state, :flow_id, :flow_ver,
                            :release_time, :fac_id, NOW(),
                            :comprehensive_score, :processing_time, :changeover_time,
                            :algorithm_version, :match_type, :priority_score
                        )
                    """)
                    
                    insert_data = {
                        'priority': item.get('PRIORITY') or saved_count + 1,
                        'handler_id': item.get('HANDLER_ID', ''),
                        'lot_id': item.get('LOT_ID', ''),
                        'lot_type': item.get('LOT_TYPE', ''),
                        'good_qty': item.get('GOOD_QTY', 0),
                        'prod_id': item.get('PROD_ID', ''),
                        'device': item.get('DEVICE', ''),
                        'chip_id': item.get('CHIP_ID', ''),
                        'pkg_pn': item.get('PKG_PN', ''),
                        'po_id': item.get('PO_ID', ''),
                        'stage': item.get('STAGE', ''),
                        'wip_state': item.get('WIP_STATE', ''),
                        'proc_state': item.get('PROC_STATE', ''),
                        'hold_state': item.get('HOLD_STATE', 0),
                        'flow_id': item.get('FLOW_ID', ''),
                        'flow_ver': item.get('FLOW_VER', ''),
                        'release_time': item.get('RELEASE_TIME') or None,
                        'fac_id': item.get('FAC_ID', ''),
                        'comprehensive_score': item.get('comprehensive_score', 0.0),
                        'processing_time': item.get('processing_time', 0.0),
                        'changeover_time': item.get('changeover_time', 0.0),
                        'algorithm_version': metrics.get('algorithm', 'enhanced_heuristic'),
                        'match_type': item.get('match_type', ''),
                        'priority_score': item.get('priority_score', 0.0)
                    }
                
                db.session.execute(insert_query, insert_data)
                saved_count += 1
            
            # 提交事务
            db.session.commit()
            
            logger.info(f"✅ 成功保存并发布 {saved_count} 条排产记录")
            
            return jsonify({
                'success': True,
                'message': f'成功保存并发布 {saved_count} 条排产记录',
                'saved_count': saved_count,
                'publish_status': publish_status,
                'schema_info': {
                    'is_unified_schema': is_unified_schema
                }
            })
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"❌ 保存排产数据失败: {e}")
            return jsonify({
                'success': False,
                'message': f'保存失败: {str(e)}'
            }), 500
            
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 保存并发布排产结果失败: {e}")
        return jsonify({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }), 500


# 🔥 新增：统一批次状态管理API
@done_lots_api.route(get_api_route('production/done-lots/status-summary'), methods=['GET'])
def get_status_summary():
    """获取批次状态汇总信息"""
    try:
        is_unified_schema = _check_schema_compatibility()
        
        if not is_unified_schema:
            return jsonify({
                'success': False,
                'message': '当前数据库架构不支持状态汇总功能'
            }), 400
        
        # 查询状态汇总
        summary_query = text("""
            SELECT 
                SCHEDULING_STATUS,
                COUNT(*) as count,
                SUM(GOOD_QTY) as total_qty,
                AVG(comprehensive_score) as avg_score
            FROM lotprioritydone 
            GROUP BY SCHEDULING_STATUS
            ORDER BY 
                CASE SCHEDULING_STATUS 
                    WHEN 'SUCCESS' THEN 1
                    WHEN 'MANUAL_ADJUSTED' THEN 2
                    WHEN 'FAILED' THEN 3
                    ELSE 4
                END
        """)
        
        result = db.session.execute(summary_query)
        
        status_summary = []
        for row in result.fetchall():
            status_summary.append({
                'status': row[0],
                'count': row[1],
                'total_qty': row[2] or 0,
                'avg_score': round(float(row[3] or 0), 2)
            })
                
        return jsonify({
            'success': True,
            'data': status_summary
        })
        
    except Exception as e:
        logger.error(f"❌ 获取状态汇总失败: {e}")
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}'
        }), 500 

# 🔥 移除旧的失败批次API，现在统一在get_unified_lots_data中处理
# 原get_failed_lots_from_logs方法已被统一数据模型替代

# 🔥 保留清理功能，但更新为统一模型
@done_lots_api.route(get_api_route('production/clear-failed-lots'), methods=['POST'])
@login_required
def clear_failed_lots():
    """清空失败批次记录（统一数据模型版本）"""
    try:
        logger.info("🗑️ 开始清空失败批次记录...")
        
        is_unified_schema = _check_schema_compatibility()
        
        if is_unified_schema:
            # 统一模型：只删除FAILED状态的记录
            count_query = text("SELECT COUNT(*) FROM lotprioritydone WHERE SCHEDULING_STATUS = 'FAILED'")
            count_result = db.session.execute(count_query)
            before_count = count_result.scalar() or 0
            
            delete_query = text("DELETE FROM lotprioritydone WHERE SCHEDULING_STATUS = 'FAILED'")
            db.session.execute(delete_query)
            db.session.commit()
            
            logger.info(f"✅ 成功清空 {before_count} 条失败批次记录")
            
            return jsonify({
                'success': True,
                'message': f'成功清空 {before_count} 条失败批次记录',
                'deleted_count': before_count
            })
        else:
            # 兼容模式：清理scheduling_failed_lots表
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 检查表是否存在
            check_table_sql = """
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'scheduling_failed_lots'
            """
            cursor.execute(check_table_sql)
            check_result = cursor.fetchone()
            
            if isinstance(check_result, dict):
                table_exists = list(check_result.values())[0] > 0
            else:
                table_exists = check_result[0] > 0
            
            if not table_exists:
                cursor.close()
                conn.close()
                return jsonify({
                    'success': False,
                    'message': 'scheduling_failed_lots表不存在'
                }), 400
            
            # 获取删除前的记录数
            count_sql = "SELECT COUNT(*) FROM scheduling_failed_lots"
            cursor.execute(count_sql)
            count_result = cursor.fetchone()
            
            if isinstance(count_result, dict):
                before_count = list(count_result.values())[0]
            else:
                before_count = count_result[0]
            
            # 清空失败记录表
            delete_sql = "DELETE FROM scheduling_failed_lots"
            cursor.execute(delete_sql)
            conn.commit()
            
            cursor.close()
            conn.close()
            
            logger.info(f"✅ 成功清空排产失败记录，删除了 {before_count} 条记录")
            
            return jsonify({
                'success': True,
                'message': f'成功清空排产失败记录，删除了 {before_count} 条记录',
                'deleted_count': before_count
            })
            
    except Exception as e:
        logger.error(f"❌ 清空失败批次记录失败: {e}")
        return jsonify({
            'success': False,
            'message': f'清空失败: {str(e)}'
        }), 500 