import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
import pytest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.production.scheduling_results import SchedulingResultModel, SchedulingResultPersister


class TestSchedulingResultModel(unittest.TestCase):
    """测试SchedulingResultModel数据类"""
    
    def setUp(self):
        """设置测试数据"""
        self.sample_lot_data = {
            'PRIORITY': 1,
            'HANDLER_ID': 'EQP001',
            'LOT_ID': 'LOT123456',
            'LOT_TYPE': 'Production',
            'GOOD_QTY': 1000,
            'PROD_ID': 'PROD001',
            'DEVICE': 'TestDevice',
            'CHIP_ID': 'CHIP001',
            'PKG_PN': 'PKG001',
            'PO_ID': 'PO123',
            'STAGE': 'FT',
            'WIP_STATE': 'QUEUE',
            'PROC_STATE': 'WAIT',
            'HOLD_STATE': 'N',
            'FLOW_ID': 'FLOW001',
            'FLOW_VER': '1.0',
            'RELEASE_TIME': datetime.now(),
            'FAC_ID': 'FAC1',
            'CREATE_TIME': datetime.now(),
            
            # 算法扩展字段
            'match_type': '完全匹配',
            'comprehensive_score': 95.5,
            'processing_time': 2.5,
            'changeover_time': 15.0,
            'algorithm_version': 'v3.0-enhanced',
            'priority_score': 85.0,
            'estimated_hours': 3.0,
            'equipment_status': 'IDLE',
            'FAMILY': 'ProductFamily1',
            'SESSION_ID': 'session_123'
        }
    
    def test_from_algorithm_result_complete_data(self):
        """测试从完整算法结果创建模型"""
        model = SchedulingResultModel.from_algorithm_result(self.sample_lot_data)
        
        # 验证基础字段
        self.assertEqual(model.LOT_ID, 'LOT123456')
        self.assertEqual(model.PRIORITY, 1)
        self.assertEqual(model.GOOD_QTY, 1000)
        self.assertEqual(model.HANDLER_ID, 'EQP001')
        
        # 验证扩展字段
        self.assertEqual(model.match_type, '完全匹配')
        self.assertEqual(model.comprehensive_score, 95.5)
        self.assertEqual(model.processing_time, 2.5)
        self.assertEqual(model.algorithm_version, 'v3.0-enhanced')
        
        # 验证默认值
        self.assertEqual(model.SCHEDULING_STATUS, 'SUCCESS')
        self.assertIsNotNone(model.created_at)
        self.assertIsNotNone(model.updated_at)
    
    def test_from_algorithm_result_minimal_data(self):
        """测试从最小数据创建模型"""
        minimal_data = {
            'LOT_ID': 'LOT001',
            'GOOD_QTY': 500,
            'PROD_ID': 'PROD001'
        }
        
        model = SchedulingResultModel.from_algorithm_result(minimal_data)
        
        # 验证必填字段
        self.assertEqual(model.LOT_ID, 'LOT001')
        self.assertEqual(model.GOOD_QTY, 500)
        self.assertEqual(model.PROD_ID, 'PROD001')
        
        # 验证默认值
        self.assertEqual(model.PRIORITY, 1)
        self.assertEqual(model.WIP_STATE, 'QUEUE')
        self.assertEqual(model.match_type, '')
        self.assertEqual(model.comprehensive_score, 0.0)
    
    def test_safe_int_conversion(self):
        """测试安全整数转换"""
        # 正常转换
        self.assertEqual(SchedulingResultModel._safe_int(10), 10)
        self.assertEqual(SchedulingResultModel._safe_int("20"), 20)
        self.assertEqual(SchedulingResultModel._safe_int(30.5), 30)
        
        # 异常情况
        self.assertEqual(SchedulingResultModel._safe_int(None), 0)
        self.assertEqual(SchedulingResultModel._safe_int("invalid"), 0)
        self.assertEqual(SchedulingResultModel._safe_int(""), 0)
    
    def test_safe_float_conversion(self):
        """测试安全浮点数转换"""
        # 正常转换
        self.assertEqual(SchedulingResultModel._safe_float(10.5), 10.5)
        self.assertEqual(SchedulingResultModel._safe_float("20.3"), 20.3)
        self.assertEqual(SchedulingResultModel._safe_float(30), 30.0)
        
        # 异常情况
        self.assertEqual(SchedulingResultModel._safe_float(None), 0.0)
        self.assertEqual(SchedulingResultModel._safe_float("invalid"), 0.0)
        self.assertEqual(SchedulingResultModel._safe_float(""), 0.0)
    
    def test_safe_datetime_conversion(self):
        """测试安全日期时间转换"""
        now = datetime.now()
        
        # 正常转换
        self.assertEqual(SchedulingResultModel._safe_datetime(now), now)
        self.assertIsNone(SchedulingResultModel._safe_datetime(None))
        
        # ISO格式字符串转换（简化测试）
        iso_string = "2025-01-16T10:30:00"
        result = SchedulingResultModel._safe_datetime(iso_string)
        # 如果转换失败，应该返回None
        self.assertIsNone(result)  # 因为我们的实现中会捕获异常并返回None
    
    def test_validate_success(self):
        """测试数据验证成功"""
        model = SchedulingResultModel.from_algorithm_result(self.sample_lot_data)
        errors = model.validate()
        self.assertEqual(len(errors), 0)
    
    def test_validate_missing_required_fields(self):
        """测试缺少必填字段的验证"""
        # 缺少LOT_ID
        data_missing_lot_id = self.sample_lot_data.copy()
        data_missing_lot_id['LOT_ID'] = ''
        model = SchedulingResultModel.from_algorithm_result(data_missing_lot_id)
        errors = model.validate()
        self.assertGreater(len(errors), 0)
        self.assertTrue(any('LOT_ID' in error for error in errors))
        
        # 缺少GOOD_QTY
        data_missing_qty = self.sample_lot_data.copy()
        data_missing_qty['GOOD_QTY'] = 0
        model = SchedulingResultModel.from_algorithm_result(data_missing_qty)
        errors = model.validate()
        self.assertGreater(len(errors), 0)
        self.assertTrue(any('GOOD_QTY' in error for error in errors))
    
    def test_validate_invalid_values(self):
        """测试无效值的验证"""
        # 负数GOOD_QTY
        data_negative_qty = self.sample_lot_data.copy()
        data_negative_qty['GOOD_QTY'] = -100
        model = SchedulingResultModel.from_algorithm_result(data_negative_qty)
        errors = model.validate()
        self.assertGreater(len(errors), 0)
        self.assertTrue(any('GOOD_QTY' in error for error in errors))
        
        # 无效PRIORITY
        data_invalid_priority = self.sample_lot_data.copy()
        data_invalid_priority['PRIORITY'] = 0
        model = SchedulingResultModel.from_algorithm_result(data_invalid_priority)
        errors = model.validate()
        self.assertGreater(len(errors), 0)
        self.assertTrue(any('PRIORITY' in error for error in errors))
    
    def test_to_dict(self):
        """测试字典转换"""
        model = SchedulingResultModel.from_algorithm_result(self.sample_lot_data)
        result_dict = model.to_dict()
        
        # 验证关键字段存在
        self.assertIn('LOT_ID', result_dict)
        self.assertIn('PRIORITY', result_dict)
        self.assertIn('match_type', result_dict)
        self.assertIn('comprehensive_score', result_dict)
        
        # 验证值正确
        self.assertEqual(result_dict['LOT_ID'], 'LOT123456')
        self.assertEqual(result_dict['match_type'], '完全匹配')


class TestSchedulingResultPersister(unittest.TestCase):
    """测试SchedulingResultPersister持久化管理器"""
    
    def setUp(self):
        """设置测试数据"""
        self.sample_models = [
            SchedulingResultModel(
                LOT_ID='LOT001',
                PRIORITY=1,
                GOOD_QTY=1000,
                PROD_ID='PROD001',
                match_type='完全匹配',
                comprehensive_score=95.5
            ),
            SchedulingResultModel(
                LOT_ID='LOT002',
                PRIORITY=2,
                GOOD_QTY=500,
                PROD_ID='PROD002',
                match_type='小改机',
                comprehensive_score=80.0
            )
        ]
    
    @patch('app.models.production.scheduling_results.db')
    def test_save_results_success(self, mock_db):
        """测试成功保存结果"""
        # 模拟数据库操作
        mock_session = Mock()
        mock_db.session = mock_session
        mock_session.begin.return_value.__enter__ = Mock()
        mock_session.begin.return_value.__exit__ = Mock(return_value=None)
        mock_session.execute.return_value.scalar.return_value = 2  # 验证插入数量
        
        persister = SchedulingResultPersister()
        result = persister.save_results(self.sample_models)
        
        self.assertTrue(result)
        # 验证调用了数据库操作
        self.assertTrue(mock_session.execute.called)
    
    @patch('app.models.production.scheduling_results.db')
    def test_save_results_empty_list(self, mock_db):
        """测试保存空列表"""
        persister = SchedulingResultPersister()
        result = persister.save_results([])
        
        self.assertTrue(result)
        # 空列表应该不调用数据库操作
        mock_db.session.execute.assert_not_called()
    
    @patch('app.models.production.scheduling_results.db')
    def test_save_results_validation_failure(self, mock_db):
        """测试数据验证失败"""
        # 创建无效数据模型
        invalid_model = SchedulingResultModel(
            LOT_ID='',  # 空LOT_ID应该导致验证失败
            PRIORITY=0,  # 无效PRIORITY
            GOOD_QTY=-100,  # 负数GOOD_QTY
            PROD_ID=''  # 空PROD_ID
        )
        
        persister = SchedulingResultPersister()
        
        with self.assertRaises(ValueError):
            persister.save_results([invalid_model])
    
    @patch('app.models.production.scheduling_results.db')
    @patch('app.models.production.scheduling_results.datetime')
    def test_save_results_creates_backup(self, mock_datetime, mock_db):
        """测试保存时创建备份表"""
        # 模拟时间
        mock_datetime.now.return_value.strftime.return_value = "20250116_103000"
        
        # 模拟数据库操作
        mock_session = Mock()
        mock_db.session = mock_session
        mock_session.begin.return_value.__enter__ = Mock()
        mock_session.begin.return_value.__exit__ = Mock(return_value=None)
        mock_session.execute.return_value.scalar.return_value = 2
        
        persister = SchedulingResultPersister()
        persister.save_results(self.sample_models)
        
        # 验证创建了备份表
        calls = mock_session.execute.call_args_list
        backup_call = str(calls[0])
        self.assertIn('CREATE TABLE', backup_call)
        self.assertIn('lotprioritydone_backup_20250116_103000', backup_call)
    
    def test_get_complete_insert_sql(self):
        """测试获取完整插入SQL"""
        persister = SchedulingResultPersister()
        sql = persister._get_complete_insert_sql()
        
        # 验证SQL包含所有重要字段
        self.assertIn('INSERT INTO lotprioritydone', sql)
        self.assertIn('PRIORITY', sql)
        self.assertIn('LOT_ID', sql)
        self.assertIn('match_type', sql)
        self.assertIn('comprehensive_score', sql)
        self.assertIn('algorithm_version', sql)
        
        # 验证VALUES部分
        self.assertIn('VALUES', sql)
        self.assertIn(':PRIORITY', sql)
        self.assertIn(':match_type', sql)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_complete_workflow(self):
        """测试完整的工作流程"""
        # 模拟算法结果
        algorithm_result = {
            'LOT_ID': 'LOT_INTEGRATION_001',
            'PRIORITY': 1,
            'GOOD_QTY': 2000,
            'PROD_ID': 'PROD_INTEGRATION',
            'HANDLER_ID': 'EQP_INT_001',
            'match_type': '完全匹配',
            'comprehensive_score': 98.5,
            'processing_time': 1.5,
            'algorithm_version': 'v3.0-test'
        }
        
        # 1. 从算法结果创建模型
        model = SchedulingResultModel.from_algorithm_result(algorithm_result)
        
        # 2. 验证模型
        errors = model.validate()
        self.assertEqual(len(errors), 0, f"Model validation failed: {errors}")
        
        # 3. 转换为字典
        model_dict = model.to_dict()
        self.assertIn('LOT_ID', model_dict)
        self.assertEqual(model_dict['comprehensive_score'], 98.5)
        
        # 4. 验证关键字段正确映射
        self.assertEqual(model.LOT_ID, 'LOT_INTEGRATION_001')
        self.assertEqual(model.match_type, '完全匹配')
        self.assertEqual(model.comprehensive_score, 98.5)
        self.assertEqual(model.SCHEDULING_STATUS, 'SUCCESS')


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2) 