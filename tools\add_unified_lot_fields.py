#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一批次管理数据库扩展脚本
为et_wait_lot表添加必要字段以支持统一管理
"""

import logging
import mysql.connector
from mysql.connector import Error
from app.utils.mysql_config_helper import get_mysql_config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_unified_lot_fields():
    """为et_wait_lot表添加统一管理所需的字段"""
    try:
        # 获取数据库配置
        config = get_mysql_config()
        
        # 连接数据库
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'et_wait_lot'
        """)
        
        if cursor.fetchone()[0] == 0:
            logger.warning("⚠️ et_wait_lot表不存在，跳过字段添加")
            return False
        
        # 定义要添加的字段
        fields_to_add = [
            {
                'name': 'status',
                'definition': "status VARCHAR(20) DEFAULT 'WAITING' COMMENT '批次状态：WAITING=等待排产'",
                'index': 'idx_status'
            },
            {
                'name': 'priority_level',
                'definition': "priority_level INT DEFAULT 999 COMMENT '优先级等级，数值越小优先级越高'",
                'index': 'idx_priority_level'
            },
            {
                'name': 'last_updated',
                'definition': "last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'",
                'index': 'idx_last_updated'
            }
        ]
        
        logger.info("🔧 开始为et_wait_lot表添加统一管理字段...")
        
        for field in fields_to_add:
            try:
                # 检查字段是否已存在
                cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.columns 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'et_wait_lot' 
                    AND column_name = %s
                """, (field['name'],))
                
                if cursor.fetchone()[0] > 0:
                    logger.info(f"⚠️ 字段 {field['name']} 已存在，跳过")
                    continue
                
                # 添加字段
                alter_sql = f"ALTER TABLE et_wait_lot ADD COLUMN {field['definition']}"
                cursor.execute(alter_sql)
                logger.info(f"✅ 已添加字段: {field['name']}")
                
                # 添加索引
                if field['index']:
                    try:
                        index_sql = f"CREATE INDEX {field['index']} ON et_wait_lot ({field['name']})"
                        cursor.execute(index_sql)
                        logger.info(f"✅ 已添加索引: {field['index']}")
                    except Error as e:
                        if "Duplicate key name" in str(e):
                            logger.info(f"⚠️ 索引 {field['index']} 已存在，跳过")
                        else:
                            logger.warning(f"⚠️ 添加索引失败: {e}")
                
            except Error as e:
                if "Duplicate column name" in str(e):
                    logger.info(f"⚠️ 字段 {field['name']} 已存在，跳过")
                else:
                    logger.error(f"❌ 添加字段 {field['name']} 失败: {e}")
                    raise e
        
        # 提交更改
        conn.commit()
        logger.info("✅ 所有字段添加完成")
        
        # 验证字段是否成功添加
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'et_wait_lot' 
            AND column_name IN ('status', 'priority_level', 'last_updated')
            ORDER BY ORDINAL_POSITION
        """)
        
        added_fields = cursor.fetchall()
        logger.info("📋 添加的字段验证:")
        for field in added_fields:
            logger.info(f"   - {field[0]} ({field[1]}) DEFAULT {field[2]} - {field[3]}")
        
        cursor.close()
        conn.close()
        
        logger.info("🎉 et_wait_lot表扩展完成！")
        return True
        
    except Error as e:
        logger.error(f"❌ 数据库操作失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 扩展失败: {e}")
        return False

def verify_unified_lot_schema():
    """验证统一批次管理所需的表结构"""
    try:
        config = get_mysql_config()
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        logger.info("🔍 验证统一批次管理表结构...")
        
        # 检查et_wait_lot表结构
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'et_wait_lot'
            ORDER BY ORDINAL_POSITION
        """)
        
        et_wait_lot_columns = cursor.fetchall()
        logger.info("📋 et_wait_lot表结构:")
        for col in et_wait_lot_columns:
            logger.info(f"   - {col[0]} ({col[1]}) {'NULL' if col[2] == 'YES' else 'NOT NULL'} DEFAULT {col[3]} - {col[4]}")
        
        # 检查lotprioritydone表结构
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'lotprioritydone'
            ORDER BY ORDINAL_POSITION
        """)
        
        lotprioritydone_columns = cursor.fetchall()
        logger.info("📋 lotprioritydone表结构:")
        for col in lotprioritydone_columns:
            logger.info(f"   - {col[0]} ({col[1]}) {'NULL' if col[2] == 'YES' else 'NOT NULL'} DEFAULT {col[3]} - {col[4]}")
        
        # 检查关键字段是否存在
        required_et_wait_lot_fields = ['LOT_ID', 'DEVICE', 'STAGE', 'GOOD_QTY', 'PROD_ID', 'status', 'priority_level']
        required_lotprioritydone_fields = ['LOT_ID', 'DEVICE', 'STAGE', 'GOOD_QTY', 'PROD_ID', 'PRIORITY', 'SCHEDULING_STATUS']
        
        et_wait_lot_field_names = [col[0] for col in et_wait_lot_columns]
        lotprioritydone_field_names = [col[0] for col in lotprioritydone_columns]
        
        missing_et_wait_lot_fields = [f for f in required_et_wait_lot_fields if f not in et_wait_lot_field_names]
        missing_lotprioritydone_fields = [f for f in required_lotprioritydone_fields if f not in lotprioritydone_field_names]
        
        if missing_et_wait_lot_fields:
            logger.warning(f"⚠️ et_wait_lot表缺少字段: {missing_et_wait_lot_fields}")
        else:
            logger.info("✅ et_wait_lot表结构完整")
        
        if missing_lotprioritydone_fields:
            logger.warning(f"⚠️ lotprioritydone表缺少字段: {missing_lotprioritydone_fields}")
        else:
            logger.info("✅ lotprioritydone表结构完整")
        
        cursor.close()
        conn.close()
        
        return len(missing_et_wait_lot_fields) == 0 and len(missing_lotprioritydone_fields) == 0
        
    except Exception as e:
        logger.error(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 统一批次管理数据库扩展脚本")
    print("="*50)
    
    # 先验证现有结构
    print("\n1️⃣ 验证现有表结构...")
    verify_unified_lot_schema()
    
    # 添加必要字段
    print("\n2️⃣ 添加统一管理字段...")
    success = add_unified_lot_fields()
    
    if success:
        print("\n3️⃣ 再次验证表结构...")
        verify_unified_lot_schema()
        print("\n🎉 数据库扩展完成！统一批次管理功能已就绪。")
    else:
        print("\n❌ 数据库扩展失败！请检查错误日志。") 