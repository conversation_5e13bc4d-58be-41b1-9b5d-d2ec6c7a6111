#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time

# 等待服务器启动
print("等待服务器启动...")
time.sleep(5)

# 测试API路径
api_paths = [
    "http://127.0.0.1:5000/api/v2/production/unified-lot-management/lots",
    "http://127.0.0.1:5000/api/v2/production/unified-lot-management/status-counts",
    "http://127.0.0.1:5000/api/v2/production",
    "http://127.0.0.1:5000/api/v2/production/health"
]

for path in api_paths:
    try:
        print(f"\n测试路径: {path}")
        response = requests.get(path, timeout=5)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 路径可访问")
        elif response.status_code == 404:
            print("❌ 路径不存在 (404)")
        else:
            print(f"⚠️  状态码: {response.status_code}")
        
        # 显示响应内容的前200个字符
        content = response.text[:200]
        print(f"响应内容: {content}")
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，服务器未启动")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 错误: {e}")