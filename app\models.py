﻿from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from app import db, login_manager
import logging
import json
import os
from app.config.menu_config import get_menu_by_id, get_all_menu_ids
import base64

# 配置日志
logger = logging.getLogger(__name__)

class UserPermission(db.Model):
    __tablename__ = 'user_permissions'

    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), db.<PERSON>Key('users.username', ondelete='CASCADE'))
    menu_id = db.Column(db.Integer)  # 不再使用外键关联到menu_settings?
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    granted_by = db.Column(db.String(64), comment='')

class User(UserMixin, db.Model):
    __tablename__ = 'users'

    
    username = db.Column(db.String(64), primary_key=True)
    password_hash = db.Column(db.String(1000))  # 增加长度以匹配迁移后的表结构
    role = db.Column(db.String(20))  # admin/tech/op
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)
    
    # 添加权限关联 - 直接使用user_permissions表,不再通过MenuSetting?
    permissions_relation = db.relationship('UserPermission', backref='user', lazy='dynamic', 
                                          cascade='all, delete-orphan')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
        logger.info(f"为用户{self.username} 设置值密码哈希: {self.password_hash}")
    
    def check_password(self, password):
        if not self.password_hash:
            logger.warning(f"用户 {self.username} 没有密码哈希")
            return False
        
        result = check_password_hash(self.password_hash, password)
        logger.info(f"用户 {self.username} 密码验证结果: {result}")
        return result
    
    @property
    def id(self):
        """为了兼容Flask-Login,提供id属?"""
        return self.username
    
    def get_id(self):
        return self.username
    
    def has_permission(self, menu_id):
        """检查用户是否有权限访问指定菜单"""
        if self.role == 'admin':  # 管理员拥有所有权限
            return True
        
        # 从user_permissions表中查询
        return UserPermission.query.filter_by(
            username=self.username, 
            menu_id=menu_id
        ).first() is not None
    
    def get_permissions(self):
        """获取用户的所有菜单权限ID"""
        if self.role == 'admin':  # 管理员拥有所有权限
            return get_all_menu_ids()
        
        # 从关联表中获取所有menu_id
        permissions = UserPermission.query.filter_by(username=self.username).all()
        return [p.menu_id for p in permissions]
    
    def set_permissions(self, menu_ids):
        """设置用户的菜单权限"""
        if self.role == 'admin':  # 管理员权限不可修改
            logger.info(f"跳过修改管理员{self.username} 的权限")
            return
        
        # 记录当前的权限
        current_permissions = self.get_permissions()
        logger.info(f"用户 {self.username} 当前权限: {current_permissions}")
        logger.info(f"准备设置值新权限 {menu_ids}")
        
        # 清除现有权限
        deleted = UserPermission.query.filter_by(username=self.username).delete()
        logger.info(f"已删除{deleted} 条现有权限记录")
        
        # 获取所有有效的菜单ID
        all_menu_ids = get_all_menu_ids()
        
        # 添加新权限
        added_count = 0
        for menu_id in menu_ids:
            menu_id = int(menu_id)  # 确保是整数类型
            
            # 检查菜单ID是否有效 (只要在有效ID列表中即可)
            if menu_id in all_menu_ids:
                permission = UserPermission(username=self.username, menu_id=menu_id)
                db.session.add(permission)
                added_count += 1
            else:
                logger.warning(f"忽略无效的菜单ID: {menu_id}")
        
        logger.info(f"为用户{self.username} 添加了{added_count} 条新权限记录")

    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login = datetime.utcnow()
        db.session.commit()

class MenuSetting(db.Model):
    """菜单设置值 - 已迁移到配置文件,保留模型以兼容旧数"""
    __tablename__ = 'menu_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    menu_name = db.Column(db.String(128), unique=True)
    # 暂时注释掉外键关系,避免删除用户时的约束问题
    # parent_id = db.Column(db.Integer, db.ForeignKey('menu_settings.id'), nullable=True)
    parent_id = db.Column(db.Integer, nullable=True)
    is_visible = db.Column(db.Boolean, default=True)
    order = db.Column(db.Integer)
    icon = db.Column(db.String(64))
    route = db.Column(db.String(128))
    
    # 暂时注释掉关系定义,避免删除用户时的约束问题
    # children = db.relationship('MenuSetting', backref=db.backref('parent', remote_side=[id]))

# 生产管理相关模型
class Product(db.Model):
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(64), unique=True, index=True)
    name = db.Column(db.String(128))
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ProductionOrder(db.Model):
    __tablename__ = 'production_orders'
    
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(64), unique=True, index=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'))
    quantity = db.Column(db.Integer)
    priority = db.Column(db.String(20), default='medium')  # high/medium/low
    status = db.Column(db.String(20))  # pending/in_progress/completed/cancelled
    scheduled_start = db.Column(db.DateTime)
    scheduled_end = db.Column(db.DateTime)
    actual_start = db.Column(db.DateTime)
    actual_end = db.Column(db.DateTime)
    created_by = db.Column(db.String(64), db.ForeignKey('users.username'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    product = db.relationship('Product', backref='production_orders')
    # 暂时注释掉,因为可能导致删除用户时的外键约束问题
    # creator = db.relationship('User', backref='production_orders')

class ProductionSchedule(db.Model):
    __tablename__ = 'production_schedules'
    
    id = db.Column(db.Integer, primary_key=True)
    production_order_id = db.Column(db.Integer, db.ForeignKey('production_orders.id'))
    resource_id = db.Column(db.Integer, db.ForeignKey('resources.id'))
    start_time = db.Column(db.DateTime)
    end_time = db.Column(db.DateTime)
    status = db.Column(db.String(20))  # scheduled/in_progress/completed/cancelled
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    production_order = db.relationship('ProductionOrder', backref='schedules')
    resource = db.relationship('Resource', backref='schedules')

# 订单管理相关模型
class CustomerOrder(db.Model):
    __tablename__ = 'customer_orders'
    
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(64), unique=True, index=True)
    customer_name = db.Column(db.String(128))
    status = db.Column(db.String(20))  # new/confirmed/in_production/completed/cancelled
    total_amount = db.Column(db.Float)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class OrderItem(db.Model):
    __tablename__ = 'order_items'
    
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('customer_orders.id'))
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'))
    quantity = db.Column(db.Integer)
    unit_price = db.Column(db.Float)
    total_price = db.Column(db.Float)
    
    order = db.relationship('CustomerOrder', backref='items')
    product = db.relationship('Product', backref='order_items')

# 资源管理相关模型
class Resource(db.Model):
    """资源基类"""
    __tablename__ = 'resources'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)  # 设备编号
    name = db.Column(db.String(50), nullable=False)  # 设备名称
    type = db.Column(db.String(20), nullable=False)  # 设备类型:sorter/tester/fixture
    status = db.Column(db.String(20), default='available')  # 状态:available/busy/maintenance
    specifications = db.Column(db.Text)  # 设备规格(JSON格式)
    location = db.Column(db.String(50))  # 设备位置
    maintenance_cycle = db.Column(db.Integer)  # 维护周期(天)
    last_maintenance = db.Column(db.DateTime)  # 上次维护时间
    next_maintenance = db.Column(db.DateTime)  # 下次维护时间
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'type': self.type,
            'status': self.status,
            'specifications': json.loads(self.specifications) if self.specifications else {},
            'location': self.location,
            'maintenance_cycle': self.maintenance_cycle,
            'last_maintenance': self.last_maintenance.isoformat() if self.last_maintenance else None,
            'next_maintenance': self.next_maintenance.isoformat() if self.next_maintenance else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class TestSpec(db.Model):
    """测试规范"""
    __tablename__ = 'test_specs'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 规范名称
    version = db.Column(db.String(20), nullable=False)  # 版本号
    status = db.Column(db.String(20), default='draft')  # 状态:draft/active/archived
    description = db.Column(db.Text)  # 规范描述
    parameters = db.Column(db.Text)  # 测试参数(JSON格式)
    owner = db.Column(db.String(50))  # 负责人
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'version': self.version,
            'status': self.status,
            'description': self.description,
            'parameters': json.loads(self.parameters) if self.parameters else [],
            'owner': self.owner,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class MaintenanceRecord(db.Model):
    """维护记录"""
    __tablename__ = 'maintenance_records'
    
    id = db.Column(db.Integer, primary_key=True)
    resource_id = db.Column(db.Integer, db.ForeignKey('resources.id'), nullable=False)
    maintenance_type = db.Column(db.String(20), nullable=False)  # 维护类型:routine/repair/calibration
    description = db.Column(db.Text)  # 维护描述
    start_time = db.Column(db.DateTime, nullable=False)  # 开始时间
    end_time = db.Column(db.DateTime)  # 结束时间
    status = db.Column(db.String(20), default='pending')  # 状态:pending/in_progress/completed
    operator = db.Column(db.String(50))  # 操作员
    result = db.Column(db.Text)  # 维护结果
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'resource_id': self.resource_id,
            'maintenance_type': self.maintenance_type,
            'description': self.description,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'operator': self.operator,
            'result': self.result,
            'created_at': self.created_at.isoformat()
        }

class ResourceUsageLog(db.Model):
    """资源使用日志"""
    __tablename__ = 'resource_usage_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    resource_id = db.Column(db.Integer, db.ForeignKey('resources.id'), nullable=False)
    order_id = db.Column(db.String(20))  # 关联订单号
    start_time = db.Column(db.DateTime, nullable=False)  # 开始使用时间
    end_time = db.Column(db.DateTime)  # 结束使用时间
    operator = db.Column(db.String(50))  # 操作员
    usage_type = db.Column(db.String(20))  # 使用类型
    details = db.Column(db.Text)  # 使用详情(JSON格式)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'resource_id': self.resource_id,
            'order_id': self.order_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'operator': self.operator,
            'usage_type': self.usage_type,
            'details': json.loads(self.details) if self.details else {},
            'created_at': self.created_at.isoformat()
        }

# WIP跟踪相关模型
class WIPRecord(db.Model):
    __tablename__ = 'wip_records'
    
    id = db.Column(db.Integer, primary_key=True)
    production_order_id = db.Column(db.Integer, db.ForeignKey('production_orders.id'))
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'))
    quantity = db.Column(db.Integer)
    status = db.Column(db.String(20))
    location = db.Column(db.String(128))
    recorded_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    production_order = db.relationship('ProductionOrder', backref='wip_records')
    product = db.relationship('Product', backref='wip_records')

# 用户操作员日志模型
class UserActionLog(db.Model):
    __tablename__ = 'user_action_logs'

    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64))  # 移除外键约束,避免跨数据库外键问题
    action_type = db.Column(db.String(64))  # login, logout, create, update, delete, etc.
    target_model = db.Column(db.String(64))  # users, products, orders, etc.
    target_id = db.Column(db.String(128))  # ID of the affected record
    details = db.Column(db.Text)  # JSON or text details of the action
    ip_address = db.Column(db.String(64))
    user_agent = db.Column(db.String(256))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 暂时注释掉,因为可能导致删除用户时的外键约束问题
    # user = db.relationship('User', backref='action_logs')
    
    @classmethod
    def log_action(cls, username, action_type, target_model, target_id=None, details=None, request=None):
        """记录用户操作"""
        log_entry = cls(
            username=username,
            action_type=action_type,
            target_model=target_model,
            target_id=str(target_id) if target_id else None,
            details=details
        )
        
        # 获取请求信息
        if request:
            log_entry.ip_address = request.remote_addr
            log_entry.user_agent = request.user_agent.string
            
        db.session.add(log_entry)
        try:
            db.session.commit()
        except:
            db.session.rollback()
        
        return log_entry

class SystemSetting(db.Model):
    """系统设置值"""
    __tablename__ = 'system_settings'

    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(64), nullable=False)  # 设置值键名
    value = db.Column(db.Text)  # 设置值
    description = db.Column(db.String(256))  # 设置值描述
    user_id = db.Column(db.String(64), nullable=True)  # 用户ID,为NULL表示全局设置值 (不使用外键,因为user表在不同数据
    setting_type = db.Column(db.String(20), default='system')  # 设置值类型:system, user, path, etc.
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# 新增表的模型定义
class CT(db.Model):
    """产品生产周期参考表"""
    __tablename__ = 'ct'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    PRODUCT = db.Column(db.String(50), nullable=True, comment='')
    STAGE = db.Column(db.String(50), nullable=True, comment='')
    CT_VALUE = db.Column(db.Float, nullable=True, comment='')
    UNIT = db.Column(db.String(10), nullable=True, comment='')
    REMARK = db.Column(db.Text, nullable=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<CT {self.PRODUCT}-{self.STAGE}>'

class ET_RECIPE_FILE(db.Model):
    """FT设备配方资源"""
    __tablename__ = 'et_recipe_file'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    DEVICE = db.Column(db.String(50), nullable=True, comment='')
    CHIP_ID = db.Column(db.String(50), nullable=True, comment='')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='')
    STAGE = db.Column(db.String(50), nullable=True, comment='')
    RECIPE_FILE_NAME = db.Column(db.String(100), nullable=True, comment='')
    RECIPE_FILE_PATH = db.Column(db.String(200), nullable=True, comment='')
    KIT_PN = db.Column(db.String(50), nullable=True, comment='')
    SOCKET_PN = db.Column(db.String(50), nullable=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<ET_RECIPE_FILE {self.DEVICE}-{self.STAGE}>'

class ET_UPH_EQP(db.Model):
    """产品UPH数据"""
    __tablename__ = 'et_uph_eqp'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    DEVICE = db.Column(db.String(50), nullable=True, comment='')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='')
    STAGE = db.Column(db.String(50), nullable=True, comment='')
    UPH = db.Column(db.Integer, nullable=True, comment='')
    SORTER_MODEL = db.Column(db.String(50), nullable=True, comment='')
    FAC_ID = db.Column(db.String(50), nullable=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<ET_UPH_EQP {self.DEVICE}-{self.STAGE}>'

class TCC_INV(db.Model):
    """测试硬件资源"""
    __tablename__ = 'TCC_INV'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    INVENTORY_ID = db.Column(db.String(50), nullable=True, comment='')
    INVENTORY_TYPE = db.Column(db.String(50), nullable=True, comment='')
    INVENTORY_STATUS = db.Column(db.String(50), nullable=True, comment='')
    LOCATION = db.Column(db.String(50), nullable=True, comment='')
    REMARK = db.Column(db.Text, nullable=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<TCC_INV {self.INVENTORY_ID}>'

class WIP_LOT(db.Model):
    """在制品信息表 - 基于实际数据库结构"""
    __tablename__ = 'wip_lot'
    
    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    
    # 核心批次信息
    LOT_ID = db.Column(db.String(50), nullable=False, comment='')
    LOT_TYPE = db.Column(db.String(50), nullable=True, comment='')
    DET_LOT_TYPE = db.Column(db.String(50), nullable=True, comment='')
    
    # 数量信息
    LOT_QTY = db.Column(db.Integer, nullable=True, comment='')
    SUB_QTY = db.Column(db.Float, nullable=True, comment='')
    UNIT = db.Column(db.String(20), nullable=True, comment='')
    SUB_UNIT = db.Column(db.Float, nullable=True, comment='')
    
    # 状态信息
    WIP_STATE = db.Column(db.String(50), nullable=True, comment='')
    PROC_STATE = db.Column(db.String(50), nullable=True, comment='')
    HOLD_STATE = db.Column(db.String(50), nullable=True, comment='')
    RW_STATE = db.Column(db.String(50), nullable=True, comment='')
    REPAIR_STATE = db.Column(db.String(50), nullable=True, comment='')
    QC_STATE = db.Column(db.Float, nullable=True, comment='')
    
    # 产品信息
    PROD_ID = db.Column(db.String(50), nullable=True, comment='')
    DEVICE = db.Column(db.String(50), nullable=True, comment='')
    CHIP_ID = db.Column(db.String(50), nullable=True, comment='')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='')
    
    # 工艺信息
    STAGE = db.Column(db.String(50), nullable=True, comment='')
    PROC_RULE_ID = db.Column(db.String(50), nullable=True, comment='')
    PRP_ID = db.Column(db.String(50), nullable=True, comment='')
    FLOW_ID = db.Column(db.String(50), nullable=True, comment='')
    PRP_VER = db.Column(db.Float, nullable=True, comment='')
    FLOW_VER = db.Column(db.Float, nullable=True, comment='')
    OPER_VER = db.Column(db.Float, nullable=True, comment='')
    
    # 设备信息
    EQP_ID = db.Column(db.String(50), nullable=True, comment='')
    AREA_ID = db.Column(db.String(50), nullable=True, comment='')
    
    # 数量详情
    LOT_IN_QTY = db.Column(db.Integer, nullable=True, comment='')
    LOT_OUT_QTY = db.Column(db.Integer, nullable=True, comment='')
    GOOD_QTY = db.Column(db.Integer, nullable=True, comment='')
    NG_QTY = db.Column(db.Integer, nullable=True, comment='')
    
    # 批次关系
    ROOT_LOT_ID = db.Column(db.String(50), nullable=True, comment='')
    PARENT_LOT_ID = db.Column(db.String(50), nullable=True, comment='')
    CHILD_LOT_ID = db.Column(db.String(50), nullable=True, comment='')
    CUST_LOT_ID = db.Column(db.String(50), nullable=True, comment='')
    
    # 优先级和计划
    HOT_TYPE = db.Column(db.String(50), nullable=True, comment='')
    PLAN_START_DATE = db.Column(db.Float, nullable=True, comment='')
    
    # 时间信息
    OPER_CHANGE_TIME = db.Column(db.String(50), nullable=True, comment='')
    JOB_START_TIME = db.Column(db.String(50), nullable=True, comment='')
    JOB_END_TIME = db.Column(db.String(50), nullable=True, comment='')
    RELEASE_TIME = db.Column(db.String(50), nullable=True, comment='')
    
    # 工厂信息
    FAC_ID = db.Column(db.String(50), nullable=True, comment='')
    SUB_FAC = db.Column(db.String(50), nullable=True, comment='')
    
    # 订单信息
    WORK_ORDER_ID = db.Column(db.String(50), nullable=True, comment='')
    WORK_ORDER_VER = db.Column(db.Integer, nullable=True, comment='')
    PO_ID = db.Column(db.String(50), nullable=True, comment='')
    
    # 事件信息
    EVENT = db.Column(db.String(50), nullable=True, comment='')
    EVENT_KEY = db.Column(db.String(50), nullable=True, comment='')
    EVENT_TIME = db.Column(db.String(50), nullable=True, comment='')
    EVENT_USER = db.Column(db.String(50), nullable=True, comment='')
    EVENT_MSG = db.Column(db.String(200), nullable=True, comment='')
    
    # 创建信息
    created_at = db.Column(db.String(50), nullable=True, comment='')
    CREATE_USER = db.Column(db.String(50), nullable=True, comment='')
    
    # 质量信息
    UPH = db.Column(db.Float, nullable=True, comment='')
    ORT_QTY = db.Column(db.Float, nullable=True, comment='')
    IQC_QTY = db.Column(db.Float, nullable=True, comment='')
    
    # 兼容性字段(为了向后兼容)
    
    # 注意:实际数据库48个字段,这里只定义了主要的字段
    # 其他字段可以通过动态查询获取

    def __repr__(self):
        return f'<WIP_LOT {self.LOT_ID}>'
    
    def to_dict(self):
        """转换为字典,包含所有非空字段"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if value is not None:
                result[column.name] = value
        return result

class ET_WAIT_LOT(db.Model):
    """待排产批次表"""
    __tablename__ = 'et_wait_lot'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    LOT_ID = db.Column(db.String(50), nullable=False, comment='')
    LOT_TYPE = db.Column(db.String(50), nullable=True, comment='')
    GOOD_QTY = db.Column(db.Integer, nullable=True, comment='')
    PROD_ID = db.Column(db.String(50), nullable=True, comment='')
    DEVICE = db.Column(db.String(50), nullable=True, comment='')
    CHIP_ID = db.Column(db.String(50), nullable=True, comment='')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='')
    PO_ID = db.Column(db.String(50), nullable=True, comment='')
    STAGE = db.Column(db.String(50), nullable=True, comment='')
    WIP_STATE = db.Column(db.String(50), nullable=True, comment='')
    PROC_STATE = db.Column(db.String(50), nullable=True, comment='')
    HOLD_STATE = db.Column(db.String(50), nullable=True, comment='')
    FLOW_ID = db.Column(db.String(50), nullable=True, comment='')
    FLOW_VER = db.Column(db.Integer, nullable=True, comment='')
    RELEASE_TIME = db.Column(db.String(50), nullable=True, comment='')
    FAC_ID = db.Column(db.String(50), nullable=True, comment='')
    created_at = db.Column(db.String(50), nullable=True, comment='')

    def __repr__(self):
        return f'<ET_WAIT_LOT {self.LOT_ID}>'

class ET_FT_TEST_SPEC(db.Model):
    """FT测试规范"""
    __tablename__ = 'et_ft_test_spec'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    TEST_SPEC_ID = db.Column(db.String(100), nullable=True, comment='')
    TEST_SPEC_NAME = db.Column(db.String(100), nullable=True, comment='')
    TEST_SPEC_VER = db.Column(db.String(50), nullable=True, comment='')
    STAGE = db.Column(db.String(50), nullable=True, comment='')
    TESTER = db.Column(db.String(50), nullable=True, comment='')
    INV_ID = db.Column(db.String(50), nullable=True, comment='')
    TEST_SPEC_TYPE = db.Column(db.String(50), nullable=True, comment='')
    APPROVAL_STATE = db.Column(db.String(50), nullable=True, comment='')
    ACTV_YN = db.Column(db.String(10), nullable=True, comment='')
    PROD_ID = db.Column(db.String(50), nullable=True, comment='')
    DEVICE = db.Column(db.String(50), nullable=True, comment='')
    CHIP_ID = db.Column(db.String(50), nullable=True, comment='')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='')
    COMPANY_ID = db.Column(db.String(50), nullable=True, comment='')
    TEST_AREA = db.Column(db.String(50), nullable=True, comment='')
    HANDLER = db.Column(db.String(50), nullable=True, comment='')
    TEMPERATURE = db.Column(db.String(50), nullable=True, comment='')
    FT_PROGRAM = db.Column(db.String(100), nullable=True, comment='')
    QA_PROGRAM = db.Column(db.String(100), nullable=True, comment='')
    GU_PROGRAM = db.Column(db.String(100), nullable=True, comment='')
    TB_PN = db.Column(db.String(50), nullable=True, comment='')
    HB_PN = db.Column(db.String(50), nullable=True, comment='')
    TEST_TIME = db.Column(db.Float, nullable=True, comment='')
    UPH = db.Column(db.Integer, nullable=True, comment='')
    STANDARD_YIELD = db.Column(db.Float, nullable=True, comment='')
    LOW_YIELD = db.Column(db.Float, nullable=True, comment='')
    DOWN_YIELD = db.Column(db.Float, nullable=True, comment='')
    ORT_QTY = db.Column(db.Integer, nullable=True, comment='')
    REMAIN_QTY = db.Column(db.Integer, nullable=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<ET_FT_TEST_SPEC {self.TEST_SPEC_ID}>'

class EQP_STATUS(db.Model):
    """设备状态表"""
    __tablename__ = 'eqp_status'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    HANDLER_ID = db.Column(db.String(50), nullable=True, comment='')
    TESTER_ID = db.Column(db.String(50), nullable=True, comment='')
    STATUS = db.Column(db.String(50), nullable=True, comment='')
    LOT_ID = db.Column(db.String(50), nullable=True, comment='')
    DEVICE = db.Column(db.String(50), nullable=True, comment='')
    STAGE = db.Column(db.String(50), nullable=True, comment='')
    EQP_TYPE = db.Column(db.String(50), nullable=True, comment='')
    EQP_CLASS = db.Column(db.String(50), nullable=True, comment='')
    TEMPERATURE_RANGE = db.Column(db.String(50), nullable=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<EQP_STATUS {self.HANDLER_ID}-{self.STATUS}>'

@login_manager.user_loader
def load_user(username):
    return User.query.get(username)

# 邮件附件自动处理系统相关模型
class EmailConfig(db.Model):
    """邮箱配置"""
    __tablename__ = 'email_configs'

    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False, comment='')
    server = db.Column(db.String(128), nullable=False, comment='')
    port = db.Column(db.Integer, nullable=False, comment='')
    email = db.Column(db.String(128), nullable=False, comment='')
    password = db.Column(db.String(128), nullable=False, comment='')
    senders = db.Column(db.Text, nullable=True, comment='')
    subjects = db.Column(db.Text, nullable=True, comment='')
    check_interval = db.Column(db.Integer, default=60, comment='')
    work_start_time = db.Column(db.String(10), default='08:00', comment='')
    work_end_time = db.Column(db.String(10), default='18:00', comment='')
    enabled = db.Column(db.Boolean, default=False, comment='')
    download_path = db.Column(db.String(256), nullable=False, comment='')
    use_date_folder = db.Column(db.Boolean, default=True, comment='')
    fetch_days = db.Column(db.Integer, default=10, comment='')
    created_by = db.Column(db.String(64), comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')
    
    # 暂时注释掉,因为可能导致删除用户时的外键约束问题
    # creator = db.relationship('User', backref='email_configs')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'server': self.server,
            'port': self.port,
            'email': self.email,
            'senders': self.senders,
            'subjects': self.subjects,
            'check_interval': self.check_interval,
            'work_start_time': self.work_start_time,
            'work_end_time': self.work_end_time,
            'enabled': self.enabled,
            'download_path': self.download_path,
            'use_date_folder': self.use_date_folder,
            'fetch_days': self.fetch_days,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class ExcelMapping(db.Model):
    """Excel字段映射配置"""
    __tablename__ = 'excel_mappings'

    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False, comment='')
    sheet_name = db.Column(db.String(64), nullable=True, comment='')
    start_row = db.Column(db.Integer, default=2, comment='')
    header_row = db.Column(db.Integer, default=1, comment='')
    field_mappings = db.Column(db.Text, nullable=False, comment='')
    key_fields = db.Column(db.String(256), nullable=False, comment='')
    date_format = db.Column(db.String(64), default='%Y-%m-%d', comment='')
    created_by = db.Column(db.String(64), comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')
    
    # 暂时注释掉,因为可能导致删除用户时的外键约束问题
    # creator = db.relationship('User', backref='excel_mappings')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'sheet_name': self.sheet_name,
            'start_row': self.start_row,
            'header_row': self.header_row,
            'field_mappings': json.loads(self.field_mappings) if self.field_mappings else {},
            'key_fields': self.key_fields,
            'date_format': self.date_format,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class EmailAttachment(db.Model):
    """邮件附件记录"""
    __tablename__ = 'email_attachments'

    
    id = db.Column(db.Integer, primary_key=True)
    email_config_id = db.Column(db.Integer, db.ForeignKey('email_configs.id'), comment='')
    message_id = db.Column(db.String(256), nullable=False, comment='')
    sender = db.Column(db.String(128), nullable=False, comment='')
    subject = db.Column(db.String(256), nullable=False, comment='')
    receive_date = db.Column(db.DateTime, nullable=False, comment='')
    filename = db.Column(db.String(256), nullable=False, comment='')
    file_path = db.Column(db.String(512), nullable=False, comment='')
    file_size = db.Column(db.Integer, nullable=False, comment='')
    processed = db.Column(db.Boolean, default=False, comment='')
    process_date = db.Column(db.DateTime, nullable=True, comment='')
    process_result = db.Column(db.String(64), nullable=True, comment='')
    process_message = db.Column(db.Text, nullable=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    
    email_config = db.relationship('EmailConfig', backref='attachments')
    
    def to_dict(self):
        return {
            'id': self.id,
            'email_config_id': self.email_config_id,
            'message_id': self.message_id,
            'sender': self.sender,
            'subject': self.subject,
            'receive_date': self.receive_date.strftime('%Y-%m-%d %H:%M:%S') if self.receive_date else None,
            'filename': self.filename,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'processed': self.processed,
            'process_date': self.process_date.strftime('%Y-%m-%d %H:%M:%S') if self.process_date else None,
            'process_result': self.process_result,
            'process_message': self.process_message,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }

class OrderData(db.Model):
    """完整的订单数据表 - 包含横向信息和纵向数据的所有字段"""
    __tablename__ = 'order_data'
    
    # 主键和基础信息
    id = db.Column(db.Integer, primary_key=True)
    
    # === 横向信息字段 (来自Excel表3 ===
    document_type = db.Column(db.String(50), comment='')
    document_number = db.Column(db.String(50), comment='')
    processing_type = db.Column(db.String(100), comment='')
    processing_status = db.Column(db.String(50), default='pending', comment='处理状态')
    contractor_name = db.Column(db.String(200), comment='')
    contractor_contact = db.Column(db.String(100), comment='')
    contractor_address = db.Column(db.String(500), comment='')
    contractor_phone = db.Column(db.String(50), comment='')
    contractor_email = db.Column(db.String(100), comment='')
    client_name = db.Column(db.String(200), comment='')
    
    # === 纵向表格数据字段 (来自表格 ===
    order_number = db.Column(db.String(64), nullable=False, index=True, comment='')
    product_name = db.Column(db.String(200), comment='')
    circuit_name = db.Column(db.String(100), comment='')
    chip_id = db.Column(db.String(50), comment='芯片ID')
    wafer_size = db.Column(db.String(20), comment='')
    package_qty = db.Column(db.Integer, comment='')
    package_pieces = db.Column(db.Integer, comment='')
    diffusion_batch = db.Column(db.String(100), comment='')
    wafer_number = db.Column(db.String(100), comment='')
    assembly_method = db.Column(db.String(100), comment='')
    drawing_number = db.Column(db.String(100), comment='')
    package_form = db.Column(db.String(100), comment='')
    stamp_line1 = db.Column(db.String(200), comment='')
    stamp_line2 = db.Column(db.String(200), comment='')
    stamp_line3 = db.Column(db.String(200), comment='')
    other_notes = db.Column(db.Text, comment='')
    delivery_date = db.Column(db.Date, comment='')
    env_requirement = db.Column(db.String(100), comment='')
    msl_requirement = db.Column(db.String(100), comment='')
    reliability_requirement = db.Column(db.String(200), comment='')
    print_pin_dot = db.Column(db.Boolean, default=False, comment='')
    pin_dot_position = db.Column(db.String(100), comment='')
    item_code = db.Column(db.String(100), comment='')
    shipping_address = db.Column(db.String(500), comment='')
    wafer_lot = db.Column(db.String(100), comment='')
    order_attribute = db.Column(db.String(100), comment='')
    
    # === 关键分类和状态字===
    lot_type = db.Column(db.String(50), comment='')
    classification = db.Column(db.Enum('engineering', 'production', 'unknown'), default='unknown', comment='')
    sequence_number = db.Column(db.Integer, comment='序列号')
    priority_score = db.Column(db.Float, default=0.0, comment='优先级分数')
    customer = db.Column(db.String(128), comment='')
    product_code = db.Column(db.String(64), comment='')
    quantity = db.Column(db.Integer, comment='')
    unit_price = db.Column(db.Numeric(10, 2), comment='')
    total_price = db.Column(db.Numeric(10, 2), comment='')
    status = db.Column(db.String(20), default='new', comment='')
    urgent = db.Column(db.Boolean, default=False, comment='')
    owner = db.Column(db.String(64), comment='')
    note = db.Column(db.Text, comment='')
    
    # === 数据来源和追溯字===
    source_file = db.Column(db.String(512), comment='')
    raw_data = db.Column(db.Text, comment='')
    horizontal_data = db.Column(db.Text, comment='')
    vertical_data = db.Column(db.Text, comment='')
    data_row_number = db.Column(db.Integer, comment='')
    extraction_method = db.Column(db.String(50), default='enhanced_parser', comment='')
    extraction_info = db.Column(db.Text, comment='')
    
    # === 时间戳字===
    created_by = db.Column(db.String(64), comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')
    imported_at = db.Column(db.DateTime, comment='')
    processed_at = db.Column(db.DateTime, comment='')
    
    def to_dict(self):
        """转换为字典格式"""
        result = {
            'id': self.id,
            
            # 横向信息
            'document_type': self.document_type,
            'document_number': self.document_number,
            'processing_type': self.processing_type,
            'processing_status': self.processing_status,
            'contractor_name': self.contractor_name,
            'contractor_contact': self.contractor_contact,
            'contractor_address': self.contractor_address,
            'contractor_phone': self.contractor_phone,
            'contractor_email': self.contractor_email,
            'client_name': self.client_name,
            
            # 纵向数据
            'order_number': self.order_number,
            'product_name': self.product_name,
            'circuit_name': self.circuit_name,
            'chip_id': self.chip_id,
            'wafer_size': self.wafer_size,
            'package_qty': self.package_qty,
            'package_pieces': self.package_pieces,
            'diffusion_batch': self.diffusion_batch,
            'wafer_number': self.wafer_number,
            'assembly_method': self.assembly_method,
            'drawing_number': self.drawing_number,
            'package_form': self.package_form,
            'delivery_date': self.delivery_date.strftime('%Y-%m-%d') if self.delivery_date else None,
            'item_code': self.item_code,
            'shipping_address': self.shipping_address,
            'wafer_lot': self.wafer_lot,
            'order_attribute': self.order_attribute,
            
            # 分类字段
            'lot_type': self.lot_type,
            'classification': self.classification,
            'sequence_number': self.sequence_number,
            'priority_score': self.priority_score,
            'customer': self.customer,
            'product_code': self.product_code,
            'quantity': self.quantity,
            'unit_price': float(self.unit_price) if self.unit_price else None,
            'total_price': float(self.total_price) if self.total_price else None,
            'status': self.status,
            'urgent': self.urgent,
            'owner': self.owner,
            'note': self.note,
            
            # 数据来源
            'source_file': self.source_file,
            'data_row_number': self.data_row_number,
            'extraction_method': self.extraction_method,
            
            # 时间
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'imported_at': self.imported_at.strftime('%Y-%m-%d %H:%M:%S') if self.imported_at else None,
            'processed_at': self.processed_at.strftime('%Y-%m-%d %H:%M:%S') if self.processed_at else None
        }
        return result
    
    def get_classification_display(self):
        """获取分类的显示名称"""
        classification_map = {
            'engineering': '工程',
            'production': '量产',
            'unknown': '未分'
        }
        return classification_map.get(self.classification, '未分')
    
    def auto_classify_by_lot_type(self):
        """根据Lot Type自动分类"""
        if not self.lot_type:
            return 'unknown'
        
        # 工程类型
        engineering_types = ['试验-E', '新品-E', '工程', '工程', 'DOE-Q', 'qual-Q']
        # 量产类型
        production_types = ['量产-P', '小批-PE', '量产']
        
        if self.lot_type in engineering_types:
            return 'engineering'
        elif self.lot_type in production_types:
            return 'production'
        else:
            return 'unknown'

# ===================== 系统数据库模?(绑定?system.db) =====================

class AISettings(db.Model):
    """AI助手设置值"""
    __tablename__ = 'ai_settings'

    
    id = db.Column(db.Integer, primary_key=True)
    settings = db.Column(db.Text, nullable=False, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    
    def __repr__(self):
        return f'<AISettings {self.id}>'
    
    def to_dict(self):
        import json
        return {
            'id': self.id,
            'settings': json.loads(self.settings) if self.settings else {},
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class ProductPriorityConfig(db.Model):
    """产品优先级配置 - 基于最新Excel结构"""
    __tablename__ = 'product_priority_config'


    id = db.Column(db.Integer, primary_key=True)
    device = db.Column(db.String(100), nullable=True, comment='')
    stage = db.Column(db.String(20), nullable=True, comment='')
    pkg_pn = db.Column(db.String(30), nullable=True, comment='')
    chip_id = db.Column(db.String(50), nullable=True, comment='')
    priority_level = db.Column(db.String(20), default='medium', comment='')
    priority_order = db.Column(db.Integer, default=999, comment='')
    lead_time_days = db.Column(db.Integer, default=7, comment='')
    uph_override = db.Column(db.Integer, nullable=True, comment='')
    notes = db.Column(db.Text, nullable=True, comment='')
    created_by = db.Column(db.String(64), nullable=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_by = db.Column(db.String(64), nullable=True, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')

    def __repr__(self):
        return f'<ProductPriorityConfig {self.device}:{self.stage}:{self.priority_level}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'device': self.device,
            'stage': self.stage,
            'pkg_pn': self.pkg_pn,
            'chip_id': self.chip_id,
            'priority_level': self.priority_level,
            'priority_order': self.priority_order,
            'lead_time_days': self.lead_time_days,
            'uph_override': self.uph_override,
            'notes': self.notes,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_by': self.updated_by,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

# 产品优先级配置模型 - 基于Excel完整结构(小写字段)
class DevicePriorityConfig(db.Model):
    """产品优先级配置"""
    __tablename__ = 'devicepriorityconfig'


    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    device = db.Column(db.String(200), nullable=False, comment='')
    stage = db.Column(db.String(100), nullable=True, comment='')
    handler_config = db.Column(db.String(100), nullable=True, comment='')
    handler_priority = db.Column(db.String(10), nullable=True, comment='')
    setup_qty = db.Column(db.Integer, nullable=True, comment='')
    priority = db.Column(db.String(10), comment='')
    price = db.Column(db.Numeric(10,2), nullable=True, comment='')
    from_time = db.Column(db.DateTime, nullable=True, comment='')
    end_time = db.Column(db.DateTime, nullable=True, comment='')
    refresh_time = db.Column(db.DateTime, nullable=True, comment='')
    user = db.Column(db.String(100), nullable=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')
    
    def __repr__(self):
        return f'<DevicePriorityConfig {self.device}:{self.priority}>'

    def to_dict(self):
        return {
            'id': self.id,
            'device': self.device,
            'stage': self.stage,
            'handler_config': self.handler_config,
            'handler_priority': self.handler_priority,
            'setup_qty': self.setup_qty,
            'priority': self.priority,
            'price': float(self.price) if self.price else None,
            'from_time': self.from_time.strftime('%Y-%m-%d %H:%M:%S') if self.from_time else None,
            'end_time': self.end_time.strftime('%Y-%m-%d %H:%M:%S') if self.end_time else None,
            'refresh_time': self.refresh_time.strftime('%Y-%m-%d %H:%M:%S') if self.refresh_time else None,
            'user': self.user,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

# 批次优先级配置模型 - 基于Excel结构(小写字段，增加lot_id字段)
class LotPriorityConfig(db.Model):
    """批次优先级配置"""
    __tablename__ = 'lotpriorityconfig'


    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    lot_id = db.Column(db.String(100), nullable=False, comment='')
    device = db.Column(db.String(200), nullable=False, comment='')
    stage = db.Column(db.String(100), nullable=True, comment='')
    priority = db.Column(db.String(10), comment='')
    refresh_time = db.Column(db.DateTime, nullable=True, comment='')
    user = db.Column(db.String(100), nullable=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')

    def __repr__(self):
        return f'<LotPriorityConfig {self.lot_id}:{self.device}:{self.priority}>'

    def to_dict(self):
        return {
            'id': self.id,
            'lot_id': self.lot_id,
            'device': self.device,
            'stage': self.stage,
            'priority': self.priority,
            'refresh_time': self.refresh_time.strftime('%Y-%m-%d %H:%M:%S') if self.refresh_time else None,
            'user': self.user,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class UserFilterPresets(db.Model):
    """用户过滤器预设"""
    __tablename__ = 'user_filter_presets'

    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), nullable=False, comment='')
    preset_name = db.Column(db.String(100), nullable=False, comment='')
    page_type = db.Column(db.String(50), nullable=False, comment='')
    filters = db.Column(db.Text, nullable=False, comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')
    
    def __repr__(self):
        return f'<UserFilterPresets {self.username}:{self.preset_name}>'
    
    def to_dict(self):
        import json
        return {
            'id': self.id,
            'username': self.username,
            'preset_name': self.preset_name,
            'page_type': self.page_type,
            'filters': json.loads(self.filters) if self.filters else {},
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class Settings(db.Model):
    """系统全局设置值"""
    __tablename__ = 'settings'

    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False, comment='')
    value = db.Column(db.Text, nullable=True, comment='')
    description = db.Column(db.String(255), nullable=True, comment='')
    data_type = db.Column(db.String(20), default='string', comment='')
    category = db.Column(db.String(50), nullable=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')
    
    def __repr__(self):
        return f'<Settings {self.key}:{self.value}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'data_type': self.data_type,
            'category': self.category,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class SchedulingTasks(db.Model):
    """调度任务配置"""
    __tablename__ = 'scheduling_tasks'

    
    id = db.Column(db.Integer, primary_key=True)
    task_name = db.Column(db.String(100), nullable=False, comment='')
    task_type = db.Column(db.String(50), nullable=False, comment='')
    schedule_config = db.Column(db.Text, nullable=False, comment='')
    enabled = db.Column(db.Boolean, default=True, comment='')
    last_run = db.Column(db.DateTime, nullable=True, comment='')
    next_run = db.Column(db.DateTime, nullable=True, comment='')
    created_by = db.Column(db.String(64), nullable=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')
    
    def __repr__(self):
        return f'<SchedulingTasks {self.task_name}>'
    
    def to_dict(self):
        import json
        return {
            'id': self.id,
            'task_name': self.task_name,
            'task_type': self.task_type,
            'schedule_config': json.loads(self.schedule_config) if self.schedule_config else {},
            'enabled': self.enabled,
            'last_run': self.last_run.strftime('%Y-%m-%d %H:%M:%S') if self.last_run else None,
            'next_run': self.next_run.strftime('%Y-%m-%d %H:%M:%S') if self.next_run else None,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class DatabaseInfo(db.Model):
    """数据库信息"""
    __tablename__ = 'database_info'

    
    id = db.Column(db.Integer, primary_key=True)
    db_name = db.Column(db.String(100), nullable=False, comment='')
    db_type = db.Column(db.String(50), nullable=False, comment='')
    connection_info = db.Column(db.Text, nullable=True, comment='')
    description = db.Column(db.String(255), nullable=True, comment='')
    status = db.Column(db.String(20), default='active', comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')
    
    def __repr__(self):
        return f'<DatabaseInfo {self.db_name}>'
    
    def to_dict(self):
        import json
        return {
            'id': self.id,
            'db_name': self.db_name,
            'db_type': self.db_type,
            'connection_info': json.loads(self.connection_info) if self.connection_info else {},
            'description': self.description,
            'status': self.status,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }


class SchedulingConfig(db.Model):
    """排产算法权重配置"""
    __tablename__ = 'scheduling_config'


    id = db.Column(db.Integer, primary_key=True)
    config_name = db.Column(db.String(100), nullable=False, comment='')
    user_id = db.Column(db.String(50), nullable=True, comment='')
    strategy_name = db.Column(db.String(50), nullable=False, default='intelligent', comment='')

    # 六个维度的权重配置
    tech_match_weight = db.Column(db.Numeric(5, 2), default=25.00, comment='')
    load_balance_weight = db.Column(db.Numeric(5, 2), default=20.00, comment='')
    deadline_weight = db.Column(db.Numeric(5, 2), default=25.00, comment='')
    value_efficiency_weight = db.Column(db.Numeric(5, 2), default=20.00, comment='')
    business_priority_weight = db.Column(db.Numeric(5, 2), default=10.00, comment='')
    temperature_weight = db.Column(db.Numeric(5, 2), default=0.00, comment='温度切换权重')

    # 其他配置参数
    minor_changeover_time = db.Column(db.Integer, default=45, comment='')
    major_changeover_time = db.Column(db.Integer, default=120, comment='')
    urgent_threshold = db.Column(db.Integer, default=8, comment='')
    normal_threshold = db.Column(db.Integer, default=24, comment='')
    critical_threshold = db.Column(db.Integer, default=72, comment='')

    is_active = db.Column(db.Boolean, default=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')

    def __repr__(self):
        return f'<SchedulingConfig {self.config_name}>'

    def to_dict(self):
        return {
            'id': self.id,
            'config_name': self.config_name,
            'user_id': self.user_id,
            'strategy_name': self.strategy_name,
            'tech_match_weight': float(self.tech_match_weight) if self.tech_match_weight else 25.0,
            'load_balance_weight': float(self.load_balance_weight) if self.load_balance_weight else 20.0,
            'deadline_weight': float(self.deadline_weight) if self.deadline_weight else 25.0,
            'value_efficiency_weight': float(self.value_efficiency_weight) if self.value_efficiency_weight else 20.0,
            'business_priority_weight': float(self.business_priority_weight) if self.business_priority_weight else 10.0,
            'temperature_weight': float(self.temperature_weight) if self.temperature_weight else 0.0,
            'minor_changeover_time': self.minor_changeover_time or 45,
            'major_changeover_time': self.major_changeover_time or 120,
            'urgent_threshold': self.urgent_threshold or 8,
            'normal_threshold': self.normal_threshold or 24,
            'critical_threshold': self.critical_threshold or 72,
            'is_active': self.is_active,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_active_config(cls, user_id=None, strategy_name='intelligent'):
        """获取激活的配置"""
        query = cls.query.filter_by(is_active=True, strategy_name=strategy_name)
        if user_id:
            # 优先获取用户专属配置
            user_config = query.filter_by(user_id=user_id).first()
            if user_config:
                return user_config
        # 获取默认配置
        return query.filter_by(user_id=None).first()

    @classmethod
    def get_strategy_weights(cls, strategy_name='intelligent', user_id=None):
        """获取指定策略的权重配置"""
        config = cls.get_active_config(user_id=user_id, strategy_name=strategy_name)
        if config:
            return config.to_dict()
        else:
            # 如果数据库中没有配置,返回策略对应的默认值
            return cls.get_default_weights_for_strategy(strategy_name)

    @classmethod
    def get_default_weights(cls):
        """获取默认权重配置(向后兼容)"""
        return cls.get_strategy_weights('intelligent')

    @classmethod
    def get_default_weights_for_strategy(cls, strategy_name):
        """根据策略名称获取默认权重配置"""
        # 不同策略的默认权重配
        strategy_defaults = {
            'intelligent': {
                'tech_match_weight': 25.0,
                'load_balance_weight': 20.0,
                'deadline_weight': 25.0,
                'value_efficiency_weight': 20.0,
                'business_priority_weight': 10.0,
                'temperature_weight': 0.0,
            },
            'deadline': {
                'tech_match_weight': 15.0,
                'load_balance_weight': 10.0,
                'deadline_weight': 50.0,
                'value_efficiency_weight': 15.0,
                'business_priority_weight': 10.0,
                'temperature_weight': 0.0,
            },
            'product': {
                'tech_match_weight': 40.0,
                'load_balance_weight': 15.0,
                'deadline_weight': 20.0,
                'value_efficiency_weight': 15.0,
                'business_priority_weight': 10.0,
                'temperature_weight': 0.0,
            },
            'value': {
                'tech_match_weight': 15.0,
                'load_balance_weight': 15.0,
                'deadline_weight': 20.0,
                'value_efficiency_weight': 40.0,
                'business_priority_weight': 10.0,
                'temperature_weight': 0.0,
            }
        }

        base_config = {
            'minor_changeover_time': 45,
            'major_changeover_time': 120,
            'urgent_threshold': 8,
            'normal_threshold': 24,
            'critical_threshold': 72
        }

        # 合并策略特定权重和基础配置
        weights = strategy_defaults.get(strategy_name, strategy_defaults['intelligent'])
        weights.update(base_config)
        return weights

    @classmethod
    def get_all_strategies(cls):
        """获取所有可用的排产策略"""
        return [
            {'value': 'intelligent', 'label': '🧠 智能综合策略', 'description': '平衡各项指标的综合策略'},
            {'value': 'deadline', 'label': '📅 交期优先策略', 'description': '优先考虑交期紧迫性'},
            {'value': 'product', 'label': '📦 产品优先策略', 'description': '优先考虑技术匹配度'},
            {'value': 'value', 'label': '💰 产值优先策略', 'description': '优先考虑产值效益'}
        ]


class MigrationLog(db.Model):
    """数据迁移日志"""
    __tablename__ = 'migration_log'

    
    id = db.Column(db.Integer, primary_key=True)
    migration_name = db.Column(db.String(100), nullable=False, comment='')
    migration_type = db.Column(db.String(50), nullable=False, comment='')
    source_db = db.Column(db.String(100), nullable=True, comment='')
    target_db = db.Column(db.String(100), nullable=True, comment='')
    status = db.Column(db.String(20), nullable=False, comment='')
    start_time = db.Column(db.DateTime, nullable=False, comment='')
    end_time = db.Column(db.DateTime, nullable=True, comment='')
    records_processed = db.Column(db.Integer, default=0, comment='')
    error_message = db.Column(db.Text, nullable=True, comment='')
    details = db.Column(db.Text, nullable=True, comment='')
    created_by = db.Column(db.String(64), nullable=True, comment='')
    
    def __repr__(self):
        return f'<MigrationLog {self.migration_name}:{self.status}>'
    
    def to_dict(self):
        import json
        return {
            'id': self.id,
            'migration_name': self.migration_name,
            'migration_type': self.migration_type,
            'source_db': self.source_db,
            'target_db': self.target_db,
            'status': self.status,
            'start_time': self.start_time.strftime('%Y-%m-%d %H:%M:%S') if self.start_time else None,
            'end_time': self.end_time.strftime('%Y-%m-%d %H:%M:%S') if self.end_time else None,
            'records_processed': self.records_processed,
            'error_message': self.error_message,
            'details': json.loads(self.details) if self.details else {},
            'created_by': self.created_by
        }

# ============== APScheduler调度器相关模型 ==============

class SchedulerJob(db.Model):
    """调度器任务配置表 - 存储在MySQL aps_system数据库"""
    __tablename__ = 'scheduler_jobs'

    
    id = db.Column(db.String(255), primary_key=True)
    name = db.Column(db.String(255), nullable=False, comment='')
    job_type = db.Column(db.String(100), nullable=False, comment='')
    func = db.Column(db.String(255), nullable=False, comment='')
    args = db.Column(db.Text, comment='')
    kwargs = db.Column(db.Text, comment='')
    trigger = db.Column(db.String(50), nullable=False, comment='')
    trigger_args = db.Column(db.Text, comment='')
    enabled = db.Column(db.Boolean, default=True, comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')
    created_by = db.Column(db.String(100), comment='')
    description = db.Column(db.Text, comment='')
    
    # 关联的日志记录
    logs = db.relationship('SchedulerJobLog', backref='job', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<SchedulerJob {self.name}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'job_type': self.job_type,
            'func': self.func,
            'args': self.args,
            'kwargs': self.kwargs,
            'trigger': self.trigger,
            'trigger_args': self.trigger_args,
            'enabled': self.enabled,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'description': self.description
        }

class SchedulerJobLog(db.Model):
    """调度器任务执行日志表 - 存储在MySQL aps_system数据库"""
    __tablename__ = 'scheduler_job_logs'

    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    job_id = db.Column(db.String(255), db.ForeignKey('scheduler_jobs.id'), nullable=False, comment='')
    job_name = db.Column(db.String(255), comment='')
    start_time = db.Column(db.DateTime, nullable=False, comment='')
    end_time = db.Column(db.DateTime, comment='')
    status = db.Column(db.String(50), nullable=False, comment='')
    result = db.Column(db.Text, comment='')
    error = db.Column(db.Text, comment='')
    duration = db.Column(db.Numeric(10, 3), comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    
    # 添加索引
    __table_args__ = (
        db.Index('idx_job_id', 'job_id'),
        db.Index('idx_start_time', 'start_time'),
        db.Index('idx_status', 'status'),
    )
    
    def __repr__(self):
        return f'<SchedulerJobLog {self.job_name} - {self.status}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'job_id': self.job_id,
            'job_name': self.job_name,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'result': self.result,
            'error': self.error,
            'duration': float(self.duration) if self.duration else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class SchedulerConfig(db.Model):
    """调度器配置表 - 存储在MySQL aps_system数据库"""
    __tablename__ = 'scheduler_config'

    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    key = db.Column(db.String(255), unique=True, nullable=False, comment='')
    value = db.Column(db.Text, comment='')
    description = db.Column(db.String(500), comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')
    updated_by = db.Column(db.String(100), comment='')
    
    def __repr__(self):
        return f'<SchedulerConfig {self.key}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'updated_by': self.updated_by
        }
    
    @classmethod
    def get_config(cls, key, default=None):
        """获取配置值"""
        config = cls.query.filter_by(key=key).first()
        return config.value if config else default
    
    @classmethod
    def set_config(cls, key, value, description=None, updated_by=None):
        """设置值配置"""
        config = cls.query.filter_by(key=key).first()
        if config:
            config.value = value
            config.updated_by = updated_by
            if description:
                config.description = description
        else:
            config = cls(
                key=key, 
                value=value, 
                description=description,
                updated_by=updated_by
            )
            db.session.add(config)
        
        db.session.commit()
        return config

class LotTypeClassificationRule(db.Model):
    """Lot Type分类规则表"""
    __tablename__ = 'lot_type_classification_rules'
    
    id = db.Column(db.Integer, primary_key=True)
    lot_type = db.Column(db.String(50), nullable=False, unique=True, comment='')
    classification = db.Column(db.Enum('engineering', 'production', 'unknown'), nullable=False, comment='')
    description = db.Column(db.String(200), comment='')
    created_by = db.Column(db.String(64), comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')
    
    def __repr__(self):
        return f'<LotTypeClassificationRule {self.lot_type}:{self.classification}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'lot_type': self.lot_type,
            'classification': self.classification,
            'description': self.description,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_classification(cls, lot_type):
        """根据Lot Type获取分类"""
        if not lot_type:
            return 'unknown'
        
        rule = cls.query.filter_by(lot_type=lot_type).first()
        return rule.classification if rule else 'unknown'
    
    @classmethod
    def bulk_classify(cls, lot_types):
        """批量分类Lot Types"""
        if not lot_types:
            return {}
        
        rules = cls.query.filter(cls.lot_type.in_(lot_types)).all()
        classification_map = {rule.lot_type: rule.classification for rule in rules}
        
        # 为未找到规则的lot_type设置值默认值
        for lot_type in lot_types:
            if lot_type not in classification_map:
                classification_map[lot_type] = 'unknown'
        
        return classification_map

# ===================== 系统数据库模?(绑定?system.db) =====================

# 导入CP订单模型
try:
    from .cp_order_data import CpOrderData
    # 静默模式下不输出详细信息
    if os.environ.get('FLASK_QUIET_STARTUP') != '1':
        import logging
        logging.getLogger(__name__).debug("成功导入CP订单模型")
except ImportError as e:
    # 如果模型文件不存在,定义一个临时的空模型避免错?
    class CpOrderData(db.Model):
        __tablename__ = 'cp_order_data'
        id = db.Column(db.Integer, primary_key=True)
    
    # 静默模式下不输出详细信息
    if os.environ.get('FLASK_QUIET_STARTUP') != '1':
        import logging
        logging.getLogger(__name__).warning(f"无法导入CP订单模型: {e}")

class DifyConfig(db.Model):
    """Dify平台配置"""
    __tablename__ = 'dify_configs'

    
    id = db.Column(db.Integer, primary_key=True)
    config_name = db.Column(db.String(100), nullable=False, unique=True, comment='')
    config_type = db.Column(db.String(50), nullable=False, comment='')
    
    # 基础配置
    enabled = db.Column(db.Boolean, default=False, comment='')
    server_url = db.Column(db.String(255), nullable=False, comment='')
    api_key_encrypted = db.Column(db.Text, comment='')
    app_id = db.Column(db.String(100), comment='')
    
    # 聊天机器人配
    chatbot_url = db.Column(db.String(255), comment='')
    chatbot_token = db.Column(db.String(255), comment='')
    chatbot_color = db.Column(db.String(20), default='#b72424', comment='')
    integration_type = db.Column(db.String(50), default='script', comment='')
    
    # 扩展配置
    extra_config = db.Column(db.Text, comment='')
    description = db.Column(db.String(255), comment='')
    
    # 状态信息
    last_test_at = db.Column(db.DateTime, comment='')
    last_test_status = db.Column(db.String(20), comment='')
    last_test_message = db.Column(db.Text, comment='')
    
    # 创建和更新信
    created_by = db.Column(db.String(64), comment='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='')
    updated_by = db.Column(db.String(64), comment='')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='')
    
    @property
    def encryption_key(self):
        """获取加密密钥"""
        key = os.environ.get('DIFY_ENCRYPTION_KEY')
        if not key:
            # 生成默认密钥(生产环境应该使用环境变量)
            key = base64.urlsafe_b64encode(b'APS-Dify-Config-Key-2025-AI')[:32]
            key = base64.urlsafe_b64encode(key)
        return key.encode() if isinstance(key, str) else key
    
    def set_api_key(self, api_key):
        """加密并设置值API密钥"""
        if api_key:
            from cryptography.fernet import Fernet
            import base64
            fernet = Fernet(self.encryption_key)
            encrypted_key = fernet.encrypt(api_key.encode())
            self.api_key_encrypted = base64.urlsafe_b64encode(encrypted_key).decode()
    
    def get_api_key(self):
        """解密并获取API密钥"""
        if not self.api_key_encrypted:
            return None
        try:
            from cryptography.fernet import Fernet
            import base64
            fernet = Fernet(self.encryption_key)
            encrypted_key = base64.urlsafe_b64decode(self.api_key_encrypted.encode())
            return fernet.decrypt(encrypted_key).decode()
        except Exception:
            return None
    
    @property
    def api_key(self):
        """API密钥属性(只读)"""
        return self.get_api_key()
    
    def test_connection(self):
        """测试Dify连接"""
        try:
            import requests
            
            api_key = self.get_api_key()
            if not api_key:
                return False, "API密钥未设置值"
            
            # 构造测试URL - 智能处理服务器地址
            server_url = self.server_url.rstrip('/')
            
            # 如果服务器地址已经包含/v1,则直接使用
            if server_url.endswith('/v1'):
                base_url = server_url
            else:
                base_url = f"{server_url}/v1"
            
            test_url = f"{base_url}/chat-messages"
            
            # 构造测试请求
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            test_data = {
                'inputs': {},
                'query': '测试连接',
                'response_mode': 'blocking',
                'conversation_id': '',
                'user': 'test_user'
            }
            
            # 发送测试请求
            response = requests.post(
                test_url,
                headers=headers,
                json=test_data,
                timeout=10
            )
            
            # 更新测试状态
            self.last_test_at = datetime.utcnow()
            
            if response.status_code == 200:
                self.last_test_status = 'success'
                self.last_test_message = '连接测试成功'
                return True, '连接测试成功'
            elif response.status_code == 401:
                self.last_test_status = 'failed'
                self.last_test_message = '认证失败: API密钥无效'
                return False, '认证失败: API密钥无效,请检查API密钥是否正确'
            elif response.status_code == 404:
                self.last_test_status = 'failed'
                self.last_test_message = '服务器地址错误: 404 Not Found'
                return False, '服务器地址错误: 找不到API端点,请检查服务器地址是否正确'
            elif response.status_code == 422:
                # 可能是请求参数问题,但说明连接和认证是成功的
                self.last_test_status = 'success'
                self.last_test_message = '连接测试成功 (参数验证)'
                return True, '连接测试成功 (API可达,认证有效)'
            else:
                self.last_test_status = 'failed'
                self.last_test_message = f'连接失败: HTTP {response.status_code}'
                try:
                    error_data = response.json()
                    error_msg = error_data.get('message', 'Unknown error')
                    return False, f'连接失败: {error_msg} (HTTP {response.status_code})'
                except:
                    return False, f'连接失败: HTTP {response.status_code}'
                
        except requests.exceptions.Timeout:
            self.last_test_status = 'failed'
            self.last_test_message = '连接超时'
            return False, '连接超时,请检查服务器地址是否正确'
        except requests.exceptions.ConnectionError:
            self.last_test_status = 'failed'
            self.last_test_message = '连接错误'
            return False, '无法连接到服务器,请检查网络和服务器地址'
        except Exception as e:
            self.last_test_status = 'failed'
            self.last_test_message = str(e)
            return False, f'测试失败: {str(e)}'
    
    def to_dict(self, include_sensitive=False):
        """转换为字典"""
        import json
        data = {
            'id': self.id,
            'config_name': self.config_name,
            'config_type': self.config_type,
            'enabled': self.enabled,
            'server_url': self.server_url,
            'app_id': self.app_id,
            'chatbot_url': self.chatbot_url,
            'chatbot_token': self.chatbot_token,
            'chatbot_color': self.chatbot_color,
            'integration_type': self.integration_type,
            'extra_config': json.loads(self.extra_config) if self.extra_config else {},
            'description': self.description,
            'last_test_at': self.last_test_at.isoformat() if self.last_test_at else None,
            'last_test_status': self.last_test_status,
            'last_test_message': self.last_test_message,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_by': self.updated_by,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_sensitive:
            data['api_key'] = self.get_api_key()
        
        return data
    
    @classmethod
    def get_config_by_type(cls, config_type):
        """根据配置类型获取配置值"""
        return cls.query.filter_by(config_type=config_type).first()
    
    @classmethod
    def get_enabled_configs(cls):
        """获取所有启用的配置"""
        return cls.query.filter_by(enabled=True).all()
    
    @classmethod
    def create_default_configs(cls):
        """创建默认配置"""
        default_configs = [
            {
                'config_name': '新员工培训AI助教',
                'config_type': 'training_ai',
                'enabled': False,
                'server_url': 'http://************',
                'api_key': '',
                'app_id': '',
                'chatbot_url': '',
                'description': '新员工培训AI助教配置'
            },
            {
                'config_name': '产品进度查询AI助手',
                'config_type': 'progress_ai',
                'enabled': False,
                'server_url': 'http://************',
                'api_key': '',
                'app_id': '',
                'chatbot_url': '',
                'description': '产品进度查询AI助手配置'
            },
            {
                'config_name': 'Dify聊天机器人',
                'config_type': 'chatbot',
                'enabled': True,
                'server_url': 'http://************',
                'chatbot_token': 'uV72gGRdNz0eP7ac',
                'chatbot_color': '#b72424',
                'integration_type': 'script',
                'description': 'Dify聊天机器人配置'
            }
        ]
        
        created_configs = []
        for config_data in default_configs:
            existing = cls.query.filter_by(config_type=config_data['config_type']).first()
            if not existing:
                config = cls(**config_data)
                if config_data.get('api_key'):
                    config.set_api_key(config_data['api_key'])
                created_configs.append(config)
        
        return created_configs
    
    def __repr__(self):
        return f'<DifyConfig {self.config_name}({self.config_type})>'
