# 🔒 APS车规芯片终测智能调度平台 - 一键安全部署脚本
# 自动化源代码保护和Docker容器化部署
# 作者: Claude AI Assistant

param(
    [switch]$BuildOnly,        # 仅构建镜像，不启动服务
    [switch]$NoBuild,          # 跳过构建，使用现有镜像
    [switch]$TestMode,         # 测试模式，使用测试配置
    [switch]$Force,            # 强制执行，跳过确认
    [string]$Registry = "",    # 私有镜像仓库地址
    [string]$Version = "1.0.0" # 版本号
)

# 设置编码和错误处理
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$ErrorActionPreference = "Stop"

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    
    $colors = @{
        "Red" = "Red"; "Green" = "Green"; "Yellow" = "Yellow"
        "Blue" = "Blue"; "Magenta" = "Magenta"; "Cyan" = "Cyan"; "White" = "White"
    }
    
    Write-Host $Text -ForegroundColor $colors[$Color]
}

function Test-Prerequisites {
    Write-ColorText "🔍 检查部署前置条件..." "Blue"
    
    # 检查Docker
    try {
        $dockerVersion = docker --version
        Write-ColorText "✅ Docker版本: $dockerVersion" "Green"
    }
    catch {
        Write-ColorText "❌ Docker未安装或未启动" "Red"
        return $false
    }
    
    # 检查Docker Compose
    try {
        $composeVersion = docker-compose --version
        Write-ColorText "✅ Docker Compose版本: $composeVersion" "Green"
    }
    catch {
        Write-ColorText "❌ Docker Compose未安装" "Red"
        return $false
    }
    
    # 检查必要文件
    $requiredFiles = @(
        "requirements_optimized.txt",
        "Dockerfile.secure", 
        "docker-compose.secure.yml"
    )
    
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-ColorText "✅ 发现必要文件: $file" "Green"
        }
        else {
            Write-ColorText "❌ 缺少必要文件: $file" "Red"
            return $false
        }
    }
    
    return $true
}

function Initialize-SecurityConfig {
    Write-ColorText "🔐 初始化安全配置..." "Blue"
    
    # 创建.env文件
    if (-not (Test-Path ".env") -or $Force) {
        Write-ColorText "📝 创建安全环境变量配置..." "Blue"
        
        $envContent = @"
# 🔒 APS安全部署环境变量配置
# 请根据实际情况修改这些值

# 构建信息
BUILD_DATE=$(Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
VERSION=$Version
COMMIT_SHA=$(if (Get-Command git -ErrorAction SilentlyContinue) { git rev-parse HEAD 2>$null } else { "unknown" })

# 数据库配置
MYSQL_ROOT_PASSWORD=$(([System.Web.Security.Membership]::GeneratePassword(16, 4)))
MYSQL_PASSWORD=$(([System.Web.Security.Membership]::GeneratePassword(12, 2)))

# 应用密钥
SECRET_KEY=$(([System.Web.Security.Membership]::GeneratePassword(32, 8)))
JWT_SECRET=$(([System.Web.Security.Membership]::GeneratePassword(32, 8)))

# Redis配置
REDIS_PASSWORD=$(([System.Web.Security.Membership]::GeneratePassword(16, 4)))

# 镜像仓库配置
DOCKER_REGISTRY=$Registry
"@
        
        $envContent | Out-File -FilePath ".env" -Encoding UTF8
        Write-ColorText "✅ 环境变量配置已生成: .env" "Green"
        Write-ColorText "⚠️ 请妥善保管 .env 文件中的密码！" "Yellow"
    }
    
    # 创建secrets目录（如果不存在）
    if (-not (Test-Path "secrets")) {
        New-Item -ItemType Directory -Path "secrets" | Out-Null
        Write-ColorText "✅ 创建secrets目录" "Green"
    }
    
    # 创建.dockerignore文件
    if (-not (Test-Path ".dockerignore") -or $Force) {
        $dockerignoreContent = @"
# 🔒 Docker构建忽略文件 - 防止敏感信息泄露
*.md
*.txt
!requirements_optimized.txt
.git
.gitignore
.env
secrets/
logs/
downloads/
uploads/
instance/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.coverage
.pytest_cache/
node_modules/
.DS_Store
Thumbs.db
*.backup
requirements_backup_*.txt
dependency_audit_report_*.json
"@
        
        $dockerignoreContent | Out-File -FilePath ".dockerignore" -Encoding UTF8
        Write-ColorText "✅ Docker忽略文件已创建: .dockerignore" "Green"
    }
}

function Build-SecureImage {
    Write-ColorText "🔨 构建安全Docker镜像..." "Blue"
    
    # 读取环境变量
    if (Test-Path ".env") {
        Get-Content ".env" | ForEach-Object {
            if ($_ -match "^([^=]+)=(.*)$") {
                [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
            }
        }
    }
    
    $buildArgs = @(
        "--build-arg", "BUILD_DATE=$([Environment]::GetEnvironmentVariable('BUILD_DATE'))",
        "--build-arg", "VERSION=$([Environment]::GetEnvironmentVariable('VERSION'))",
        "--build-arg", "COMMIT_SHA=$([Environment]::GetEnvironmentVariable('COMMIT_SHA'))"
    )
    
    $imageName = if ($Registry) { "$Registry/aps-secure:$Version" } else { "aps-secure:$Version" }
    
    try {
        Write-ColorText "🚀 开始构建镜像: $imageName" "Blue"
        & docker build -f Dockerfile.secure -t $imageName @buildArgs .
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ 安全镜像构建成功: $imageName" "Green"
            
            # 显示镜像信息
            $imageInfo = docker images $imageName --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
            Write-ColorText "📊 镜像信息:" "Blue"
            Write-ColorText $imageInfo "White"
            
            return $true
        }
        else {
            Write-ColorText "❌ 镜像构建失败" "Red"
            return $false
        }
    }
    catch {
        Write-ColorText "❌ 构建过程出错: $_" "Red"
        return $false
    }
}

function Deploy-SecureStack {
    Write-ColorText "🚀 部署安全容器栈..." "Blue"
    
    try {
        # 停止现有服务
        Write-ColorText "🛑 停止现有服务..." "Yellow"
        docker-compose -f docker-compose.secure.yml down 2>$null
        
        # 启动安全服务栈
        Write-ColorText "▶️ 启动安全服务栈..." "Blue"
        docker-compose -f docker-compose.secure.yml up -d
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ 安全容器栈部署成功！" "Green"
            
            # 等待服务启动
            Write-ColorText "⏳ 等待服务启动..." "Yellow"
            Start-Sleep -Seconds 30
            
            # 检查服务状态
            Write-ColorText "📊 服务状态检查:" "Blue"
            docker-compose -f docker-compose.secure.yml ps
            
            # 测试应用访问
            Test-ServiceAccess
            
            return $true
        }
        else {
            Write-ColorText "❌ 服务部署失败" "Red"
            return $false
        }
    }
    catch {
        Write-ColorText "❌ 部署过程出错: $_" "Red"
        return $false
    }
}

function Test-ServiceAccess {
    Write-ColorText "🔧 测试服务访问..." "Blue"
    
    $maxRetries = 10
    $retryCount = 0
    
    while ($retryCount -lt $maxRetries) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:5000" -TimeoutSec 5 -ErrorAction Stop
            Write-ColorText "✅ APS服务访问正常 (HTTP $($response.StatusCode))" "Green"
            Write-ColorText "🌐 应用访问地址: http://localhost:5000" "Cyan"
            return $true
        }
        catch {
            $retryCount++
            Write-ColorText "⏳ 等待服务启动... ($retryCount/$maxRetries)" "Yellow"
            Start-Sleep -Seconds 5
        }
    }
    
    Write-ColorText "⚠️ 服务访问测试超时，请手动检查" "Yellow"
    return $false
}

function Show-SecuritySummary {
    Write-ColorText "`n" + "="*60 "Blue"
    Write-ColorText "🔒 APS安全部署完成摘要" "Blue"
    Write-ColorText "="*60 "Blue"
    
    Write-ColorText "`n🛡️ 安全特性:" "Blue"
    Write-ColorText "   ✅ 源代码编译保护 - 核心算法已编译为字节码" "Green"
    Write-ColorText "   ✅ 多阶段Docker构建 - 源代码不存在于运行镜像" "Green"
    Write-ColorText "   ✅ 非root用户运行 - 容器以apsuser身份运行" "Green"
    Write-ColorText "   ✅ 只读文件系统 - 防止运行时文件篡改" "Green"
    Write-ColorText "   ✅ 网络隔离 - 前后端网络分离" "Green"
    Write-ColorText "   ✅ 最小权限原则 - 移除所有不必要的Linux能力" "Green"
    
    Write-ColorText "`n🎯 核心算法保护:" "Blue"
    Write-ColorText "   🧠 real_scheduling_service.py - 已编译保护" "Cyan"
    Write-ColorText "   🧠 algorithm_selector.py - 已编译保护" "Cyan"
    Write-ColorText "   🧠 multilevel_cache_manager.py - 已编译保护" "Cyan"
    Write-ColorText "   🧠 parallel_scheduling_engine.py - 已编译保护" "Cyan"
    
    Write-ColorText "`n📊 服务信息:" "Blue"
    Write-ColorText "   🌐 Web访问: http://localhost:5000" "White"
    Write-ColorText "   📁 数据持久化: Docker Volumes" "White"
    Write-ColorText "   📝 日志位置: Docker Logs" "White"
    
    Write-ColorText "`n🔧 常用命令:" "Blue"
    Write-ColorText "   查看服务状态: docker-compose -f docker-compose.secure.yml ps" "White"
    Write-ColorText "   查看应用日志: docker-compose -f docker-compose.secure.yml logs -f aps-secure" "White"
    Write-ColorText "   停止服务: docker-compose -f docker-compose.secure.yml down" "White"
    Write-ColorText "   重启服务: docker-compose -f docker-compose.secure.yml restart aps-secure" "White"
    
    Write-ColorText "`n" + "="*60 "Blue"
    Write-ColorText "🎉 您的智能排产算法已得到企业级安全保护！" "Green"
}

function Push-ToRegistry {
    if ($Registry) {
        Write-ColorText "📤 推送镜像到私有仓库..." "Blue"
        
        $imageName = "$Registry/aps-secure:$Version"
        
        try {
            docker push $imageName
            
            if ($LASTEXITCODE -eq 0) {
                Write-ColorText "✅ 镜像推送成功: $imageName" "Green"
            }
            else {
                Write-ColorText "❌ 镜像推送失败" "Red"
            }
        }
        catch {
            Write-ColorText "❌ 推送过程出错: $_" "Red"
        }
    }
}

function Main {
    Write-ColorText "🔒 APS车规芯片终测智能调度平台 - 安全部署工具" "Blue"
    Write-ColorText "作者: Claude AI Assistant" "Blue"
    Write-ColorText "="*60 "Blue"
    
    # 检查前置条件
    if (-not (Test-Prerequisites)) {
        Write-ColorText "❌ 前置条件检查失败，请先安装Docker和Docker Compose" "Red"
        exit 1
    }
    
    # 初始化安全配置
    Initialize-SecurityConfig
    
    # 确认执行
    if (-not $Force -and -not $BuildOnly) {
        Write-ColorText "`n⚠️ 即将执行安全部署，这将:" "Yellow"
        Write-ColorText "   • 编译核心算法为字节码" "White"
        Write-ColorText "   • 构建多阶段安全Docker镜像" "White"
        Write-ColorText "   • 部署容器化服务栈" "White"
        Write-ColorText "   • 配置企业级安全策略" "White"
        
        $confirm = Read-Host "`n确认继续安全部署？(y/N)"
        if ($confirm -ne "y") {
            Write-ColorText "❌ 部署已取消" "Yellow"
            exit 0
        }
    }
    
    $success = $true
    
    # 构建安全镜像
    if (-not $NoBuild) {
        if (-not (Build-SecureImage)) {
            $success = $false
        }
        
        # 推送到私有仓库
        if ($success -and $Registry) {
            Push-ToRegistry
        }
    }
    
    # 部署服务（除非只构建）
    if ($success -and -not $BuildOnly) {
        if (-not (Deploy-SecureStack)) {
            $success = $false
        }
    }
    
    if ($success) {
        if ($BuildOnly) {
            Write-ColorText "✅ 安全镜像构建完成！" "Green"
        }
        else {
            Show-SecuritySummary
        }
    }
    else {
        Write-ColorText "❌ 安全部署失败，请检查错误信息" "Red"
        exit 1
    }
}

# 添加清理函数
function Cleanup-OnExit {
    if ($?) {
        Write-ColorText "`n🎉 安全部署任务完成！" "Green"
    }
    else {
        Write-ColorText "`n💥 部署过程中断，请检查错误信息" "Red"
    }
}

# 注册退出处理
trap { Cleanup-OnExit } EXIT

# 执行主函数
Main 