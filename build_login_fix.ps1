# APS Platform - Login Fix Build Script
# Version: 3.2 - Fix login issues with template and model files

Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "APS Platform - Login Fix Build v3.2" -ForegroundColor Cyan
Write-Host "Fixing login issues - including templates and models" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan

# Check environment
Write-Host "Checking build environment..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Python not found" -ForegroundColor Red
    exit 1
}

# Clean old files
Write-Host "Cleaning old build files..." -ForegroundColor Yellow
if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
if (Test-Path "build") { Remove-Item -Recurse -Force "build" }
Get-ChildItem -Path "." -Filter "*.spec" | Remove-Item -Force

# Verify model architecture works
Write-Host "Verifying refactored model architecture..." -ForegroundColor Cyan
$modelTest = 'import os; os.environ["FLASK_QUIET_STARTUP"] = "1"; from app.models import User, ET_WAIT_LOT; print("Refactored models work!")'
echo $modelTest | python
if ($LASTEXITCODE -ne 0) {
    Write-Host "Model verification failed" -ForegroundColor Red
    exit 1
}

# Verify critical files exist
Write-Host "Verifying critical files..." -ForegroundColor Yellow
$criticalFiles = @(
    "app/models.py",
    "app/templates/auth/login.html",
    "app/templates/base.html"
)

foreach ($file in $criticalFiles) {
    if (Test-Path $file) {
        Write-Host "OK: $file" -ForegroundColor Green
    } else {
        Write-Host "MISSING: $file" -ForegroundColor Red
        exit 1
    }
}

# Execute PyInstaller with complete file collection
Write-Host "Building executable with templates and models..." -ForegroundColor Yellow
Write-Host "Including template files and database models for login support" -ForegroundColor Cyan

python -m PyInstaller run.py `
    --name "APS-Platform-Final" `
    --icon "icon/icon.ico" `
    --clean `
    --noconfirm `
    --onefile `
    --console `
    --hidden-import "pymysql" `
    --hidden-import "sqlalchemy.dialects.mysql" `
    --hidden-import "flask" `
    --hidden-import "flask_sqlalchemy" `
    --hidden-import "flask_login" `
    --hidden-import "app.models" `
    --hidden-import "app.auth" `
    --hidden-import "app.api_v2" `
    --hidden-import "app.services" `
    --collect-submodules "app" `
    --add-data "app/templates;app/templates" `
    --add-data "app/static;app/static" `
    --add-data "app/models.py;app" `
    --add-data "config;config"

# Check result
if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "========================================================" -ForegroundColor Green
    Write-Host "BUILD SUCCESS!" -ForegroundColor Green
    Write-Host "========================================================" -ForegroundColor Green
    
    $exePath = "dist/APS-Platform-Final.exe"
    if (Test-Path $exePath) {
        $size = (Get-Item $exePath).Length
        $sizeMB = [math]::Round($size / 1MB, 1)
        
        Write-Host ""
        Write-Host "Executable: $exePath" -ForegroundColor Yellow
        Write-Host "Size: $sizeMB MB" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Fix Summary:" -ForegroundColor Green
        Write-Host "- Included all template files (app/templates)" -ForegroundColor Green
        Write-Host "- Included static files (app/static)" -ForegroundColor Green
        Write-Host "- Explicitly included models.py file" -ForegroundColor Green
        Write-Host "- Included config files (config)" -ForegroundColor Green
        Write-Host "- Login functionality should now work properly" -ForegroundColor Green
        Write-Host ""
        Write-Host "To distribute:" -ForegroundColor Cyan
        Write-Host "1. Copy APS-Platform-Final.exe to target machine" -ForegroundColor White
        Write-Host "2. Create config/ folder with environment.ini" -ForegroundColor White
        Write-Host "3. Configure database in config/environment.ini" -ForegroundColor White
        Write-Host "4. Run the exe file" -ForegroundColor White
        
    } else {
        Write-Host "ERROR: Executable not found" -ForegroundColor Red
    }
} else {
    Write-Host ""
    Write-Host "Build failed" -ForegroundColor Red
    Write-Host "This should not happen since model verification passed" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "======================================================" -ForegroundColor Cyan
Write-Host "Build completed" -ForegroundColor Cyan
Write-Host "======================================================" -ForegroundColor Cyan 