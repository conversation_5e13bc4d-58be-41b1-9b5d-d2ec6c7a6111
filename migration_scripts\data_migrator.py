#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
渐进式数据库迁移 - 数据迁移执行器
负责执行实际的数据迁移操作
"""

import pymysql
import logging
from datetime import datetime
from migration_scripts.batch_configs import get_database_config

class DataMigrator:
    """数据迁移执行器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_config = get_database_config()
        self.connection_params = self.db_config['connection_params']
    
    def get_connection(self, database_name):
        """获取数据库连接"""
        try:
            conn = pymysql.connect(
                database=database_name,
                **self.connection_params
            )
            return conn
        except Exception as e:
            self.logger.error(f"连接数据库 {database_name} 失败: {e}")
            return None
    
    def migrate_tables(self, tables, special_operation=None):
        """迁移指定的表"""
        try:
            success_count = 0
            total_count = len(tables)
            
            for table in tables:
                self.logger.info(f"📋 开始迁移表: {table}")
                
                if special_operation == 'data_completion':
                    # 特殊操作：数据补全
                    success = self.complete_table_data(table)
                else:
                    # 标准操作：数据迁移
                    success = self.migrate_single_table(table)
                
                if success:
                    success_count += 1
                    self.logger.info(f"✅ 表 {table} 迁移成功")
                else:
                    self.logger.error(f"❌ 表 {table} 迁移失败")
                    return False
            
            self.logger.info(f"📊 迁移统计: {success_count}/{total_count} 表成功")
            return success_count == total_count
            
        except Exception as e:
            self.logger.error(f"表迁移过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def migrate_single_table(self, table_name):
        """迁移单个表"""
        source_conn = None
        target_conn = None
        
        try:
            # 连接源数据库和目标数据库
            source_conn = self.get_connection(self.db_config['source_db'])
            target_conn = self.get_connection(self.db_config['target_db'])
            
            if not source_conn or not target_conn:
                return False
            
            source_cursor = source_conn.cursor(pymysql.cursors.DictCursor)
            target_cursor = target_conn.cursor()
            
            # 检查源表是否存在
            source_cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            if not source_cursor.fetchone():
                self.logger.warning(f"源表 {table_name} 不存在，跳过")
                return True
            
            # 获取表结构
            source_cursor.execute(f"SHOW CREATE TABLE {table_name}")
            create_table_result = source_cursor.fetchone()
            if not create_table_result:
                self.logger.error(f"无法获取表 {table_name} 的结构")
                return False
            
            create_table_sql = create_table_result['Create Table']
            
            # 在目标数据库中创建表（如果不存在）
            target_cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
            target_cursor.execute(create_table_sql)
            target_conn.commit()
            self.logger.info(f"表 {table_name} 结构创建成功")
            
            # 获取源表数据
            source_cursor.execute(f"SELECT * FROM {table_name}")
            data = source_cursor.fetchall()
            
            if not data:
                self.logger.info(f"表 {table_name} 无数据，结构迁移完成")
                return True
            
            # 构建插入SQL
            columns = list(data[0].keys())
            # 用反引号包围字段名以处理保留字
            quoted_columns = [f"`{col}`" for col in columns]
            placeholders = ', '.join(['%s'] * len(columns))
            insert_sql = f"INSERT INTO {table_name} ({', '.join(quoted_columns)}) VALUES ({placeholders})"
            
            # 批量插入数据
            batch_size = 1000
            total_records = len(data)
            inserted_records = 0
            
            for i in range(0, total_records, batch_size):
                batch_data = data[i:i + batch_size]
                batch_values = []
                
                for record in batch_data:
                    values = [record[col] for col in columns]
                    batch_values.append(values)
                
                target_cursor.executemany(insert_sql, batch_values)
                target_conn.commit()
                
                inserted_records += len(batch_values)
                self.logger.info(f"已插入 {inserted_records}/{total_records} 条记录")
            
            self.logger.info(f"表 {table_name} 数据迁移完成，共 {total_records} 条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"迁移表 {table_name} 时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            if source_conn:
                source_conn.close()
            if target_conn:
                target_conn.close()
    
    def complete_table_data(self, table_name):
        """补全表数据（特殊操作）"""
        source_conn = None
        target_conn = None
        
        try:
            self.logger.info(f"🔄 开始补全表 {table_name} 的数据")
            
            source_conn = self.get_connection(self.db_config['source_db'])
            target_conn = self.get_connection(self.db_config['target_db'])
            
            if not source_conn or not target_conn:
                return False
            
            source_cursor = source_conn.cursor(pymysql.cursors.DictCursor)
            target_cursor = target_conn.cursor(pymysql.cursors.DictCursor)
            
            # 检查两个表是否都存在
            source_cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            target_cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            
            if not source_cursor.fetchone():
                self.logger.warning(f"源表 {table_name} 不存在")
                return True
            
            if not target_cursor.fetchone():
                self.logger.error(f"目标表 {table_name} 不存在")
                return False
            
            # 获取源表的所有数据
            source_cursor.execute(f"SELECT * FROM {table_name}")
            source_data = source_cursor.fetchall()
            
            # 获取目标表的所有数据
            target_cursor.execute(f"SELECT * FROM {table_name}")
            target_data = target_cursor.fetchall()
            
            source_count = len(source_data)
            target_count = len(target_data)
            
            self.logger.info(f"源表记录数: {source_count}, 目标表记录数: {target_count}")
            
            if source_count <= target_count:
                self.logger.info(f"目标表数据已完整，无需补全")
                return True
            
            # 找出需要补全的数据
            # 假设表有主键 'id'，根据实际情况调整
            target_ids = set()
            if target_data:
                # 尝试找到主键字段
                primary_key = self.get_primary_key(table_name, target_conn)
                if primary_key:
                    target_ids = {record[primary_key] for record in target_data}
                else:
                    self.logger.warning(f"表 {table_name} 没有找到主键，使用全量替换策略")
                    # 清空目标表并重新插入所有数据
                    target_cursor.execute(f"DELETE FROM {table_name}")
                    target_conn.commit()
                    target_ids = set()
            
            # 找出需要插入的记录
            missing_records = []
            if primary_key and target_ids:
                for record in source_data:
                    if record[primary_key] not in target_ids:
                        missing_records.append(record)
            else:
                missing_records = source_data
            
            if not missing_records:
                self.logger.info(f"表 {table_name} 无缺失数据")
                return True
            
            # 插入缺失的记录
            columns = list(missing_records[0].keys())
            # 用反引号包围字段名以处理保留字
            quoted_columns = [f"`{col}`" for col in columns]
            placeholders = ', '.join(['%s'] * len(columns))
            insert_sql = f"INSERT INTO {table_name} ({', '.join(quoted_columns)}) VALUES ({placeholders})"
            
            for record in missing_records:
                values = [record[col] for col in columns]
                target_cursor.execute(insert_sql, values)
            
            target_conn.commit()
            
            self.logger.info(f"表 {table_name} 数据补全完成，补全 {len(missing_records)} 条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"补全表 {table_name} 数据时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            if source_conn:
                source_conn.close()
            if target_conn:
                target_conn.close()
    
    def get_primary_key(self, table_name, connection):
        """获取表的主键字段名"""
        try:
            cursor = connection.cursor()
            cursor.execute(f"SHOW KEYS FROM {table_name} WHERE Key_name = 'PRIMARY'")
            result = cursor.fetchone()
            if result:
                return result[4]  # Column_name is at index 4
            return None
        except Exception as e:
            self.logger.warning(f"获取表 {table_name} 主键失败: {e}")
            return None
    
    def verify_migration(self, table_name):
        """验证迁移结果"""
        source_conn = None
        target_conn = None
        
        try:
            source_conn = self.get_connection(self.db_config['source_db'])
            target_conn = self.get_connection(self.db_config['target_db'])
            
            if not source_conn or not target_conn:
                return False
            
            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()
            
            # 比较记录数
            source_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            source_count = source_cursor.fetchone()[0]
            
            target_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            target_count = target_cursor.fetchone()[0]
            
            if source_count == target_count:
                self.logger.info(f"表 {table_name} 验证通过: {source_count} 条记录")
                return True
            else:
                self.logger.error(f"表 {table_name} 验证失败: 源({source_count}) != 目标({target_count})")
                return False
                
        except Exception as e:
            self.logger.error(f"验证表 {table_name} 时发生错误: {e}")
            return False
        
        finally:
            if source_conn:
                source_conn.close()
            if target_conn:
                target_conn.close() 