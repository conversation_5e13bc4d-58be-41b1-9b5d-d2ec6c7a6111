#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一批次管理数据库初始化脚本
运行此脚本来创建必要的数据库结构
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.utils.init_unified_management import initialize_unified_management
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def main():
    """主函数"""
    print("🚀 统一批次管理数据库初始化工具")
    print("=" * 50)
    
    try:
        # 创建Flask应用
        app = create_app()
        
        with app.app_context():
            # 初始化数据库结构
            success = initialize_unified_management()
            
            if success:
                print("\n✅ 数据库初始化成功！")
                print("现在可以启用统一批次管理功能了。")
                print("\n📝 下一步操作：")
                print("1. 重启Flask应用")
                print("2. 访问 /production/semi-auto 页面")
                print("3. 点击'统一批次管理'按钮测试功能")
            else:
                print("\n❌ 数据库初始化失败！")
                print("请检查日志信息并联系管理员。")
                return 1
                
    except Exception as e:
        print(f"\n❌ 初始化过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
