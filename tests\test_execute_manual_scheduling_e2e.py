#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
executeManualScheduling() 端到端测试
从API调用开始到最终结果的完整验证
"""

import sys
import os
import json
import time
import requests
from datetime import datetime, timedelta
import mysql.connector
from mysql.connector import Error

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

class ExecuteManualSchedulingE2ETest:
    def __init__(self):
        self.base_url = "http://127.0.0.1:5000"
        self.session = requests.Session()
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'WWWwww123!',
            'database': 'aps',
            'charset': 'utf8mb4'
        }
        self.test_results = []
        
    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 1. 检查Flask应用是否运行
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            if response.status_code != 200:
                raise Exception(f"Flask应用未正常运行，状态码: {response.status_code}")
            print("✅ Flask应用运行正常")
        except Exception as e:
            print(f"❌ Flask应用连接失败: {e}")
            return False
            
        # 2. 登录获取会话
        login_data = {
            'username': 'admin',
            'password': 'admin'
        }
        
        try:
            response = self.session.post(f"{self.base_url}/auth/login", data=login_data)
            if response.status_code != 200:
                raise Exception(f"登录失败，状态码: {response.status_code}")
            print("✅ 登录成功")
        except Exception as e:
            print(f"❌ 登录失败: {e}")
            return False
            
        # 3. 检查数据库连接
        try:
            connection = mysql.connector.connect(**self.db_config)
            if connection.is_connected():
                print("✅ 数据库连接成功")
                connection.close()
        except Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
            
        return True
        
    def check_initial_data(self):
        """检查初始数据状态"""
        print("📊 检查初始数据状态...")
        
        try:
            connection = mysql.connector.connect(**self.db_config)
            cursor = connection.cursor()
            
            # 检查待排产数据
            cursor.execute("SELECT COUNT(*) FROM ET_WAIT_LOT")
            wait_lots_count = cursor.fetchone()[0]
            print(f"📋 待排产批次数量: {wait_lots_count}")
            
            # 检查设备状态数据
            cursor.execute("SELECT COUNT(*) FROM EQP_STATUS")
            equipment_count = cursor.fetchone()[0]
            print(f"⚙️ 设备状态数量: {equipment_count}")
            
            # 检查已排产数据（排产前）
            cursor.execute("SELECT COUNT(*) FROM lotprioritydone")
            initial_scheduled_count = cursor.fetchone()[0]
            print(f"📈 排产前已排产数量: {initial_scheduled_count}")
            
            cursor.close()
            connection.close()
            
            return {
                'wait_lots_count': wait_lots_count,
                'equipment_count': equipment_count,
                'initial_scheduled_count': initial_scheduled_count
            }
            
        except Error as e:
            print(f"❌ 检查初始数据失败: {e}")
            return None
            
    def execute_manual_scheduling_api(self, algorithm='intelligent', optimization_target='balanced'):
        """调用executeManualScheduling API"""
        print(f"🚀 调用executeManualScheduling API - 算法: {algorithm}, 目标: {optimization_target}")
        
        api_url = f"{self.base_url}/api/v2/production/execute-manual-scheduling"
        
        request_data = {
            'algorithm': algorithm,
            'optimization_target': optimization_target,
            'auto_mode': False,
            'time_limit': 30,
            'population_size': 100
        }
        
        try:
            start_time = time.time()
            
            # 发送API请求
            response = self.session.post(
                api_url,
                json=request_data,
                headers={'Content-Type': 'application/json'},
                timeout=60  # 60秒超时
            )
            
            execution_time = time.time() - start_time
            
            print(f"📡 API请求耗时: {execution_time:.2f}秒")
            print(f"📡 API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ API调用成功")
                print(f"📊 返回数据键: {list(result.keys())}")
                
                # 详细分析返回结果
                if result.get('success'):
                    schedule_count = len(result.get('schedule', []))
                    metrics = result.get('metrics', {})
                    
                    print(f"📈 排产成功: {schedule_count} 个批次")
                    print(f"📊 成功率: {metrics.get('success_rate', 'N/A')}")
                    print(f"⏱️ 核心算法耗时: {metrics.get('core_execution_time', 'N/A')}秒")
                    print(f"⏱️ API总耗时: {metrics.get('api_total_time', 'N/A')}秒")
                    
                    return {
                        'success': True,
                        'result': result,
                        'execution_time': execution_time,
                        'schedule_count': schedule_count,
                        'metrics': metrics
                    }
                else:
                    print(f"❌ API返回失败: {result.get('message', 'Unknown error')}")
                    return {
                        'success': False,
                        'error': result.get('message', 'Unknown error'),
                        'execution_time': execution_time
                    }
            else:
                print(f"❌ API调用失败，状态码: {response.status_code}")
                print(f"❌ 错误响应: {response.text}")
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}",
                    'execution_time': execution_time
                }
                
        except Exception as e:
            print(f"❌ API调用异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'execution_time': 0
            }
            
    def verify_database_results(self, expected_count=None):
        """验证数据库中的排产结果"""
        print("🔍 验证数据库中的排产结果...")
        
        try:
            connection = mysql.connector.connect(**self.db_config)
            cursor = connection.cursor()
            
            # 1. 检查lotprioritydone表中的排产结果
            cursor.execute("SELECT COUNT(*) FROM lotprioritydone")
            scheduled_count = cursor.fetchone()[0]
            print(f"📈 数据库中已排产数量: {scheduled_count}")
            
            # 2. 检查最新的排产记录
            cursor.execute("""
                SELECT LOT_ID, HANDLER_CONFIG, GOOD_QTY, PRIORITY, STATUS, CREATE_TIME
                FROM lotprioritydone 
                ORDER BY CREATE_TIME DESC 
                LIMIT 5
            """)
            
            latest_records = cursor.fetchall()
            print(f"📋 最新5条排产记录:")
            for record in latest_records:
                print(f"  LOT_ID: {record[0]}, HANDLER: {record[1]}, QTY: {record[2]}, PRIORITY: {record[3]}, STATUS: {record[4]}, TIME: {record[5]}")
            
            # 3. 检查排产历史记录
            cursor.execute("""
                SELECT history_id, algorithm, optimization_target, status, results_count, duration_seconds
                FROM scheduling_history 
                ORDER BY start_time DESC 
                LIMIT 3
            """)
            
            history_records = cursor.fetchall()
            print(f"📜 最新3条排产历史:")
            for record in history_records:
                print(f"  ID: {record[0]}, 算法: {record[1]}, 目标: {record[2]}, 状态: {record[3]}, 结果数: {record[4]}, 耗时: {record[5]}秒")
            
            # 4. 验证字段完整性 - 检查是否有38个字段的数据
            cursor.execute("DESCRIBE lotprioritydone")
            columns = cursor.fetchall()
            column_names = [col[0] for col in columns]
            print(f"📊 lotprioritydone表字段数量: {len(column_names)}")
            
            # 检查关键的扩展字段是否存在
            extended_fields = [
                'match_type', 'comprehensive_score', 'processing_time', 
                'equipment_utilization', 'priority_score', 'delivery_urgency',
                'setup_cost', 'resource_availability', 'quality_score'
            ]
            
            existing_extended_fields = [field for field in extended_fields if field in column_names]
            print(f"📈 扩展字段存在情况: {len(existing_extended_fields)}/{len(extended_fields)}")
            
            cursor.close()
            connection.close()
            
            return {
                'scheduled_count': scheduled_count,
                'latest_records': latest_records,
                'history_records': history_records,
                'total_fields': len(column_names),
                'extended_fields_count': len(existing_extended_fields),
                'field_names': column_names
            }
            
        except Error as e:
            print(f"❌ 数据库验证失败: {e}")
            return None
            
    def verify_performance_monitoring(self):
        """验证性能监控功能"""
        print("⚡ 验证性能监控功能...")
        
        try:
            # 检查是否有性能监控日志
            from app.utils.scheduling_performance_monitor import get_performance_monitor
            
            monitor = get_performance_monitor()
            
            # 获取最近的性能指标
            recent_metrics = monitor.get_recent_metrics()
            
            if recent_metrics:
                print(f"📊 性能监控数据: {len(recent_metrics)} 条记录")
                for metric in recent_metrics[:3]:  # 显示前3条
                    print(f"  操作: {metric.get('operation', 'N/A')}, 耗时: {metric.get('duration', 'N/A')}秒")
            else:
                print("⚠️ 没有找到性能监控数据")
                
            return True
            
        except Exception as e:
            print(f"❌ 性能监控验证失败: {e}")
            return False
            
    def run_complete_test(self):
        """运行完整的端到端测试"""
        print("🧪 开始executeManualScheduling()端到端测试")
        print("="*60)
        
        # 1. 设置测试环境
        if not self.setup_test_environment():
            print("❌ 测试环境设置失败，测试终止")
            return False
            
        # 2. 检查初始数据
        initial_data = self.check_initial_data()
        if not initial_data:
            print("❌ 初始数据检查失败，测试终止")
            return False
            
        # 3. 执行排产API
        api_result = self.execute_manual_scheduling_api()
        if not api_result['success']:
            print(f"❌ executeManualScheduling API调用失败: {api_result['error']}")
            return False
            
        # 4. 验证数据库结果
        db_result = self.verify_database_results()
        if not db_result:
            print("❌ 数据库结果验证失败")
            return False
            
        # 5. 验证性能监控
        perf_result = self.verify_performance_monitoring()
        
        # 6. 生成测试报告
        print("\n" + "="*60)
        print("📋 executeManualScheduling()端到端测试报告")
        print("="*60)
        
        print(f"🚀 API调用状态: {'✅ 成功' if api_result['success'] else '❌ 失败'}")
        print(f"⏱️ API执行耗时: {api_result['execution_time']:.2f}秒")
        
        if api_result['success']:
            print(f"📊 排产结果数量: {api_result['schedule_count']}")
            print(f"📈 成功率: {api_result['metrics'].get('success_rate', 'N/A')}")
            print(f"⚙️ 算法耗时: {api_result['metrics'].get('core_execution_time', 'N/A')}秒")
        
        if db_result:
            print(f"💾 数据库已排产数量: {db_result['scheduled_count']}")
            print(f"📊 数据库字段数量: {db_result['total_fields']}")
            print(f"🔧 扩展字段数量: {db_result['extended_fields_count']}")
            print(f"📜 排产历史记录: {len(db_result['history_records'])} 条")
            
        print(f"⚡ 性能监控功能: {'✅ 正常' if perf_result else '❌ 异常'}")
        
        # 7. 综合评估
        overall_success = (
            api_result['success'] and 
            db_result is not None and 
            db_result['scheduled_count'] > 0 and
            perf_result
        )
        
        print(f"\n🎯 综合测试结果: {'✅ 通过' if overall_success else '❌ 失败'}")
        
        if overall_success:
            print("🎉 executeManualScheduling()端到端测试完全成功！")
            print("✅ API调用正常")
            print("✅ 数据库保存正常") 
            print("✅ 字段映射正常")
            print("✅ 性能监控正常")
            print("✅ 历史记录正常")
        else:
            print("❌ executeManualScheduling()端到端测试存在问题")
            
        print("="*60)
        
        return overall_success
        
    def run_stress_test(self, iterations=3):
        """运行压力测试"""
        print(f"🔥 开始压力测试 - {iterations} 次连续执行")
        
        success_count = 0
        total_time = 0
        
        for i in range(iterations):
            print(f"\n📊 第 {i+1}/{iterations} 次测试:")
            
            result = self.execute_manual_scheduling_api()
            
            if result['success']:
                success_count += 1
                total_time += result['execution_time']
                print(f"✅ 第 {i+1} 次成功")
            else:
                print(f"❌ 第 {i+1} 次失败: {result['error']}")
                
            # 等待2秒避免过于频繁的请求
            time.sleep(2)
            
        print(f"\n🔥 压力测试结果:")
        print(f"成功率: {success_count}/{iterations} ({success_count/iterations*100:.1f}%)")
        if success_count > 0:
            print(f"平均耗时: {total_time/success_count:.2f}秒")
            
        return success_count == iterations

def main():
    """主函数"""
    test = ExecuteManualSchedulingE2ETest()
    
    # 运行完整测试
    success = test.run_complete_test()
    
    if success:
        print("\n🔥 运行压力测试...")
        stress_success = test.run_stress_test(3)
        
        if stress_success:
            print("\n🎉 所有测试完全通过！executeManualScheduling()功能完美运行！")
        else:
            print("\n⚠️ 基础功能正常，但压力测试存在问题")
    else:
        print("\n❌ 基础功能测试失败，请检查系统状态")
        
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1) 