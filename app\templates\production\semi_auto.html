{% extends "base.html" %}

{% block title %}AEC-FT ICP - 半自动排产数据收集
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}



{% block extra_css %}
<link href="{{ url_for('static', filename='vendor/fullcalendar/core/main.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/fullcalendar/daygrid/main.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/fullcalendar/timegrid/main.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='css/custom/production-tables.css') }}" rel="stylesheet" />

<style>
    .fc-event {
        cursor: pointer;
    }
    .resource-list {
        max-height: calc(100vh - 300px);
        overflow-y: auto;
    }
    .order-list {
        max-height: calc(100vh - 300px);
        overflow-y: auto;
    }
    .batch-upload {
        margin-bottom: 0.5rem;
        padding: 0.75rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    .priority-settings {
        margin-bottom: 0.5rem;
        padding: 0.75rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    /* 进度条容器样式 */
    .progress-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .progress-container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        width: 300px;
        text-align: center;
    }

    .progress {
        height: 20px;
        margin: 15px 0;
        border-radius: 5px;
        overflow: hidden;
        position: relative;
    }

    .progress-bar {
        background-color: #b72424 !important;
        height: 100%;
        position: relative;
    }

    .progress-text {
        margin-top: 10px;
        font-weight: bold;
    }

    .progress-percent {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        line-height: 20px;
        color: white;
        text-align: center;
        font-weight: bold;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
    }

    /* 页面特定样式保留 */
    /* 压缩数据预览区域 */
    .data-preview-section {
        margin-top: 1rem;
        margin-bottom: 1rem;
    }
    
    /* 增大排产结果表格空间 */
    .schedule-result-table {
        max-height: 60vh;
        min-height: 400px;
    }

    /* 修改主要按钮颜色 */
    .btn-primary {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:hover {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:focus {
        background-color: #b72424;
        border-color: #b72424;
        box-shadow: 0 0 0 0.25rem rgba(245, 34, 45, 0.25);
    }

    /* 修改信息按钮颜色 */
    .btn-info {
        background-color: #b72424;
        border-color: #b72424;
        color: white;
    }
    .btn-info:hover {
        background-color: #b54442;
        border-color: #b54442;
        color: white;
    }

    /* 修改徽章颜色 */
    .badge.bg-primary {
        background-color: #b72424 !important;
    }

    /* 🚀 表格搜索和分页样式优化 */
    #tableSearchSection .card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    #tableSearchSection .input-group-text {
        background: #fff;
        border: 1px solid #ced4da;
        color: #6c757d;
    }

    #tableSearchSection .form-control,
    #tableSearchSection .form-select {
        border: 1px solid #ced4da;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    #tableSearchSection .form-control:focus,
    #tableSearchSection .form-select:focus {
        border-color: #b72424;
        box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.25);
    }

    /* 分页控件样式 */
    .pagination .page-link {
        color: #b72424;
        border: 1px solid #dee2e6;
        padding: 0.5rem 0.75rem;
        margin: 0 2px;
        border-radius: 6px;
        transition: all 0.15s ease-in-out;
    }

    .pagination .page-link:hover {
        color: #fff;
        background-color: #b72424;
        border-color: #b72424;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .pagination .page-item.active .page-link {
        background-color: #b72424;
        border-color: #b72424;
        color: #fff;
        box-shadow: 0 2px 4px rgba(183, 36, 36, 0.3);
    }

    .pagination .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #dee2e6;
    }

    /* 表格头部固定样式 */
    .table thead.sticky-top th {
        position: sticky;
        top: 0;
        z-index: 10;
        background: linear-gradient(135deg, #b72424 0%, #d73027 100%);
        color: white;
        border: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* 表格行悬停效果优化 */
    .table tbody tr {
        transition: all 0.2s ease;
    }

    .table tbody tr:hover {
        background-color: rgba(183, 36, 36, 0.08);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
    }

    /* 记录统计样式 */
    .text-muted {
        font-size: 0.875rem;
        font-weight: 500;
    }

    /* 搜索框动画效果 */
    #searchInput {
        transition: all 0.3s ease;
    }

    #searchInput:focus {
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(183, 36, 36, 0.15);
    }

    /* 表格响应式优化 */
    @media (max-width: 768px) {
        #tableSearchSection .col-md-3,
        #tableSearchSection .col-md-2 {
            margin-bottom: 0.5rem;
        }
        
        .pagination .page-link {
            padding: 0.375rem 0.5rem;
            font-size: 0.875rem;
        }
    }

    /* 加载状态样式 */
    .table-loading {
        position: relative;
        opacity: 0.6;
        pointer-events: none;
    }

    .table-loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 32px;
        height: 32px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #b72424;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        transform: translate(-50%, -50%);
        z-index: 20;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* 修改链接颜色 */
    a {
        color: #b72424;
    }
    a:hover {
        color: #b72424;
    }

    /* 修改表格中的选中和悬停状态 */
    .table tbody tr:hover {
        background-color: #fff1f0;
    }
    .table tbody tr.selected {
        background-color: #fff1f0;
    }

    /* 修改分页激活状态颜色 */
    .page-item.active .page-link {
        background-color: #b72424;
        border-color: #b72424;
    }
    .page-link {
        color: #b72424;
    }
    .page-link:hover {
        color: #b72424;
    }

    /* 功能卡片样式 */
    .function-card {
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 10px;
        margin-bottom: 10px;
        background-color: #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }
    
    /* 极度紧凑的管理中心样式 */
    .management-center {
        padding: 0.25rem 0.5rem;
        margin-bottom: 0.25rem;
    }
    
    .management-center h4 {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
        font-weight: 600;
    }
    
    .management-center h6 {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
        font-weight: 600;
    }
    
    /* 控制台整体优化 */
    .console-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border: 1px solid #e9ecef;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    /* 功能块分割样式 */
    .function-block {
        background-color: #ffffff;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 0.5rem;
        margin: 0 0.25rem;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    
    .function-block:first-child {
        margin-left: 0;
    }
    
    .function-block:last-child {
        margin-right: 0;
    }
    
    /* 统一按钮样式 - 与refreshTableData完全一致 */
    .btn-console {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        line-height: 1.5;
        border-radius: 0.375rem;
        white-space: nowrap;
    }
    
    /* 紧凑表单控件 */
    .form-control-console,
    .form-select-console {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
        line-height: 1.5;
        border-radius: 0.375rem;
    }
    
    /* 功能块标签 */
    .block-label {
        font-size: 0.75rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.375rem;
        text-align: center;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 0.25rem;
    }
    
    /* 均匀分布的列 */
    .equal-cols {
        display: flex;
        gap: 0.5rem;
    }
    
    .equal-cols > div {
        flex: 1;
    }
    
    .function-block-content {
        display: flex;
        flex-direction: column;
        gap: 0.25rem; /* 统一内部元素间距 */
    }
    
    /* 页面特定样式（全局样式已统一处理卡片和按钮） */
    .form-label.small {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
        font-weight: 500;
    }
    
    /* 优化手动排产区域 */
    .manual-scheduling-section {
        border-top: 1px solid #e9ecef;
        padding-top: 0.5rem;
        margin-top: 0.5rem;
    }
    
    /* 数据预览区域优化 */
    .data-preview-section .table {
        margin-bottom: 0.5rem;
    }
    
    /* 排产结果统计行优化 */
    .schedule-stats {
        padding: 0.5rem 0;
        margin-bottom: 0.75rem;
        background-color: #f8f9fa;
        border-radius: 0.375rem;
    }
    
    .schedule-stats h5 {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }
    
    .schedule-stats small {
        font-size: 0.75rem;
    }
    
    /* 定时任务区域样式 */
    .scheduled-task-section {
        border-top: 1px solid #e9ecef;
        padding-top: 0.5rem;
        margin-top: 0.5rem;
    }
    
    .task-status-active {
        color: #28a745 !important;
        font-weight: 600;
    }
    
    .task-status-inactive {
        color: #6c757d !important;
    }
    
    /* 定时任务模态框样式 */
    .time-input-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .time-input {
        width: 60px;
        text-align: center;
    }
    
    .task-preview {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin-top: 1rem;
    }

    .function-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .function-card .card-icon {
        font-size: 2rem;
        margin-bottom: 10px;
        color: #b72424;
    }

    .function-card .card-title {
        font-weight: 600;
        margin-bottom: 10px;
    }

    .function-card .card-text {
        color: #666;
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row" style="margin-bottom: 0.5rem;">
        <div class="col">
            <div class="card console-container">
                <div class="card-body management-center">
                    <h6 class="mb-2 text-primary"><i class="fas fa-cogs me-2"></i>数据导入与调度控制台</h6>
                    
                    <!-- 三个功能块均匀分布 -->
                    <div class="equal-cols">
                        <!-- 数据导入功能块 -->
                        <div class="function-block">
                            <div class="block-label">
                                <i class="fas fa-file-import me-1"></i>数据导入
                            </div>
                            <div class="function-block-content">
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-console" id="importPath" 
                                           placeholder="Excel文件路径">
                                    <input type="file" id="folderSelector" webkitdirectory directory style="display: none;">
                                    <button class="btn btn-outline-secondary btn-console" type="button" onclick="savePath()">
                                        <i class="fas fa-save"></i>
                                    </button>
                                </div>
                                <div class="d-flex gap-1">
                                    <button type="button" class="btn btn-primary btn-console" onclick="importFromDirectory()">
                                        <i class="fas fa-file-import"></i> 导入
                                    </button>
                                </div>
                                <div class="d-flex gap-1 flex-wrap">
                                    <button type="button" class="btn btn-info btn-console" onclick="previewSchedule()">
                                        <i class="fas fa-eye"></i> 预览
                                    </button>
                                    <button type="button" class="btn btn-secondary btn-console" onclick="exportSchedule()">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                    <button type="button" class="btn btn-danger btn-console" onclick="deleteSelectedFile()">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 调度策略功能块 -->
                        <div class="function-block">
                            <div class="block-label">
                                <i class="fas fa-cogs me-1"></i>调度策略
                            </div>
                            <div class="function-block-content">
                                <select class="form-select form-select-console" id="manualScheduleStrategy">
                                    <option value="intelligent" selected>智能综合策略</option>
                                    <option value="deadline">交期优先策略</option>
                                    <option value="product">产品优先策略</option>
                                    <option value="value">产值优先策略</option>
                                </select>
                                <select class="form-select form-select-console" id="manualOptimizationTarget">
                                    <option value="balanced" selected>均衡优化</option>
                                    <option value="makespan">最小完工时间</option>
                                    <option value="efficiency">最大效率</option>
                                </select>
                                <div class="d-flex gap-1">
                                    <button type="button" class="btn btn-primary btn-console flex-fill" onclick="executeManualScheduling()">
                                        <i class="fas fa-play"></i> 执行调度
                                    </button>
                                    <button type="button" class="btn btn-info btn-console" onclick="viewScheduleHistory()">
                                        <i class="fas fa-history"></i> 历史
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 定时任务功能块 -->
                        <div class="function-block">
                            <div class="block-label">
                                <i class="fas fa-clock me-1"></i>定时任务
                            </div>
                            <div class="function-block-content">
                                <div class="d-flex gap-1">
                                    <button type="button" class="btn btn-warning btn-console flex-fill" onclick="openScheduledTaskModal()">
                                        <i class="fas fa-clock"></i> 设置定时
                                    </button>
                                </div>
                                <div class="d-flex gap-1">
                                    <button type="button" class="btn btn-outline-warning btn-console flex-fill" onclick="viewScheduledTasks()" id="viewTasksBtn">
                                        <i class="fas fa-list"></i> 查看任务
                                    </button>
                                </div>
                                <div class="text-center">
                                    <small class="text-muted" id="taskStatusText" style="font-size: 0.7rem;">当前无定时任务</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- 🔥 统一数据展示区域 -->
    <div class="data-display-section">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0 small">
                <i class="fas fa-table me-2"></i>
                <span id="dataDisplayTitle">待排产批次数据</span>
                <span class="badge bg-info ms-2" id="dataDisplayCount">0</span>
            </h6>
            <div class="d-flex gap-1">
                <!-- 数据视图切换 -->
                <div class="btn-group" role="group" aria-label="数据视图">
                    <input type="radio" class="btn-check" name="dataView" id="viewWaitLots" value="wait" checked>
                    <label class="btn btn-outline-primary btn-sm" for="viewWaitLots" style="font-size: 0.75rem;">
                        <i class="fas fa-clock me-1"></i>待排产
                    </label>

                    <input type="radio" class="btn-check" name="dataView" id="viewScheduleResult" value="result">
                    <label class="btn btn-outline-success btn-sm" for="viewScheduleResult" style="font-size: 0.75rem;">
                        <i class="fas fa-check me-1"></i>排产结果
                    </label>

                    <input type="radio" class="btn-check" name="dataView" id="viewDoneLots" value="done">
                    <label class="btn btn-outline-info btn-sm" for="viewDoneLots" style="font-size: 0.75rem;">
                        <i class="fas fa-archive me-1"></i>已完成
                    </label>
                </div>

                <button class="btn btn-sm btn-primary" onclick="refreshCurrentData()" style="font-size: 0.8rem;">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>

        <!-- 数据统计信息 -->
        <div class="row text-center mb-2" id="dataStatsRow" style="display: none;">
            <div class="col">
                <small class="text-muted">总数量</small>
                <div class="fw-bold" id="statTotal">0</div>
            </div>
            <div class="col">
                <small class="text-muted">已排产</small>
                <div class="fw-bold text-success" id="statScheduled">0</div>
            </div>
            <div class="col">
                <small class="text-muted">成功率</small>
                <div class="fw-bold text-primary" id="statSuccessRate">0%</div>
            </div>
            <div class="col">
                <small class="text-muted">执行时间</small>
                <div class="fw-bold text-info" id="statExecutionTime">0s</div>
            </div>
        </div>

        <div class="aps-table-responsive" style="max-height: 400px;">
            <table class="aps-table table-sm table-hover" id="unifiedDataTable">
                <thead id="unifiedTableHeader">
                    <!-- 表头将动态生成 -->
                </thead>
                <tbody id="unifiedTableBody">
                    <!-- 表体将动态生成 -->
                </tbody>
            </table>
        </div>
        <!-- 分页导航 -->
        <nav>
            <ul class="pagination pagination-sm justify-content-center mt-2" id="unifiedTablePagination"></ul>
        </nav>
    </div>

    <!-- 🔥 排产结果显示区域 (已整合到统一数据展示区域) -->
    <div class="card mt-4" id="scheduleResultSection" style="display: none; visibility: hidden;">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list-alt me-1"></i>排产调度结果
                    <span class="badge bg-primary ms-2" id="scheduleResultCount">0</span>
                </h6>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-warning" onclick="openManualAdjustment()" title="手动精细调整排产结果的优先级和分选机分配">
                        <i class="fas fa-tools me-1"></i>🔧 手动精调
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="saveAndPublishSchedule()" title="保存排产结果并发布到生产系统">
                        <i class="fas fa-save me-1"></i>保存并发布
                    </button>
                    <button class="btn btn-sm btn-success" onclick="exportScheduleResult()" title="优先从数据库导出已排产数据，如无数据则导出当前结果">
                        <i class="fas fa-file-excel me-1"></i>导出已排产数据
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="viewFailedLots()" title="查看排产失败的批次清单">
                        <i class="fas fa-exclamation-triangle me-1"></i>失败清单
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="clearScheduleResult()">
                        <i class="fas fa-trash-alt me-1"></i>清除结果
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- 排产统计信息 -->
            <div class="row text-center schedule-stats">
                <div class="col">
                    <h5 id="totalBatches">0</h5>
                    <small class="text-muted">总批次数</small>
                </div>
                <div class="col">
                    <h5 id="scheduledBatches" class="text-success">0</h5>
                    <small class="text-muted">已排批次</small>
                </div>
                <div class="col">
                    <h5 id="usedStrategy">智能综合</h5>
                    <small class="text-muted">排产策略</small>
                </div>
                <div class="col">
                    <h5 id="executionTime">0s</h5>
                    <small class="text-muted">执行时间</small>
                </div>
            </div>

            <!-- 表格搜索栏 -->
            <div class="row mb-3" id="tableSearchSection" style="display: none;">
                <div class="col-12">
                    <div class="card border-0 bg-light">
                        <div class="card-body py-2">
                            <div class="row g-2 align-items-center">
                                <div class="col-md-3">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" id="searchInput" placeholder="搜索所有字段...">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select form-select-sm" id="searchField">
                                        <option value="all">全部字段</option>
                                        <option value="HANDLER_ID">分选机</option>
                                        <option value="LOT_ID">工单号</option>
                                        <option value="DEVICE">产品名称</option>
                                        <option value="STAGE">工序阶段</option>
                                        <option value="PKG_PN">封装类型</option>
                                        <option value="PO_ID">订单号</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select form-select-sm" id="pageSize">
                                        <option value="10">每页10条</option>
                                        <option value="20" selected>每页20条</option>
                                        <option value="50">每页50条</option>
                                        <option value="100">每页100条</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="clearSearch()">
                                            <i class="fas fa-times"></i> 清除
                                        </button>
                                        <button class="btn btn-sm btn-outline-info" onclick="exportFilteredData()">
                                            <i class="fas fa-file-excel"></i> 导出筛选
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <small class="text-muted">
                                        共 <span id="totalRecords">0</span> 条，显示 <span id="filteredRecords">0</span> 条
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 排产结果表格 -->
            <div class="aps-table-responsive schedule-result-table">
                <table class="aps-table table-sm table-hover table-striped">
                    <thead class="table-light sticky-top">
                        <tr>
                            <th>优先级</th>
                            <th>分选机</th>
                            <th>内部工单号</th>
                            <th>批次类型</th>
                            <th>数量</th>
                            <th>产品名称</th>
                            <th>封装类型</th>
                            <th>芯片名称</th>
                            <th>工序阶段</th>
                            <th>匹配类型</th>
                            <th>综合评分</th>
                            <th>预计加工时间</th>
                            <th>改机时间</th>
                            <th>算法版本</th>
                            <th>优先级评分</th>
                            <th>订单号</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="scheduleResultTableBody">
                        <!-- JS动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页控件 -->
            <div class="row mt-3" id="paginationSection" style="display: none;">
                <div class="col-12">
                    <nav aria-label="表格分页">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted small">
                                显示第 <span id="pageStart">1</span> - <span id="pageEnd">20</span> 条，共 <span id="pageTotal">0</span> 条记录
            </div>
                                <ul class="pagination pagination-sm mb-0" id="paginationList">
                                    <!-- 分页按钮动态生成 -->
                                </ul>
                            </div>
                        </nav>
                    </div>
                </div>
        </div>
    </div>
    <!-- 🔥 历史记录模态框 -->
    <div class="modal fade" id="scheduleHistoryModal" tabindex="-1" aria-labelledby="scheduleHistoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scheduleHistoryModalLabel">
                        <i class="fas fa-history me-2"></i>排产历史记录
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="ensureModalClose()"></button>
                </div>
                <div class="modal-body">
                    <div class="aps-table-responsive" style="max-height: 600px;">
                        <table class="aps-table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>时间</th>
                                    <th>排产策略</th>
                                    <th>批次数量</th>
                                    <th>执行时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- 历史记录将动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="ensureModalClose()">关闭</button>
                    <button type="button" class="btn btn-danger" onclick="clearAllHistory()">
                        <i class="fas fa-trash me-1"></i>清空历史
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 🔥 定时任务设置模态框 -->
    <div class="modal fade" id="scheduledTaskModal" tabindex="-1" aria-labelledby="scheduledTaskModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scheduledTaskModalLabel">
                        <i class="fas fa-clock me-2"></i>设置定时排产任务
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="scheduledTaskForm">
                        <!-- 任务基本信息 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">任务名称</label>
                                <input type="text" class="form-control" id="taskName" placeholder="例如：每日自动排产" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">任务类型</label>
                                <select class="form-select" id="taskType" onchange="updateTaskTypeOptions()">
                                    <option value="once">一次性任务</option>
                                    <option value="daily">每日重复</option>
                                    <option value="weekly">每周重复</option>
                                    <option value="interval">间隔重复</option>
                                </select>
                            </div>
                        </div>

                        <!-- 一次性任务设置 -->
                        <div class="row mb-3" id="onceTaskOptions">
                            <div class="col-md-6">
                                <label class="form-label">执行日期</label>
                                <input type="date" class="form-control" id="taskDate" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">执行时间</label>
                                <div class="time-input-group">
                                    <input type="number" class="form-control time-input" id="taskHour" min="0" max="23" placeholder="时" value="9" required>
                                    <span>:</span>
                                    <input type="number" class="form-control time-input" id="taskMinute" min="0" max="59" placeholder="分" value="0" required>
                                </div>
                            </div>
                        </div>

                        <!-- 每日重复设置 -->
                        <div class="row mb-3" id="dailyTaskOptions" style="display: none;">
                            <div class="col-md-6">
                                <label class="form-label">每日执行时间</label>
                                <div class="time-input-group">
                                    <input type="number" class="form-control time-input" id="dailyHour" min="0" max="23" placeholder="时" value="9">
                                    <span>:</span>
                                    <input type="number" class="form-control time-input" id="dailyMinute" min="0" max="59" placeholder="分" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">执行说明</label>
                                <div class="form-control-plaintext">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        任务将在每天指定时间自动执行
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- 每周重复设置 -->
                        <div class="row mb-3" id="weeklyTaskOptions" style="display: none;">
                            <div class="col-md-6">
                                <label class="form-label">执行时间</label>
                                <div class="time-input-group">
                                    <input type="number" class="form-control time-input" id="weeklyHour" min="0" max="23" placeholder="时" value="9">
                                    <span>:</span>
                                    <input type="number" class="form-control time-input" id="weeklyMinute" min="0" max="59" placeholder="分" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">选择星期</label>
                                <div class="d-flex gap-2 flex-wrap">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="monday" id="monday">
                                        <label class="form-check-label" for="monday">周一</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="tuesday" id="tuesday">
                                        <label class="form-check-label" for="tuesday">周二</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="wednesday" id="wednesday">
                                        <label class="form-check-label" for="wednesday">周三</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="thursday" id="thursday">
                                        <label class="form-check-label" for="thursday">周四</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="friday" id="friday">
                                        <label class="form-check-label" for="friday">周五</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="saturday" id="saturday">
                                        <label class="form-check-label" for="saturday">周六</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="sunday" id="sunday">
                                        <label class="form-check-label" for="sunday">周日</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 间隔重复设置 -->
                        <div class="row mb-3" id="intervalTaskOptions" style="display: none;">
                            <div class="col-md-6">
                                <label class="form-label">间隔时间</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="intervalValue" min="1" placeholder="间隔数值" value="1">
                                    <select class="form-select" id="intervalUnit">
                                        <option value="minutes">分钟</option>
                                        <option value="hours" selected>小时</option>
                                        <option value="days">天</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">结束时间（可选）</label>
                                <input type="datetime-local" class="form-control" id="intervalEndTime">
                                <small class="text-muted">留空表示无限重复</small>
                            </div>
                        </div>

                        <!-- 排产参数设置 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">排产策略</label>
                                                                    <select class="form-select" id="taskStrategy">
                                        <option value="intelligent">智能综合策略</option>
                                        <option value="deadline">交期优先策略</option>
                                        <option value="product">产品优先策略</option>
                                        <option value="value">产值优先策略</option>
                                    </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">优化目标</label>
                                                                    <select class="form-select" id="taskTarget">
                                        <option value="balanced">均衡优化</option>
                                        <option value="makespan">最小化完工时间</option>
                                        <option value="efficiency">最大化效率</option>
                                    </select>
                            </div>
                        </div>

                        <!-- 高级选项 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoImport" checked>
                                    <label class="form-check-label" for="autoImport">
                                        执行前自动导入最新数据
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="emailNotification">
                                    <label class="form-check-label" for="emailNotification">
                                        完成后发送邮件通知
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 任务预览 -->
                        <div class="task-preview" id="taskPreview">
                            <h6><i class="fas fa-eye me-1"></i>任务预览</h6>
                            <div id="taskPreviewContent">
                                <small class="text-muted">请填写任务信息以预览执行计划</small>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveScheduledTask()">
                        <i class="fas fa-save me-1"></i>保存任务
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 🔥 定时任务列表模态框 -->
    <div class="modal fade" id="scheduledTaskListModal" tabindex="-1" aria-labelledby="scheduledTaskListModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scheduledTaskListModalLabel">
                        <i class="fas fa-list me-2"></i>定时任务管理
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="aps-table-responsive" style="max-height: 600px;">
                        <table class="aps-table table-sm table-hover" style="table-layout: fixed; width: 100%;">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 10%;">任务名称</th>
                                    <th style="width: 10%;">类型</th>
                                    <th style="width: 15%;">执行时间</th>
                                    <th style="width: 10%;">排产策略</th>
                                    <th style="width: 8%;">状态</th>
                                    <th style="width: 15%;">下次执行</th>
                                    <th style="width: 8%;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="scheduledTaskTableBody">
                                <!-- 任务列表将动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-warning" onclick="cleanupOrphanedTasks()" title="清理孤立任务">
                        <i class="fas fa-broom me-1"></i>清理孤立任务
                    </button>
                    <button type="button" class="btn btn-success" onclick="openScheduledTaskModal()">
                        <i class="fas fa-plus me-1"></i>新建任务
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 进度条 HTML -->
    <div class="progress-overlay" id="progressOverlay">
        <div class="progress-container">
            <div id="progressText" class="mb-2">正在导入...</div>
            <div class="progress">
                <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                <div id="progressPercent" class="progress-percent">0%</div>
            </div>
        </div>
    </div>
{% endblock %}
{% block extra_js %}
<script src="{{ url_for('static', filename='vendor/xlsx/xlsx.full.min.js') }}"></script>
<!-- 引入后端定时任务JavaScript -->
<script src="{{ url_for('static', filename='js/backend_scheduled_tasks.js') }}"></script>
<script>
// 检查XLSX库是否正确加载
document.addEventListener('DOMContentLoaded', function() {
    if (typeof XLSX === 'undefined') {
        console.error('XLSX库加载失败');
        alert('Excel导出功能不可用：XLSX库未正确加载');
        return;
    }
    console.log('XLSX库加载成功，版本:', XLSX.version);
});

// 存储导入的数据
let importedData = {
    fileData: {},
    currentFile: null
};

// 从固定目录导入数据
function importFromDirectory() {
    let path = document.getElementById('importPath').value.trim();
    if (!path) {
        alert('请输入Excel文件所在路径');
        return;
    }
    
    // 重置错误计数器
    window.progressErrorCount = 0;
    
    // 显示进度条
    showProgress(true);
    updateProgress(0, '正在准备导入...');
    
    // 规范化路径格式
    path = path.replace(/\\/g, '/');
    
    // 声明进度轮询器变量
    let progressChecker;
    
    // 先清理旧的进度文件
    fetch('/api/production/clear-import-progress', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(() => {
        updateProgress(0, '正在导入Excel文件...');
        
        // 启动进度轮询
        progressChecker = startProgressPolling();
        
        return fetch('/api/production/import-from-directory', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ path })
        });
    })
    .then(response => {
        if (!response.ok) {
            // 尝试解析错误响应
            return response.json().then(errorData => {
                const errorMessage = `HTTP ${response.status}: ${errorData.error || errorData.message || '服务器内部错误'}`;
                throw new Error(errorMessage);
            }).catch(() => {
                throw new Error(`HTTP ${response.status}: 服务器响应错误，无法解析错误信息`);
            });
        }
        return response.json();
    })
    .then(result => {
        if (result.error) {
            throw new Error(result.error);
        }
        
        // 停止进度轮询
        clearInterval(progressChecker);
        
        // 更新进度到100%
        updateProgress(100, '导入完成');
        
        setTimeout(() => {
            showProgress(false);
            
            // 构建更专业的导入结果信息
            let message = '🎉 Excel文件导入完成\n\n';
            message += `📋 导入状态：${result.message}\n\n`;
            
            // 添加详细的表导入统计
            if (result.details) {
                message += `📊 详细信息：${result.details}\n\n`;
            }
            
            // 添加总体统计摘要
            const totalFiles = result.total_files || 0;
            const successFiles = result.processed_files ? result.processed_files.length : 0;
            const failedFiles = result.failed_count || 0;
            const totalRecords = result.total_records || 0;
            const processingTime = result.processing_time || 0;
            
            message += '📈 导入统计摘要：\n';
            message += `  • 总文件数：${totalFiles}\n`;
            message += `  • 成功导入：${successFiles} 个文件\n`;
            message += `  • 导入失败：${failedFiles} 个文件\n`;
            message += `  • 总记录数：${totalRecords} 条\n`;
            message += `  • 处理耗时：${processingTime} 秒\n\n`;
            
            // 添加处理的文件列表和每个文件导入的记录数
            if (result.processed_files && result.processed_files.length > 0) {
                message += "✅ 成功导入的文件详情：\n";
                const processedFiles = [];
                
                result.processed_files.forEach(file => {
                    message += `  📄 ${file.file} → ${file.table}表：${file.records}条记录\n`;
                    processedFiles.push({
                        name: file.file,
                        table: file.table,
                        records: file.records
                    });
                });
                
                message += "\n";
                
                // 更新文件选择器
                updateFileSelector(processedFiles);
            }
            
            // 添加失败文件信息
            if (result.failed_files && result.failed_files.length > 0) {
                message += "❌ 导入失败的文件详情：\n";
                result.failed_files.forEach(file => {
                    message += `  📄 ${file.file}\n`;
                    message += `     错误：${file.error}\n`;
                });
                message += "\n";
                
                // 添加失败文件的解决建议
                message += "💡 失败文件处理建议：\n";
                message += "  • 检查文件格式是否正确\n";
                message += "  • 确认文件未被其他程序占用\n";
                message += "  • 验证文件内容是否符合导入规范\n\n";
            }
            
            // 添加下一步操作建议
            if (successFiles > 0) {
                message += "🎯 下一步操作：\n";
                message += "  • 可以开始进行智能排产\n";
                message += "  • 检查导入的数据是否正确\n";
                message += "  • 设置排产参数和策略\n";
            }
            
            // 刷新表格数据（放在弹窗之前执行）
            loadImportedFiles();
            
            // 强制显示导入结果总结弹窗
            alert(message);
        }, 500);
    })
    .catch(error => {
        // 停止进度轮询
        clearInterval(progressChecker);
        
        showProgress(false);
        console.error('导入错误:', error);
        
        // 构建详细的错误信息
        let errorMessage = '📋 Excel文件导入失败\n\n';
        errorMessage += `❌ 错误详情：${error.message || '未知错误'}\n\n`;
        errorMessage += '🔍 可能的解决方案：\n';
        errorMessage += '1. 检查文件路径是否正确\n';
        errorMessage += '2. 确认对该目录有读取权限\n';
        errorMessage += '3. 验证目录中包含正确格式的Excel文件\n';
        errorMessage += '4. 检查Excel文件是否被其他程序占用\n';
        errorMessage += '5. 确认文件名不包含特殊字符\n';
        errorMessage += '6. 检查网络连接是否正常\n\n';
        errorMessage += '💡 提示：如果问题持续存在，请联系系统管理员或查看服务器日志。';
        
        alert(errorMessage);
    });
}

// 开始轮询进度
function startProgressPolling() {
    // 每500毫秒检查一次进度
    const intervalId = setInterval(() => {
        fetch('/api/production/import-progress')
            .then(response => response.json())
            .then(data => {
                // 重置错误计数器
                window.progressErrorCount = 0;
                
                // 构建当前文件进度信息
                const currentFileProgress = {
                    current_file: data.current_file,
                    files_processed: data.files_processed || 0,
                    total_files: data.total_files || 0
                };
                
                // 更新进度条，包括当前文件进度
                updateProgress(data.percent, data.message, currentFileProgress);
                
                // 如果有错误，显示详细错误消息
                if (data.error) {
                    showProgress(false);
                    
                    let errorMessage = '📋 Excel文件导入过程中出现错误\n\n';
                    errorMessage += `❌ 错误详情：${data.message || '未知错误'}\n\n`;
                    
                    if (data.current_file) {
                        errorMessage += `📄 当前处理文件：${data.current_file}\n`;
                    }
                    
                    if (data.files_processed && data.total_files) {
                        errorMessage += `📊 处理进度：${data.files_processed}/${data.total_files} 个文件\n\n`;
                    }
                    
                    errorMessage += '🔍 可能的解决方案：\n';
                    errorMessage += '  • 检查当前文件格式是否正确\n';
                    errorMessage += '  • 确认文件未被占用\n';
                    errorMessage += '  • 验证文件权限设置\n';
                    errorMessage += '  • 重新尝试导入操作\n';
                    
                    alert(errorMessage);
                    clearInterval(intervalId);
                }
                
                // 如果状态是completed，显示完成信息但继续轮询
                if (data.status === 'completed') {
                    updateProgress(100, '导入已完成');
                }
                // 如果状态是idle，停止轮询
                else if (data.status === 'idle') {
                    clearInterval(intervalId);
                    // 不更新进度显示，保持当前的导入结果显示
                    return;
                }
                // 如果进度是100%，停止轮询
                else if (data.percent >= 100) {
                    clearInterval(intervalId);
                }
            })
            .catch(error => {
                console.error('获取进度失败:', error);
                // 如果连续多次获取进度失败，停止轮询
                if (!window.progressErrorCount) {
                    window.progressErrorCount = 0;
                }
                window.progressErrorCount++;
                
                if (window.progressErrorCount >= 10) {
                    console.warn('进度获取连续失败，停止轮询');
                    clearInterval(intervalId);
                    showProgress(false);
                    
                    let progressErrorMessage = '⚠️ 无法获取导入进度信息\n\n';
                    progressErrorMessage += '📋 当前状态：导入可能仍在后台运行\n\n';
                    progressErrorMessage += '🔄 建议操作：\n';
                    progressErrorMessage += '  • 稍等片刻后刷新页面\n';
                    progressErrorMessage += '  • 检查网络连接状态\n';
                    progressErrorMessage += '  • 查看是否有新数据导入\n';
                    progressErrorMessage += '  • 如果长时间无响应，请联系技术支持\n\n';
                    progressErrorMessage += '💡 提示：即使进度显示失败，文件导入可能已成功完成。';
                    
                    alert(progressErrorMessage);
                }
            });
    }, 500);
    
    return intervalId;
}

// 使用详细信息更新文件列表
function updateFileListWithDetails(files) {
    // 由于不再显示文件列表，此函数保留但不执行任何操作
    console.log('已导入文件:', files);
}

// 保存路径
function savePath() {
    const path = document.getElementById('importPath').value.trim();
    if (!path) {
        alert('请先输入路径');
        return;
    }
    
    fetch('/api/production/save-import-path', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ path })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('路径保存成功');
        } else {
            throw new Error(result.error || '保存失败');
        }
    })
    .catch(error => {
        console.error('保存错误:', error);
        alert('保存失败：' + error.message);
    });
}

// 页面加载时自动加载保存的路径和已导入文件
document.addEventListener('DOMContentLoaded', function() {
    // 🧹 页面加载时清理所有残留通知
    clearAllNotifications();
    console.log('🚀 半自动排产页面已加载，通知系统已初始化');
    
    loadSavedPath();
    loadImportedFiles();
    
    // 添加分页点击事件委托（兼容新的统一分页）
    const paginationElement = document.getElementById('unifiedTablePagination') || document.getElementById('tablePagination');
    if (paginationElement) {
        paginationElement.addEventListener('click', function(e) {
            e.preventDefault();

            // 检查点击的是否是分页链接
            const pageLink = e.target.closest('.page-link');
            if (!pageLink) return;

            // 获取页码和表名
            const page = pageLink.dataset.page;
            const tableName = pageLink.dataset.table;

            // 检查是否是禁用状态的链接
            if (pageLink.parentElement.classList.contains('disabled')) return;

            // 加载对应页的数据
            loadFileData(tableName, parseInt(page));
        });
    }
    
    // 🛡️ 添加全局错误捕获，防止未处理的错误显示
    window.addEventListener('error', function(event) {
        console.error('🚨 页面错误捕获:', event.error);
        // 不显示错误给用户，避免混乱
        event.preventDefault();
    });
    
    // 🛡️ 添加未处理的Promise拒绝捕获
    window.addEventListener('unhandledrejection', function(event) {
        console.error('🚨 未处理的Promise拒绝:', event.reason);
        event.preventDefault();
    });
});

// 加载保存的路径
function loadSavedPath() {
    fetch('/api/production/get-import-path')
        .then(response => response.json())
        .then(result => {
            if (result.success && result.path) {
                document.getElementById('importPath').value = result.path;
            }
        })
        .catch(error => {
            console.error('加载保存的路径失败:', error);
        });
}

// 加载已导入的文件列表
function loadImportedFiles() {
    fetch('/api/production/imported-files')
        .then(response => response.json())
        .then(result => {
            if (result.success && result.files) {
                updateFileSelector(result.files);
            }
        })
        .catch(error => {
            console.error('加载已导入文件失败:', error);
        });
}

// 更新文件选择器（兼容性检查）
function updateFileSelector(files) {
    const fileSelector = document.getElementById('fileSelector');
    if (fileSelector) {
        fileSelector.innerHTML = '<option value="">选择文件</option>' +
            files.map(file =>
                `<option value="${file.name}">${file.name}</option>`
            ).join('');
    } else {
        console.log('📁 文件列表已更新:', files.map(f => f.name));
    }
}

// 文件选择器变化时更新预览表格（兼容性检查）
const fileSelector = document.getElementById('fileSelector');
if (fileSelector) {
    fileSelector.addEventListener('change', function(e) {
        const filename = e.target.value;
        if (!filename) {
            clearPreviewTable();
            return;
        }

        // 加载选中文件的数据
        loadFileData(filename);
    });
}

// 加载文件数据
function loadFileData(filename, page = 1) {
    if (!filename) return;
    
    document.getElementById('previewTableBody').innerHTML = '<tr><td colspan="100%" class="text-center">加载中...</td></tr>';
    
    fetch(`/api/production/file-data/${filename}?page=${page}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // 更新表头
                const header = document.getElementById('previewTableHeader');
                header.innerHTML = `<tr>${result.columns.map(col => `<th>${col}</th>`).join('')}</tr>`;
                
                // 更新表体
                const body = document.getElementById('previewTableBody');
                body.innerHTML = result.data.map(row => `
                    <tr>${result.columns.map(col => `<td>${row[col] || ''}</td>`).join('')}</tr>
                `).join('');
                
                // 更新分页
                updateTablePagination(result.page, result.total_pages, filename);
            } else {
                throw new Error(result.error || '加载数据失败');
            }
        })
        .catch(error => {
            console.error('加载文件数据失败:', error);
            document.getElementById('previewTableBody').innerHTML = 
                '<tr><td colspan="100%" class="text-center text-danger">加载失败</td></tr>';
        });
}

// 更新预览表格
function updatePreviewTable(data, columns) {
    const thead = document.querySelector('#previewTable thead');
    const tbody = document.querySelector('#previewTable tbody');
    
    thead.innerHTML = `<tr>${columns.map(col => `<th>${col}</th>`).join('')}</tr>`;
    tbody.innerHTML = data.map(row => `
        <tr>${columns.map(col => `<td>${row[col] || ''}</td>`).join('')}</tr>
    `).join('');
}

// 清空预览表格
function clearPreviewTable() {
    document.querySelector('#previewTable thead').innerHTML = '';
    document.querySelector('#previewTable tbody').innerHTML = '';
}

// 更新分页（兼容新的统一分页）
function updateTablePagination(currentPage, totalPages, tableName) {
    const pagination = document.getElementById('unifiedTablePagination') || document.getElementById('tablePagination');
    let html = '';
    
    if (totalPages > 1) {
        // 上一页
        html += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage - 1}" data-table="${tableName}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
        
        // 页码
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);
        
        if (startPage > 1) {
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" data-page="1" data-table="${tableName}">1</a>
                </li>
            `;
            if (startPage > 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}" data-table="${tableName}">${i}</a>
                </li>
            `;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" data-page="${totalPages}" data-table="${tableName}">${totalPages}</a>
                </li>
            `;
        }
        
        // 下一页
        html += `
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage + 1}" data-table="${tableName}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
    }
    
    pagination.innerHTML = html;
}

// 刷新表格数据
async function refreshTableData() {
    try {
        // 先记住当前选中的文件（兼容性检查）
        const fileSelector = document.getElementById('fileSelector');
        const currentFile = fileSelector ? fileSelector.value : '';
        
        // 刷新文件列表
        await loadImportedFiles();
        
        // 如果有选中的文件，刷新表格数据
        if (currentFile) {
            loadFileData(currentFile);
        }
    } catch (error) {
        console.error('刷新失败:', error);
        alert('刷新失败，请重试');
    }
}

// 显示进度条
function showProgress(show = true) {
    const overlay = document.getElementById('progressOverlay');
    overlay.style.display = show ? 'flex' : 'none';
}

// 更新进度条
function updateProgress(percent, text, currentFileProgress) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const percentValue = Math.round(percent);
    
    progressBar.style.width = `${percent}%`;
    
    // 更新百分比显示
    progressText.textContent = text;
    
    // 更新进度条中的百分比文本
    let percentText = document.getElementById('progressPercent');
    if (percentText) {
    percentText.textContent = `${percentValue}%`;
    }
    
    // 如果有当前文件进度信息，显示更详细的信息
    if (currentFileProgress && currentFileProgress.current_file) {
        const detailText = `${text} (当前: ${currentFileProgress.current_file})`;
        progressText.textContent = detailText;
    }
}

// 预览数据（兼容性检查）
function previewSchedule() {
    const fileSelector = document.getElementById('fileSelector');
    const selectedFile = fileSelector ? fileSelector.value : '';
    if (!selectedFile) {
        alert('请先选择一个文件');
        return;
    }

    loadFileData(selectedFile);
}

// 导出数据（兼容性检查）
function exportSchedule() {
    const fileSelector = document.getElementById('fileSelector');
    const selectedFile = fileSelector ? fileSelector.value : '';
    if (!selectedFile) {
        alert('请先选择一个文件');
        return;
    }
    
    fetch(`/api/production/export-file/${selectedFile}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.blob();
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = selectedFile;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            alert('导出成功');
        })
        .catch(error => {
            console.error('导出错误:', error);
            alert('导出失败：' + error.message);
        });
}

// 删除选中的文件（兼容性检查）
function deleteSelectedFile() {
    const fileSelector = document.getElementById('fileSelector');
    const selectedFile = fileSelector ? fileSelector.value : '';
    if (!selectedFile) {
        alert('请先选择一个文件');
        return;
    }
    
    if (!confirm(`确定要删除选中的文件 ${selectedFile} 吗？`)) {
        return;
    }
    
    fetch('/api/production/delete-file', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ file: selectedFile })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(`成功删除文件 ${selectedFile}`);
            refreshTableData();
        } else {
            throw new Error(result.error || '删除失败');
        }
    })
    .catch(error => {
        console.error('删除错误:', error);
        alert('删除失败：' + error.message);
    });
}

// ==================== 🔥 手动排产功能实现 ====================

// 全局变量存储排产结果
let currentScheduleResult = null;

// 执行手动排产（清理版本，移除冲突代码）
function executeManualScheduling() {
    const strategy = document.getElementById('manualScheduleStrategy').value;
    const target = document.getElementById('manualOptimizationTarget').value;
    
    // 清除所有可能的旧通知
    clearAllNotifications();
    
    // 显示进度条
    showProgress(true);
    updateProgress(0, '正在初始化排产参数...', null);
    
    const requestData = {
        algorithm: strategy,
        optimization_target: target,
        time_limit: 30,
        population_size: 100,
        auto_mode: false
    };
    
    console.log('🚀 开始手动排产，策略:', strategy, '目标:', target);
    console.log('📤 发送到后端的请求数据:', requestData);
    
    // 进度模拟（更稳定的版本）
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15 + 5; // 5-20%递增
        if (progress > 85) progress = 85; // 不超过85%
        updateProgress(progress, '正在执行排产算法...', null);
    }, 300);
    
    fetch('/api/v2/production/execute-manual-scheduling', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('📡 收到服务器响应，状态:', response.status);
        return response.json();
    })
    .then(result => {
        clearInterval(progressInterval);
        console.log('📊 排产结果:', result);
        
        if (result.success && result.schedule && result.schedule.length > 0) {
            updateProgress(100, '排产完成！', null);
            
            // 🔧 添加策略验证日志
            const usedStrategy = result.metrics?.algorithm || result.metrics?.strategy || 'unknown';
            const strategyName = result.metrics?.strategy_name || 'unknown';
            console.log('✅ 排产成功，使用策略:', usedStrategy, '(', strategyName, ')');
            console.log('📊 排产指标:', result.metrics);
            
            setTimeout(() => {
                showProgress(false);
                
                // 存储排产结果
                currentScheduleResult = result;
                
                // 显示排产结果
                displayScheduleResult(result);
                
                // 保存按钮已移除，将自动保存
                
                // 自动保存排产结果
                saveScheduleResult();
            }, 800);
        } else {
            clearInterval(progressInterval);
            showProgress(false);
            console.error('❌ 排产失败:', result);
            
            // 🔧 改进：显示详细的错误信息和建议
            let errorMessage = result.message || '没有生成有效的排产记录';
            if (result.data_sources) {
                const suggestions = result.data_sources.suggestions || [];
                if (suggestions.length > 0) {
                    errorMessage += '\n\n💡 建议：' + suggestions.join('; ');
                }
            }
            showNotification('❌ 排产失败', errorMessage, 'error');
        }
    })
    .catch(error => {
        clearInterval(progressInterval);
        showProgress(false);
        console.error('💥 排产异常:', error);
        showNotification('💥 排产异常', '网络错误或服务器异常: ' + error.message, 'error');
    });
}

// 全局变量存储表格数据和分页状态
let tableData = [];
let filteredData = [];
let currentPage = 1;
let pageSize = 20;

// 添加完整的字段映射定义
const COMPLETE_FIELD_MAPPING = {
    // 基础显示字段
    'PRIORITY': { label: '优先级', type: 'number', width: '80px' },
    'LOT_ID': { label: '内部工单号', type: 'string', width: '120px' },
    'HANDLER_ID': { label: '分选机', type: 'string', width: '100px' },
    'DEVICE': { label: '产品名称', type: 'string', width: '100px' },
    'GOOD_QTY': { label: '数量', type: 'number', width: '90px' },
    'PKG_PN': { label: '封装类型', type: 'string', width: '100px' },
    'CHIP_ID': { label: '芯片名称', type: 'string', width: '100px' },
    'STAGE': { label: '工序阶段', type: 'string', width: '80px' },
    'LOT_TYPE': { label: '批次类型', type: 'string', width: '90px' },
    'PO_ID': { label: '订单号', type: 'string', width: '100px' },
    'FLOW_ID': { label: '流程ID', type: 'string', width: '100px' },
    'WIP_STATE': { label: '状态', type: 'string', width: '80px' },
    
    // 算法扩展字段（关键修复）
    'match_type': { label: '匹配类型', type: 'string', width: '90px' },
    'comprehensive_score': { label: '综合评分', type: 'decimal', width: '100px', precision: 2 },
    'processing_time': { label: '预计加工时间', type: 'decimal', width: '110px', precision: 2 },
    'changeover_time': { label: '改机时间', type: 'decimal', width: '90px', precision: 2 },
    'algorithm_version': { label: '算法版本', type: 'string', width: '100px' },
    'priority_score': { label: '优先级评分', type: 'decimal', width: '110px', precision: 2 },
    'estimated_hours': { label: '预计时长', type: 'decimal', width: '100px', precision: 2 },
    'equipment_status': { label: '设备状态', type: 'string', width: '100px' },
    'FAMILY': { label: '产品族', type: 'string', width: '100px' }
};

// 定义要显示的字段（选择重要字段避免表格过于复杂）
const DISPLAY_FIELDS = [
    'PRIORITY', 'HANDLER_ID', 'LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 
    'DEVICE', 'PKG_PN', 'CHIP_ID', 'STAGE', 'match_type', 
    'comprehensive_score', 'processing_time', 'changeover_time', 
    'algorithm_version', 'priority_score', 'PO_ID', 'WIP_STATE'
];

// 显示排产结果
function displayScheduleResult(result) {
    // 🔥 使用新的统一数据展示区域
    console.log('📊 显示排产结果，切换到结果视图');

    // 存储原始数据
    tableData = result.schedule || [];
    filteredData = [...tableData];

    // 缓存排产结果数据
    currentDataCache.result = tableData;

    // 切换到排产结果视图
    document.getElementById('viewScheduleResult').checked = true;
    currentDataView = 'result';

    // 更新统计信息
    const stats = {
        total: result.metrics?.total_batches || tableData.length,
        scheduled: tableData.length,
        successRate: result.metrics?.success_rate || '0%',
        executionTime: result.metrics?.execution_time || result.execution_time || '0s'
    };

    updateScheduleStats(stats);

    // 渲染表格
    renderUnifiedTable(tableData, 'result');
    document.getElementById('dataDisplayCount').textContent = tableData.length;

    // 显示数据统计行
    document.getElementById('dataStatsRow').style.display = 'flex';

    // 滚动到数据展示区域
    document.querySelector('.data-display-section').scrollIntoView({ behavior: 'smooth' });

    console.log('✅ 排产结果已显示在统一数据展示区域');
}

// 渲染表格数据
function renderTable() {
    const tbody = document.getElementById('scheduleResultTableBody');
    
    if (!tbody) {
        console.error('表格主体元素未找到');
        return;
    }
    
    // 计算分页数据
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageData = filteredData.slice(startIndex, endIndex);
    
    // 如果没有数据，显示提示信息
    if (pageData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="${DISPLAY_FIELDS.length}" class="text-center text-muted py-4">
                    <i class="fas fa-search me-2"></i>
                    ${filteredData.length === 0 && tableData.length > 0 ? 
                        '未找到匹配的记录，请尝试调整搜索条件' : 
                        '暂无数据'}
                </td>
            </tr>
        `;
        renderPagination();
        updatePaginationInfo();
        return;
    }
    
    let html = '';
    pageData.forEach((item, index) => {
        const getMatchTypeBadge = (matchType) => {
            switch(matchType) {
                case '完全匹配': return '<span class="badge bg-success">完全匹配</span>';
                case 'KIT匹配': return '<span class="badge bg-info">KIT匹配</span>';
                case '小改机': return '<span class="badge bg-warning">小改机</span>';
                case '大改机': return '<span class="badge bg-secondary">大改机</span>';
                case '空闲设备': return '<span class="badge bg-primary">空闲设备</span>';
                default: return `<span class="badge bg-light text-dark">${matchType || 'N/A'}</span>`;
            }
        };
        
        const getScoreColor = (score) => {
            if (score >= 80) return 'text-success fw-bold';
            if (score >= 60) return 'text-primary fw-bold';
            if (score >= 40) return 'text-warning fw-bold';
            return 'text-danger fw-bold';
        };
        
        // 🔧 修复：数据显示优化
        const safeValue = (value, defaultValue = 'N/A') => {
            if (value === null || value === undefined || value === '' || value === 'null') {
                return defaultValue;
            }
            return value;
        };
        
        // 🔧 修复：格式化显示值
        const formatValue = (value, config) => {
            if (value === null || value === undefined || value === '' || value === 'null') {
                return 'N/A';
            }
            
            switch (config.type) {
                case 'number':
                    return Number(value).toLocaleString();
                case 'decimal':
                    return Number(value).toFixed(config.precision || 2);
                case 'string':
                default:
                    return String(value);
            }
        };
        
        // 使用增强的表格渲染逻辑
        html += '<tr>';
        
        DISPLAY_FIELDS.forEach(field => {
            const config = COMPLETE_FIELD_MAPPING[field];
            const rawValue = item[field];
            let displayValue = '';
            
            // 特殊字段处理
            if (field === 'PRIORITY') {
                displayValue = `<span class="badge bg-primary">${rawValue || startIndex + index + 1}</span>`;
            } else if (field === 'HANDLER_ID') {
                displayValue = `<strong>${safeValue(rawValue)}</strong>`;
            } else if (field === 'LOT_TYPE') {
                displayValue = `<span class="badge bg-secondary">${safeValue(rawValue, '未知类型')}</span>`;
            } else if (field === 'GOOD_QTY') {
                displayValue = `<span class="badge bg-light text-dark">${(rawValue || 0).toLocaleString()}</span>`;
            } else if (field === 'PKG_PN') {
                displayValue = safeValue(rawValue, '待补充');
            } else if (field === 'CHIP_ID') {
                displayValue = `<small class="text-muted">${safeValue(rawValue)}</small>`;
            } else if (field === 'STAGE') {
                displayValue = `<span class="badge bg-info">${safeValue(rawValue)}</span>`;
            } else if (field === 'match_type') {
                displayValue = getMatchTypeBadge(safeValue(rawValue, '未评估'));
            } else if (field === 'comprehensive_score') {
                const score = parseFloat(rawValue || 0);
                displayValue = `<span class="${getScoreColor(score)}">${score > 0 ? score.toFixed(1) : '未评分'}</span>`;
            } else if (field === 'processing_time') {
                const time = parseFloat(rawValue || 0);
                displayValue = time > 0 ? time.toFixed(1) + 'h' : '待计算';
            } else if (field === 'changeover_time') {
                const time = parseFloat(rawValue || 0);
                displayValue = time > 0 ? time.toFixed(0) + 'min' : '0min';
            } else if (field === 'algorithm_version') {
                displayValue = `<small class="text-muted">${safeValue(rawValue, 'v1.0')}</small>`;
            } else if (field === 'priority_score') {
                const score = parseFloat(rawValue || 0);
                displayValue = `<span class="${getScoreColor(score)}">${score > 0 ? score.toFixed(1) : '未评分'}</span>`;
            } else if (field === 'WIP_STATE') {
                displayValue = `<span class="badge bg-success">${safeValue(rawValue, 'QUEUE')}</span>`;
            } else {
                // 默认处理
                displayValue = formatValue(rawValue, config);
            }
            
            html += `<td>${displayValue}</td>`;
        });
        
        html += '</tr>';
    });
    
    tbody.innerHTML = html;
    
    // 更新分页控件
    renderPagination();
    
    // 更新分页信息
    updatePaginationInfo();
}

// 绑定表格事件
function bindTableEvents() {
    // 搜索输入事件
    const searchInput = document.getElementById('searchInput');
    const searchField = document.getElementById('searchField');
    const pageSizeSelect = document.getElementById('pageSize');
    
    // 检查元素是否存在
    if (!searchInput || !searchField || !pageSizeSelect) {
        console.warn('搜索控件未找到，跳过事件绑定');
        return;
    }
    
    // 移除之前的事件监听器（避免重复绑定）
    searchInput.removeEventListener('input', handleSearch);
    searchField.removeEventListener('change', performSearch);
    pageSizeSelect.removeEventListener('change', handlePageSizeChange);
    
    // 防抖搜索
    let searchTimeout;
    function handleSearch() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            performSearch();
        }, 300);
    }
    
    function handlePageSizeChange() {
        pageSize = parseInt(this.value);
        currentPage = 1; // 重置到第一页
        renderTable();
    }
    
    // 绑定新的事件监听器
    searchInput.addEventListener('input', handleSearch);
    searchField.addEventListener('change', performSearch);
    pageSizeSelect.addEventListener('change', handlePageSizeChange);
}

// 执行搜索
function performSearch() {
    const searchTerm = document.getElementById('searchInput').value.trim().toLowerCase();
    const searchField = document.getElementById('searchField').value;
    
    if (!searchTerm) {
        filteredData = [...tableData];
    } else {
        filteredData = tableData.filter(item => {
            if (searchField === 'all') {
                // 搜索所有字段
                return Object.values(item).some(value => 
                    String(value || '').toLowerCase().includes(searchTerm)
                );
            } else {
                // 搜索指定字段
                return String(item[searchField] || '').toLowerCase().includes(searchTerm);
            }
        });
    }
    
    // 重置到第一页
    currentPage = 1;
    
    // 更新统计
    updateRecordStats();
    
    // 重新渲染表格
    renderTable();
}

// 更新记录统计
function updateRecordStats() {
    document.getElementById('totalRecords').textContent = tableData.length;
    document.getElementById('filteredRecords').textContent = filteredData.length;
}

// 渲染分页控件
function renderPagination() {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    const paginationList = document.getElementById('paginationList');
    
    if (totalPages <= 1) {
        paginationList.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    // 第一页
    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1); return false;">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }
    
    // 中间页码
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
            </li>
        `;
    }
    
    // 最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages}); return false;">${totalPages}</a></li>`;
    }
    
    // 下一页
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;
    
    paginationList.innerHTML = html;
}

// 更新分页信息
function updatePaginationInfo() {
    const startRecord = (currentPage - 1) * pageSize + 1;
    const endRecord = Math.min(currentPage * pageSize, filteredData.length);
    
    document.getElementById('pageStart').textContent = filteredData.length > 0 ? startRecord : 0;
    document.getElementById('pageEnd').textContent = endRecord;
    document.getElementById('pageTotal').textContent = filteredData.length;
}

// 切换页面
function changePage(page) {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    renderTable();
}

// 清除搜索
function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('searchField').value = 'all';
    performSearch();
}

// 导出筛选后的数据
function exportFilteredData() {
    if (!filteredData || filteredData.length === 0) {
        alert('没有可导出的数据');
        return;
    }
    
    // 准备导出数据
    const exportData = filteredData.map((item, index) => ({
        '序号': index + 1,
        '优先级': item.PRIORITY || '',
        '分选机': item.HANDLER_ID || '',
        '内部工单号': item.LOT_ID || '',
        '批次类型': item.LOT_TYPE || '',
        '数量': item.GOOD_QTY || 0,
        '产品名称': item.DEVICE || '',
        '封装类型': item.PKG_PN || '',
        '芯片名称': item.CHIP_ID || '',
        '工序阶段': item.STAGE || '',
        '匹配类型': item.match_type || '',
        '综合评分': item.comprehensive_score || 0,
        '预计加工时间': item.processing_time || 0,
        '改机时间': item.changeover_time || 0,
        '订单号': item.PO_ID || '',
        '流程ID': item.FLOW_ID || '',
        '状态': item.WIP_STATE || 'QUEUE'
    }));
    
    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(exportData);
    
    // 设置列宽
    const colWidths = [
        {wch: 8}, {wch: 12}, {wch: 15}, {wch: 20}, {wch: 10}, {wch: 12},
        {wch: 15}, {wch: 12}, {wch: 12}, {wch: 12}, {wch: 12}, {wch: 12},
        {wch: 15}, {wch: 12}, {wch: 15}, {wch: 15}, {wch: 10}
    ];
    ws['!cols'] = colWidths;
    
    XLSX.utils.book_append_sheet(wb, ws, '筛选结果');
    
    // 导出文件
    const fileName = `排产结果筛选_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;
    XLSX.writeFile(wb, fileName);
    
    showNotification('导出成功', `已导出 ${filteredData.length} 条筛选记录`, 'success');
}

// 获取优先级徽章样式
function getPriorityBadgeClass(priority) {
    switch(priority) {
        case 'high': return 'bg-danger';
        case 'medium': return 'bg-warning';
        case 'low': return 'bg-secondary';
        default: return 'bg-info';
    }
}

// 保存排产结果
function saveScheduleResult() {
    if (!currentScheduleResult || !currentScheduleResult.schedule) {
        console.log('没有可保存的排产结果');
        return;
    }
    
    // 转换数据格式以匹配现有API - 包含所有ET_WAIT_LOT字段和排产计算结果字段
    const records = currentScheduleResult.schedule.map((item, index) => ({
        ORDER: index + 1,
        // ET_WAIT_LOT 基础字段
        HANDLER_ID: item.HANDLER_ID || '',
        LOT_ID: item.LOT_ID || '',
        LOT_TYPE: item.LOT_TYPE || '',
        GOOD_QTY: item.GOOD_QTY || '',
        PROD_ID: item.PROD_ID || '',
        DEVICE: item.DEVICE || '',
        CHIP_ID: item.CHIP_ID || '',
        PKG_PN: item.PKG_PN || '',
        PO_ID: item.PO_ID || '',
        STAGE: item.STAGE || '',
        WIP_STATE: item.WIP_STATE || '',
        PROC_STATE: item.PROC_STATE || '',
        HOLD_STATE: item.HOLD_STATE || '',
        FLOW_ID: item.FLOW_ID || '',
        FLOW_VER: item.FLOW_VER || '',
        RELEASE_TIME: item.RELEASE_TIME || '',
        FAC_ID: item.FAC_ID || '',
        CREATE_TIME: item.CREATE_TIME || '',
        // 排产计算结果字段
        PRIORITY: item.PRIORITY || index + 1,
        match_type: item.match_type || '',
        comprehensive_score: item.comprehensive_score || 0,
        processing_time: item.processing_time || 0,
        changeover_time: item.changeover_time || 0,
        algorithm_version: item.algorithm_version || '',
        priority_score: item.priority_score || 0,
        estimated_hours: item.estimated_hours || 0,
        equipment_status: item.equipment_status || ''
    }));
    
    // 显示保存状态
    console.log('正在保存排产结果到数据库...');
    
    fetch('/api/production/save-priority-done', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ records: records })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            console.log(`✅ 保存成功：${records.length} 条排产记录已保存到数据库`);
            
            // 保存到历史记录
            saveToHistory(currentScheduleResult);
        } else {
            throw new Error(result.error || '保存失败');
        }
    })
    .catch(error => {
        console.error('保存错误:', error);
        showNotification('保存失败', error.message || '保存失败，请重试', 'error');
    });
}

// 保存到历史记录（优先保存到数据库，localStorage作为轻量级备份）
function saveToHistory(scheduleResult) {
    // 1. 保存轻量级数据到localStorage（避免溢出）
    try {
    const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
    
        // 只保存基本信息，不保存完整数据
    const historyItem = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        strategy: scheduleResult.metrics?.algorithm || 'unknown',
        batchCount: scheduleResult.schedule.length,
        executionTime: scheduleResult.execution_time || 0,
            // 移除data字段，避免存储大量数据
    };
    
    history.unshift(historyItem); // 添加到开头
    
        // 只保留最近20条记录（减少存储）
        if (history.length > 20) {
            history.splice(20);
    }
    
    localStorage.setItem('scheduleHistory', JSON.stringify(history));
        console.log('✅ 轻量级历史记录已保存到localStorage');
        
    } catch (error) {
        console.warn('⚠️ localStorage保存失败，可能是存储配额超限:', error);
        // 清理localStorage，只保留最近5条记录
        try {
            const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
            if (history.length > 5) {
                history.splice(5);
                localStorage.setItem('scheduleHistory', JSON.stringify(history));
                console.log('🧹 已清理localStorage，保留最近5条记录');
            }
        } catch (cleanError) {
            console.error('❌ localStorage清理失败:', cleanError);
            localStorage.removeItem('scheduleHistory');
            console.log('🗑️ 已清空localStorage历史记录');
        }
    }
    
    // 2. 保存到数据库
    const dbHistoryData = {
        schedule_results: scheduleResult.schedule,
        parameters: {
            strategy: scheduleResult.metrics?.algorithm || 'intelligent',
            optimization_target: scheduleResult.optimization_target || 'efficiency',
            time_limit: scheduleResult.time_limit || 300,
            population_size: scheduleResult.population_size || 100
        },
        statistics: {
            total_lots: scheduleResult.schedule.length,
            execution_time: scheduleResult.execution_time || 0
        }
    };
    
    fetch('/api/production/save-schedule-history', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(dbHistoryData)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            console.log('✅ 历史记录已保存到数据库');
        } else {
            console.warn('⚠️ 数据库保存失败，仅保存到localStorage:', result.error);
        }
    })
    .catch(error => {
        console.error('❌ 数据库保存错误:', error);
        console.log('📝 已保存到localStorage作为备份');
    });
}

// 查看历史记录（从数据库获取）
function viewScheduleHistory() {
    const tbody = document.getElementById('historyTableBody');
    tbody.innerHTML = '<tr><td colspan="5" class="text-center"><div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div> 正在加载...</td></tr>';

    // 🚀 修复：调用v2版本的、正确的新历史记录API
    fetch('/api/v2/production/scheduling-history')
        .then(response => {
            if (!response.ok) {
                throw new Error(`网络错误: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            tbody.innerHTML = '';
            if (data.success && data.items && data.items.length > 0) {
                const strategyNames = {
                    'intelligent': '智能综合策略',
                    'deadline': '交期优先策略',
                    'product': '产品优先策略',
                    'value': '产值优先策略'
                };

                data.items.forEach(item => {
                    const row = document.createElement('tr');
                    
                    const statusBadge = item.status === 'COMPLETED' 
                        ? `<span class="badge bg-success">成功</span>`
                        : `<span class="badge bg-danger">失败</span>`;

                    const strategyName = strategyNames[item.algorithm] || item.algorithm;

                    row.innerHTML = `
                        <td>${new Date(item.start_time).toLocaleString()}</td>
                        <td>${strategyName} ${statusBadge}</td>
                        <td>${item.results_count || 0}</td>
                        <td>${(item.duration || 0).toFixed(2)}s</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="loadDatabaseHistoryItem('${item.history_id}')" ${item.status !== 'COMPLETED' ? 'disabled' : ''}>
                                <i class="fas fa-eye"></i> 查看
                            </button>
                            <button class="btn btn-sm btn-success" onclick="exportDatabaseHistoryItem('${item.history_id}')" ${item.status !== 'COMPLETED' ? 'disabled' : ''}>
                                <i class="fas fa-file-export"></i> 导出
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteDatabaseHistoryItem('${item.history_id}')">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } else {
                const message = data.message || '没有可用的历史记录';
                tbody.innerHTML = `<tr><td colspan="5" class="text-center text-muted">${message}</td></tr>`;
            }
        })
        .catch(error => {
            console.error('❌ 获取v2历史记录失败:', error);
            tbody.innerHTML = `<tr><td colspan="5" class="text-center text-danger">获取历史记录失败: ${error.message}</td></tr>`;
        });
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('scheduleHistoryModal'));
    modal.show();
}

// 加载数据库历史记录项
function loadDatabaseHistoryItem(id) {
    fetch(`/api/v2/production/scheduling-history/${id}`)
        .then(response => response.json())
        .then(result => {
            if (result.success && result.data) {
                // 转换数据库格式为前端格式
                const scheduleData = {
                    schedule: result.data.schedule_results || [],
                    metrics: result.data.parameters || {},
                    execution_time: result.data.statistics?.execution_time || 0
                };
                
                currentScheduleResult = scheduleData;
                displayScheduleResult(scheduleData);
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleHistoryModal'));
                modal.hide();
                
                showNotification('历史记录已加载', '可以重新保存或导出此结果', 'info');
            } else {
                showNotification('加载失败', '无法获取历史记录详情', 'error');
            }
        })
        .catch(error => {
            console.error('❌ 加载历史记录失败:', error);
            showNotification('加载失败', '网络错误，请稍后重试', 'error');
        });
}

// 导出数据库历史记录项
function exportDatabaseHistoryItem(id) {
    fetch(`/api/v2/production/scheduling-history/${id}`)
        .then(response => response.json())
        .then(result => {
            if (result.success && result.data && result.data.schedule_results) {
                const scheduleData = result.data.schedule_results;
                
                if (scheduleData.length === 0) {
                    showNotification('导出失败', '该历史记录没有排产数据', 'warning');
                    return;
                }
                
                // 准备导出数据
                const exportData = scheduleData.map((record, index) => ({
                    'ORDER': index + 1,
                    'HANDLER_ID': record.HANDLER_ID || '',
                    'LOT_ID': record.LOT_ID || '',
                    'LOT_TYPE': record.LOT_TYPE || '',
                    'GOOD_QTY': record.GOOD_QTY || '',
                    'PROD_ID': record.PROD_ID || '',
                    'DEVICE': record.DEVICE || '',
                    'CHIP_ID': record.CHIP_ID || '',
                    'PKG_PN': record.PKG_PN || '',
                    'PO_ID': record.PO_ID || '',
                    'STAGE': record.STAGE || '',
                    'WIP_STATE': record.WIP_STATE || '',
                    'PROC_STATE': record.PROC_STATE || '',
                    'HOLD_STATE': record.HOLD_STATE || '',
                    'FLOW_ID': record.FLOW_ID || '',
                    'FLOW_VER': record.FLOW_VER || '',
                    'RELEASE_TIME': record.RELEASE_TIME || '',
                    'FAC_ID': record.FAC_ID || '',
                    'CREATE_TIME': record.CREATE_TIME || ''
                }));
                
                // 使用XLSX库导出
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(exportData);
                
                // 设置列宽
                const colWidths = [
                    { wch: 8 }, { wch: 12 }, { wch: 15 }, { wch: 12 }, { wch: 10 },
                    { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 12 },
                    { wch: 10 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 },
                    { wch: 10 }, { wch: 18 }, { wch: 8 }, { wch: 18 }
                ];
                ws['!cols'] = colWidths;
                
                XLSX.utils.book_append_sheet(wb, ws, "历史排产结果");
                
                // 生成文件名
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                const filename = `历史排产结果_${id}_${timestamp}.xlsx`;
                
                XLSX.writeFile(wb, filename);
                showNotification('导出成功', `文件已保存为: ${filename}`, 'success');
                
            } else {
                showNotification('导出失败', '无法获取历史记录数据', 'error');
            }
        })
        .catch(error => {
            console.error('❌ 导出历史记录失败:', error);
            showNotification('导出失败', '网络错误，请稍后重试', 'error');
        });
}

// 删除数据库历史记录项
function deleteDatabaseHistoryItem(id) {
    if (!confirm('确定要删除这条历史记录吗？此操作不可恢复！')) {
        return;
    }
    
    fetch(`/api/v2/production/scheduling-history/${id}`, {
        method: 'DELETE'
    })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showNotification('删除成功', '历史记录已删除', 'success');
                // 刷新历史记录列表
                viewScheduleHistory();
            } else {
                showNotification('删除失败', result.error || '未知错误', 'error');
            }
        })
        .catch(error => {
            console.error('❌ 删除历史记录失败:', error);
            showNotification('删除失败', '网络错误，请稍后重试', 'error');
        });
}

// 保留原有函数作为备用（处理localStorage历史记录）
function loadHistoryItem(id) {
    const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
    const item = history.find(h => h.id === id);
    
    if (item && item.data) {
        currentScheduleResult = item.data;
        displayScheduleResult(item.data);
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleHistoryModal'));
        modal.hide();
        
        showNotification('历史记录已加载', '可以重新保存或导出此结果', 'info');
    } else {
        showNotification('加载失败', '本地历史记录不包含详细数据', 'warning');
    }
}

// 直接导出历史记录项（无需加载到当前结果）
function exportHistoryItem(id) {
    console.log('🔍 直接导出历史记录 ID:', id);
    
    const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
    const item = history.find(h => h.id === id);
    
    if (!item) {
        console.warn('⚠️ 未找到指定的历史记录');
        alert('未找到指定的历史记录！');
        return;
    }
    
    if (!item.data || !item.data.schedule) {
        console.warn('⚠️ 历史记录数据异常');
        alert('历史记录数据异常，无法导出！');
        return;
    }
    
    if (item.data.schedule.length === 0) {
        console.warn('⚠️ 历史记录为空');
        alert('该历史记录没有排产数据，无法导出！');
        return;
    }
    
    console.log(`✅ 准备导出历史记录: ${item.data.schedule.length} 条记录`);
    
    try {
        // 准备导出数据
        const exportData = item.data.schedule.map((record, index) => ({
            'ORDER': index + 1,
            'HANDLER_ID': record.HANDLER_ID || '',
            'LOT_ID': record.LOT_ID || '',
            'LOT_TYPE': record.LOT_TYPE || '',
            'GOOD_QTY': record.GOOD_QTY || '',
            'PROD_ID': record.PROD_ID || '',
            'DEVICE': record.DEVICE || '',
            'CHIP_ID': record.CHIP_ID || '',
            'PKG_PN': record.PKG_PN || '',
            'PO_ID': record.PO_ID || '',
            'STAGE': record.STAGE || '',
            'WIP_STATE': record.WIP_STATE || '',
            'PROC_STATE': record.PROC_STATE || '',
            'HOLD_STATE': record.HOLD_STATE || '',
            'FLOW_ID': record.FLOW_ID || '',
            'FLOW_VER': record.FLOW_VER || '',
            'RELEASE_TIME': record.RELEASE_TIME || '',
            'FAC_ID': record.FAC_ID || '',
            'CREATE_TIME': record.CREATE_TIME || ''
        }));
        
        // 使用XLSX库导出
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(exportData);
        
        // 设置列宽
        const colWidths = [
            { wch: 8 },  // ORDER
            { wch: 12 }, // HANDLER_ID
            { wch: 15 }, // LOT_ID
            { wch: 12 }, // LOT_TYPE
            { wch: 10 }, // GOOD_QTY
            { wch: 12 }, // PROD_ID
            { wch: 15 }, // DEVICE
            { wch: 15 }, // CHIP_ID
            { wch: 15 }, // PKG_PN
            { wch: 12 }, // PO_ID
            { wch: 10 }, // STAGE
            { wch: 12 }, // WIP_STATE
            { wch: 12 }, // PROC_STATE
            { wch: 12 }, // HOLD_STATE
            { wch: 12 }, // FLOW_ID
            { wch: 10 }, // FLOW_VER
            { wch: 18 }, // RELEASE_TIME
            { wch: 8 },  // FAC_ID
            { wch: 18 }  // CREATE_TIME
        ];
        ws['!cols'] = colWidths;
        
        XLSX.utils.book_append_sheet(wb, ws, "历史排产结果");
        
        // 生成文件名（包含历史记录的时间戳）
        const historyDate = new Date(item.timestamp);
        const dateStr = historyDate.toISOString().slice(0, 19).replace(/[:-]/g, '');
        const filename = `历史排产结果_${dateStr}.xlsx`;
        
        XLSX.writeFile(wb, filename);
        console.log(`✅ 历史记录导出成功: ${filename}`);
        showNotification('导出成功', `历史记录已导出为: ${filename}`, 'success');
        
    } catch (error) {
        console.error('💥 历史记录导出失败:', error);
        alert(`历史记录导出失败！\n\n错误详情: ${error.message}`);
    }
}

// 删除历史记录项
function deleteHistoryItem(id) {
    if (!confirm('确定要删除这条历史记录吗？')) {
        return;
    }
    
    try {
        const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
        const newHistory = history.filter(h => h.id !== id);
        localStorage.setItem('scheduleHistory', JSON.stringify(newHistory));
        
        // 更新表格内容，保持模态框打开状态
        updateHistoryTableDisplay(newHistory);
        
        showNotification('历史记录已删除', '', 'info');
        
    } catch (error) {
        console.error('删除历史记录失败:', error);
        showNotification('删除失败', '操作过程中出现错误', 'error');
    }
}

// 更新历史记录表格显示
function updateHistoryTableDisplay(history) {
    const tbody = document.getElementById('historyTableBody');
    
    if (!tbody) {
        console.error('找不到历史记录表格元素');
        return;
    }
    
    if (history.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无历史记录</td></tr>';
    } else {
        const strategyNames = {
            'deadline': '交期优先',
            'product': '产品优先', 
            'value': '产值优先',
            'intelligent': '智能综合'
        };
        
        let html = '';
        history.forEach(item => {
            const date = new Date(item.timestamp);
            html += `
                <tr>
                    <td>${date.toLocaleString()}</td>
                    <td>${strategyNames[item.strategy] || item.strategy}</td>
                    <td>${item.batchCount}</td>
                    <td>${item.executionTime.toFixed(2)}s</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="loadHistoryItem(${item.id})">
                            <i class="fas fa-eye me-1"></i>查看
                        </button>
                        <button class="btn btn-sm btn-outline-success ms-1" onclick="exportHistoryItem(${item.id})">
                            <i class="fas fa-file-excel me-1"></i>导出
                        </button>
                        <button class="btn btn-sm btn-outline-danger ms-1" onclick="deleteHistoryItem(${item.id})">
                            <i class="fas fa-trash me-1"></i>删除
                        </button>
                    </td>
                </tr>
            `;
        });
        tbody.innerHTML = html;
    }
}

// 确保模态框正确关闭
function ensureModalClose() {
    try {
        const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleHistoryModal'));
        if (modal) {
            modal.hide();
        }
        
        // 移除所有可能的模态框背景
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
        
        // 恢复body的overflow样式
        document.body.style.overflow = '';
        document.body.classList.remove('modal-open');
        
    } catch (error) {
        console.error('关闭模态框时出错:', error);
    }
}

// 清空所有历史记录
function clearAllHistory() {
    if (!confirm('确定要清空所有历史记录吗？此操作不可恢复！')) {
        return;
    }
    
    // 调用后端API清空数据库
    fetch('/api/v2/production/scheduling-history/clear-all', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            // 同时清理本地缓存（以防万一）
            localStorage.removeItem('scheduleHistory');
            
            // 更新表格显示为空
            updateHistoryTableDisplay([]);
            
            showNotification('历史记录已清空', '所有历史记录已从数据库中删除', 'success');
        } else {
            throw new Error(result.error || '清空失败');
        }
    })
    .catch(error => {
        console.error('清空历史记录失败:', error);
        showNotification('清空失败', '操作过程中出现错误: ' + error.message, 'error');
    });
}

// 导出排产结果 - 修复为从数据库获取真实数据
function exportScheduleResult() {
    console.log('🔍 开始导出排产结果...');
    
    // 显示加载状态
    const exportBtn = document.querySelector('button[onclick="exportScheduleResult()"]');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>导出中...';
    exportBtn.disabled = true;
    
    // 从数据库获取真实的已排产数据
    fetch('/api/v2/production/done-lots?size=10000', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (!result.success || !result.data || result.data.length === 0) {
            // 检查是否有当前排产结果可作为备选
            if (currentScheduleResult && currentScheduleResult.schedule && currentScheduleResult.schedule.length > 0) {
                console.log('⚠️ 数据库无数据，但有当前排产结果，提供选择');
                if (confirm('数据库中暂无已排产数据。\n\n是否导出当前页面显示的排产结果？\n\n点击"确定"导出当前结果，点击"取消"先执行排产操作。')) {
                    return exportCurrentScheduleResult();
                } else {
                    throw new Error('请先执行排产操作，将结果保存到数据库后再导出');
                }
            } else {
                throw new Error('没有找到已排产数据，请先执行排产操作');
            }
        }
        
        const scheduleData = result.data;
        console.log(`✅ 从数据库获取 ${scheduleData.length} 条已排产记录`);
        
        // 按照数据库真实字段结构准备导出数据
        const exportData = scheduleData.map((item, index) => ({
            '优先级': item.PRIORITY || index + 1,
            '分拣机ID': item.HANDLER_ID || '',
            '批次号': item.LOT_ID || '',
            '批次类型': item.LOT_TYPE || '',
            '良品数量': item.GOOD_QTY || 0,
            '产品ID': item.PROD_ID || '',
            '器件名称': item.DEVICE || '',
            '芯片ID': item.CHIP_ID || '',
            '封装': item.PKG_PN || '',
            '订单号': item.PO_ID || '',
            '工序': item.STAGE || '',
            'WIP状态': item.WIP_STATE || '',
            '流程状态': item.PROC_STATE || '',
            '扣留状态': item.HOLD_STATE || '',
            '流程ID': item.FLOW_ID || '',
            '流程版本': item.FLOW_VER || '',
            '释放时间': item.RELEASE_TIME || '',
            '工厂ID': item.FAC_ID || '',
            '创建时间': item.CREATE_TIME || '',
            '综合评分': item.comprehensive_score || '',
            '预计加工时间(h)': item.processing_time || '',
            '改机时间(min)': item.changeover_time || '',
            '算法版本': item.algorithm_version || '',
            '匹配类型': item.match_type || '',
            '优先级评分': item.priority_score || ''
        }));
        
        // 使用XLSX库导出
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(exportData);
        
        // 设置列宽
        const colWidths = [
            { wch: 8 },  // 优先级
            { wch: 12 }, // 分拣机ID
            { wch: 18 }, // 批次号
            { wch: 12 }, // 批次类型
            { wch: 12 }, // 良品数量
            { wch: 15 }, // 产品ID
            { wch: 15 }, // 器件名称
            { wch: 15 }, // 芯片ID
            { wch: 15 }, // 封装
            { wch: 15 }, // 订单号
            { wch: 10 }, // 工序
            { wch: 12 }, // WIP状态
            { wch: 12 }, // 流程状态
            { wch: 12 }, // 扣留状态
            { wch: 12 }, // 流程ID
            { wch: 12 }, // 流程版本
            { wch: 20 }, // 释放时间
            { wch: 10 }, // 工厂ID
            { wch: 20 }, // 创建时间
            { wch: 12 }, // 综合评分
            { wch: 15 }, // 预计加工时间
            { wch: 12 }, // 改机时间
            { wch: 15 }, // 算法版本
            { wch: 12 }, // 匹配类型
            { wch: 12 }  // 优先级评分
        ];
        ws['!cols'] = colWidths;
        
        XLSX.utils.book_append_sheet(wb, ws, "已排产批次数据");
        
        // 生成文件名
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        const filename = `已排产批次数据_${timestamp}.xlsx`;
        
        XLSX.writeFile(wb, filename);
        console.log(`✅ 已排产数据导出成功: ${filename}`);
        showNotification('导出成功', `文件已保存为: ${filename}，包含 ${scheduleData.length} 条记录`, 'success');
        
    })
    .catch(error => {
        console.error('💥 导出过程中出现错误:', error);
        showNotification('导出失败', error.message || '获取数据失败，请稍后重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
    });
}

// 导出当前排产结果的辅助函数
function exportCurrentScheduleResult() {
    console.log('📋 导出当前页面排产结果...');
    
    if (!currentScheduleResult || !currentScheduleResult.schedule) {
        throw new Error('当前排产结果数据异常');
    }
    
    const scheduleData = currentScheduleResult.schedule;
    
    // 按照页面展示字段准备导出数据
    const exportData = scheduleData.map((item, index) => ({
        '优先级': item.PRIORITY || index + 1,
        '分拣机ID': item.HANDLER_ID || '',
        '批次号': item.LOT_ID || '',
        '批次类型': item.LOT_TYPE || '',
        '良品数量': item.GOOD_QTY || 0,
        '产品ID': item.PROD_ID || '',
        '器件名称': item.DEVICE || '',
        '芯片ID': item.CHIP_ID || '',
        '封装': item.PKG_PN || '',
        '订单号': item.PO_ID || '',
        '工序': item.STAGE || '',
        'WIP状态': item.WIP_STATE || '',
        '流程状态': item.PROC_STATE || '',
        '扣留状态': item.HOLD_STATE || '',
        '流程ID': item.FLOW_ID || '',
        '流程版本': item.FLOW_VER || '',
        '释放时间': item.RELEASE_TIME || '',
        '工厂ID': item.FAC_ID || '',
        '创建时间': item.CREATE_TIME || '',
        '匹配类型': item.match_type || '',
        '综合评分': item.comprehensive_score || '',
        '预计加工时间': item.processing_time || '',
        '改机时间': item.changeover_time || ''
    }));
    
    // 使用XLSX库导出
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(exportData);
    
    // 设置列宽
    const colWidths = [
        { wch: 8 },  // 优先级
        { wch: 12 }, // 分拣机ID
        { wch: 18 }, // 批次号
        { wch: 12 }, // 批次类型
        { wch: 12 }, // 良品数量
        { wch: 15 }, // 产品ID
        { wch: 15 }, // 器件名称
        { wch: 15 }, // 芯片ID
        { wch: 15 }, // 封装
        { wch: 15 }, // 订单号
        { wch: 10 }, // 工序
        { wch: 12 }, // WIP状态
        { wch: 12 }, // 流程状态
        { wch: 12 }, // 扣留状态
        { wch: 12 }, // 流程ID
        { wch: 12 }, // 流程版本
        { wch: 20 }, // 释放时间
        { wch: 10 }, // 工厂ID
        { wch: 20 }, // 创建时间
        { wch: 12 }, // 匹配类型
        { wch: 12 }, // 综合评分
        { wch: 15 }, // 预计加工时间
        { wch: 12 }  // 改机时间
    ];
    ws['!cols'] = colWidths;
    
    XLSX.utils.book_append_sheet(wb, ws, "当前排产结果");
    
    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const filename = `当前排产结果_${timestamp}.xlsx`;
    
        XLSX.writeFile(wb, filename);
    console.log(`✅ 当前排产结果导出成功: ${filename}`);
    showNotification('导出成功', `文件已保存为: ${filename}，包含 ${scheduleData.length} 条当前排产记录`, 'success');
        
    return Promise.resolve(); // 返回Promise以保持函数链的一致性
}

// 清除排产结果
function clearScheduleResult() {
    if (!confirm('确定要清除当前排产结果吗？')) {
        return;
    }
    
    currentScheduleResult = null;
    document.getElementById('scheduleResultSection').style.display = 'none';
    document.getElementById('scheduleResultTableBody').innerHTML = '';
    
    // 隐藏搜索和分页控件
    document.getElementById('tableSearchSection').style.display = 'none';
    document.getElementById('paginationSection').style.display = 'none';
    
    // 清除数据
    tableData = [];
    filteredData = [];
    currentPage = 1;
    
    // 重置搜索框
    if (document.getElementById('searchInput')) {
        document.getElementById('searchInput').value = '';
    }
    if (document.getElementById('searchField')) {
        document.getElementById('searchField').value = 'all';
    }
    
    showNotification('结果已清除', '排产结果和搜索状态已重置', 'info');
}

// 清除所有通知（防止冲突）
function clearAllNotifications() {
    // 移除所有position-fixed的alert通知
    const existingNotifications = document.querySelectorAll('.alert.position-fixed');
    existingNotifications.forEach(notification => {
        notification.remove();
    });
    
    // 移除可能的其他通知容器
    const otherNotifications = document.querySelectorAll('[class*="notification"], [class*="toast"], [class*="message"]');
    otherNotifications.forEach(notification => {
        if (notification.style.position === 'fixed' || notification.style.position === 'absolute') {
            notification.remove();
        }
    });
}

// 注释：已删除createJumpNotification和createSaveJumpNotification函数
// 这些函数之前用于页面跳转通知，现在已不需要

// 注释：已删除showScheduleCompletionOptions函数，排产完成后自动保存，无需用户选择

// 查看排产结果
function viewScheduleResults() {
    const section = document.getElementById('scheduleResultSection');
    section.scrollIntoView({ behavior: 'smooth' });
}

// 注释：已删除quickSaveAndView函数，现在统一使用saveScheduleResult函数

// 打开已排产界面 - 修改为不自动跳转
function openDoneLots() {
    showNotification('💡 提示', '请通过左侧菜单"排产管理 > 批次管理 > 已排产批次管理"查看排产结果', 'info');
}

// 关闭通知
function dismissNotification(button) {
    const notification = button.closest('.alert');
    if (notification) {
        notification.remove();
    }
}

// 显示通知消息（改进版本）
function showNotification(title, message, type = 'info') {
    // 先清除可能存在的同类型通知
    const existingSameType = document.querySelectorAll(`.alert.alert-${type === 'error' ? 'danger' : type}.position-fixed`);
    existingSameType.forEach(notification => notification.remove());
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 300px;
        max-width: 500px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    notification.innerHTML = `
        <strong>${title}</strong>
        ${message ? `<br><small>${message}</small>` : ''}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 添加唯一标识
    notification.setAttribute('data-notification-id', Date.now());
    document.body.appendChild(notification);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (notification && notification.parentNode) {
            notification.remove();
        }
    }, 3000);
    
    console.log(`📢 通知: ${title} - ${message}`);
}

// ==================== ✅ 定时任务功能已由后端JavaScript接管 ====================
console.log('✅ 定时任务功能已替换为后端服务，localStorage操作已迁移到MySQL数据库');

// 定时任务相关函数已由后端JavaScript文件提供

// ==================== 🔥 统一数据展示管理 ====================
let currentDataView = 'wait'; // 当前数据视图：wait, result, done
let currentDataCache = {}; // 数据缓存

// 初始化统一数据展示
document.addEventListener('DOMContentLoaded', function() {
    // 绑定数据视图切换事件
    document.querySelectorAll('input[name="dataView"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                currentDataView = this.value;
                switchDataView(this.value);
            }
        });
    });

    // 初始加载待排产数据
    loadWaitLotsData();
});

// 切换数据视图
async function switchDataView(viewType) {
    const titleElement = document.getElementById('dataDisplayTitle');
    const countElement = document.getElementById('dataDisplayCount');
    const statsRow = document.getElementById('dataStatsRow');

    try {
        switch(viewType) {
            case 'wait':
                titleElement.textContent = '待排产批次数据';
                statsRow.style.display = 'none';
                await loadWaitLotsData();
                break;

            case 'result':
                titleElement.textContent = '排产调度结果';
                statsRow.style.display = 'flex';
                await loadScheduleResultData();
                break;

            case 'done':
                titleElement.textContent = '已完成批次数据';
                statsRow.style.display = 'none';
                await loadDoneLotsData();
                break;
        }
    } catch (error) {
        console.error('切换数据视图失败:', error);
        showNotification('数据加载失败', error.message, 'error');
    }
}

// 加载待排产数据
async function loadWaitLotsData() {
    try {
        console.log('🔄 开始加载待排产数据...');
        const response = await fetch('/api/v2/production/wait-lots');
        console.log('📡 API响应状态:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('📊 待排产数据结果:', result);

        if (result.success) {
            currentDataCache.wait = result.data;
            renderUnifiedTable(result.data, 'wait');
            document.getElementById('dataDisplayCount').textContent = result.data.length;
            console.log('✅ 待排产数据加载成功:', result.data.length, '条记录');
        } else {
            throw new Error(result.message || '获取待排产数据失败');
        }
    } catch (error) {
        console.error('❌ 加载待排产数据失败:', error);
        renderUnifiedTable([], 'wait');
        document.getElementById('dataDisplayCount').textContent = '0';
    }
}

// 加载排产结果数据
async function loadScheduleResultData() {
    try {
        const response = await fetch('/api/v2/production/schedule-results');
        const result = await response.json();

        if (result.success) {
            currentDataCache.result = result.data;
            renderUnifiedTable(result.data, 'result');
            document.getElementById('dataDisplayCount').textContent = result.data.length;

            // 更新统计信息
            updateScheduleStats(result.stats || {});
        } else {
            throw new Error(result.message || '获取排产结果失败');
        }
    } catch (error) {
        console.error('加载排产结果失败:', error);
        renderUnifiedTable([], 'result');
        document.getElementById('dataDisplayCount').textContent = '0';
        updateScheduleStats({});
    }
}

// 加载已完成数据
async function loadDoneLotsData() {
    try {
        const response = await fetch('/api/v2/production/done-lots');
        const result = await response.json();

        if (result.success) {
            currentDataCache.done = result.data;
            renderUnifiedTable(result.data, 'done');
            document.getElementById('dataDisplayCount').textContent = result.data.length;
        } else {
            throw new Error(result.message || '获取已完成数据失败');
        }
    } catch (error) {
        console.error('加载已完成数据失败:', error);
        renderUnifiedTable([], 'done');
        document.getElementById('dataDisplayCount').textContent = '0';
    }
}

// 渲染统一表格
function renderUnifiedTable(data, viewType) {
    const tableHeader = document.getElementById('unifiedTableHeader');
    const tableBody = document.getElementById('unifiedTableBody');

    // 清空现有内容
    tableHeader.innerHTML = '';
    tableBody.innerHTML = '';

    if (!data || data.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="100%" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    暂无数据
                </td>
            </tr>
        `;
        return;
    }

    // 根据视图类型生成不同的表头
    const headers = getTableHeaders(viewType);
    const headerRow = document.createElement('tr');

    headers.forEach(header => {
        const th = document.createElement('th');
        th.textContent = header.label;
        th.style.width = header.width || 'auto';
        headerRow.appendChild(th);
    });

    tableHeader.appendChild(headerRow);

    // 生成表格数据
    data.forEach((row, index) => {
        const tr = document.createElement('tr');

        headers.forEach(header => {
            const td = document.createElement('td');
            const value = row[header.field] || '';

            // 特殊字段处理
            if (header.field === 'STATUS' && viewType === 'result') {
                td.innerHTML = `<span class="badge ${getStatusBadgeClass(value)}">${value}</span>`;
            } else if (header.field === 'PRIORITY' && value) {
                td.innerHTML = `<span class="badge bg-warning">${value}</span>`;
            } else {
                td.textContent = value;
            }

            tr.appendChild(td);
        });

        tableBody.appendChild(tr);
    });
}

// 获取表头配置
function getTableHeaders(viewType) {
    switch(viewType) {
        case 'wait':
            return [
                { field: 'LOT_ID', label: '工单号', width: '15%' },
                { field: 'DEVICE', label: '产品', width: '15%' },
                { field: 'STAGE', label: '工序', width: '10%' },
                { field: 'QTY', label: '数量', width: '8%' },
                { field: 'PKG_PN', label: '封装', width: '12%' },
                { field: 'PO_ID', label: '订单号', width: '12%' },
                { field: 'PRIORITY', label: '优先级', width: '8%' },
                { field: 'DUE_DATE', label: '交期', width: '10%' },
                { field: 'CREATED_TIME', label: '创建时间', width: '10%' }
            ];

        case 'result':
            return [
                { field: 'LOT_ID', label: '工单号', width: '12%' },
                { field: 'DEVICE', label: '产品', width: '12%' },
                { field: 'HANDLER_ID', label: '分选机', width: '10%' },
                { field: 'STAGE', label: '工序', width: '8%' },
                { field: 'QTY', label: '数量', width: '8%' },
                { field: 'START_TIME', label: '开始时间', width: '12%' },
                { field: 'END_TIME', label: '结束时间', width: '12%' },
                { field: 'STATUS', label: '状态', width: '8%' },
                { field: 'PRIORITY', label: '优先级', width: '8%' },
                { field: 'PO_ID', label: '订单号', width: '10%' }
            ];

        case 'done':
            return [
                { field: 'LOT_ID', label: '工单号', width: '15%' },
                { field: 'DEVICE', label: '产品', width: '15%' },
                { field: 'STAGE', label: '工序', width: '10%' },
                { field: 'QTY', label: '数量', width: '8%' },
                { field: 'COMPLETED_TIME', label: '完成时间', width: '12%' },
                { field: 'DURATION', label: '耗时', width: '8%' },
                { field: 'HANDLER_ID', label: '分选机', width: '10%' },
                { field: 'PO_ID', label: '订单号', width: '12%' },
                { field: 'STATUS', label: '状态', width: '10%' }
            ];

        default:
            return [];
    }
}

// 获取状态徽章样式
function getStatusBadgeClass(status) {
    switch(status) {
        case 'SCHEDULED': return 'bg-success';
        case 'RUNNING': return 'bg-primary';
        case 'COMPLETED': return 'bg-info';
        case 'FAILED': return 'bg-danger';
        case 'PENDING': return 'bg-warning';
        default: return 'bg-secondary';
    }
}

// 更新排产统计信息
function updateScheduleStats(stats) {
    document.getElementById('statTotal').textContent = stats.total || 0;
    document.getElementById('statScheduled').textContent = stats.scheduled || 0;
    document.getElementById('statSuccessRate').textContent = stats.successRate || '0%';
    document.getElementById('statExecutionTime').textContent = stats.executionTime || '0s';
}

// 刷新当前数据
function refreshCurrentData() {
    switchDataView(currentDataView);
}

// 兼容原有函数
function refreshTableData() {
    refreshCurrentData();
}

// 🔧 手动排产调整功能
function openManualAdjustment() {
    // 🔥 修复：不再依赖内存中的排产结果，直接使用数据库数据
    console.log('🔧 打开手动调整页面，数据将从数据库加载...');
    
    // 构建跳转URL
            const adjustmentUrl = `/production/done-lots?mode=adjust&session=${Date.now()}`;
    
    // 🔥 关键修复：清理可能存在的旧缓存数据
    try {
        sessionStorage.removeItem('currentScheduleData');
        sessionStorage.removeItem('adjustmentMode');
        sessionStorage.removeItem('sourcePageUrl');
        console.log('🧹 已清理旧的sessionStorage缓存');
    } catch (error) {
        console.warn('清理缓存时出错:', error);
    }
        
        // 跳转到调整页面（新标签页打开）
        window.open(adjustmentUrl, '_blank');
        
    showNotification('页面跳转', '正在打开排产调整工作台，数据将从数据库加载...', 'info');
        
    console.log('✅ 已跳转到调整页面，将使用数据库数据');
}

// 保存并发布排产结果
function saveAndPublishSchedule() {
    if (!currentScheduleResult || !currentScheduleResult.schedule || currentScheduleResult.schedule.length === 0) {
        showNotification('保存失败', '没有可保存的排产结果', 'warning');
        return;
    }

    if (!confirm('确定要保存并发布当前排产结果吗？\n\n发布后将覆盖现有的排产计划，此操作不可撤销。')) {
        return;
    }

    const saveBtn = document.querySelector('button[onclick="saveAndPublishSchedule()"]');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
    saveBtn.disabled = true;

    // 调用保存API
    fetch('/api/v2/production/save-and-publish-schedule', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            schedule: currentScheduleResult.schedule,
            metrics: currentScheduleResult.metrics,
            publish_status: 'PUBLISHED'
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification('保存成功', `已成功保存并发布 ${result.saved_count} 条排产记录`, 'success');
            
            // 可选：清除当前结果或保持显示
            // clearScheduleResult();
        } else {
            showNotification('保存失败', result.message || '保存排产结果时发生错误', 'error');
        }
    })
    .catch(error => {
        console.error('保存排产结果错误:', error);
        showNotification('保存失败', '网络错误或服务器异常', 'error');
    })
    .finally(() => {
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

// 查看排产失败清单
function viewFailedLots() {
    // 打开新窗口显示失败清单
    window.open('/production/failed-lots', '_blank');
}

</script>

<!-- 手动调整会话处理 -->
<script src="{{ url_for('static', filename='js/manual_adjustment_session_handler.js') }}"></script>
<script>
    // 替换原有的批量更新函数
    async function safeBatchUpdate(updates, operationType = 'drag_adjustment') {
        try {
            const result = await window.manualAdjustmentHandler.batchUpdateWithRetry(updates, operationType);
            
            if (result.success) {
                // 成功处理
                showNotification('保存成功', `成功更新 ${result.updated_count} 条记录`, 'success');
                
                // 刷新数据
                if (typeof refreshData === 'function') {
                    refreshData();
                } else if (typeof loadData === 'function') {
                    loadData();
                }
                
                return true;
            } else {
                // 业务逻辑失败
                window.manualAdjustmentHandler.showErrorMessage(
                    new Error(result.message || '更新失败'), 
                    '批量更新'
                );
                return false;
            }
            
        } catch (error) {
            console.error('批量更新异常:', error);
            window.manualAdjustmentHandler.showErrorMessage(error, '批量更新');
            return false;
        }
    }

    // 包装原有的拖拽保存函数
    const originalSaveBatch = window.saveBatch;
    if (originalSaveBatch) {
        window.saveBatch = async function(updates) {
            return await safeBatchUpdate(updates, 'drag_adjustment');
        };
    }

    // 包装排产保存功能
    const originalSaveAndPublishSchedule = saveAndPublishSchedule;
    saveAndPublishSchedule = async function() {
        try {
            // 检查会话状态
            const sessionCheck = await fetch('/api/v2/auth/check-session', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (sessionCheck.status === 401) {
                window.manualAdjustmentHandler.handleSessionExpired();
                return;
            }

            // 会话正常，执行原有逻辑
            return originalSaveAndPublishSchedule();

        } catch (error) {
            if (error.message === 'SESSION_EXPIRED') {
                return; // 已在handleSessionExpired中处理
            }
            // 其他错误继续原有流程
            return originalSaveAndPublishSchedule();
        }
    };
</script>

<!-- 统一批次管理功能 -->
<script src="{{ url_for('static', filename='js/unified_lot_management.js') }}"></script>
<script>
    // 初始化统一批次管理器
    let unifiedLotManager;
    document.addEventListener('DOMContentLoaded', function() {
        unifiedLotManager = new UnifiedLotManager();
        console.log('✅ 统一批次管理器已初始化');
    });
</script>
{% endblock %} 