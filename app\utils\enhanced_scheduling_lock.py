import threading
import time
from typing import Optional, Dict, Any
from contextlib import contextmanager
from app import db
from sqlalchemy import text
import logging

class EnhancedSchedulingLock:
    """增强的排产锁管理"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.app_locks = {}
        self.app_lock_mutex = threading.Lock()
    
    @contextmanager
    def acquire_enhanced_lock(self, algorithm: str, user_id: str, 
                             optimization_target: str, timeout: int = 60):
        """获取增强的排产锁"""
        lock_key = f"{algorithm}_{user_id}_{optimization_target}"
        lock_id = f"sched_lock_{lock_key}_{int(time.time())}"
        
        app_lock_acquired = False
        db_lock_acquired = False
        
        try:
            # 1. 获取应用级锁
            app_lock_acquired = self._acquire_app_lock(lock_key, lock_id, timeout)
            if not app_lock_acquired:
                raise Exception(f"应用级锁获取失败: {lock_key}")
            
            # 2. 获取数据库级锁
            db_lock_acquired = self._acquire_db_lock(lock_id, timeout)
            if not db_lock_acquired:
                raise Exception(f"数据库级锁获取失败: {lock_id}")
            
            self.logger.info(f"🔒 增强锁获取成功: {lock_id}")
            
            # 3. 创建锁上下文
            lock_context = LockContext(lock_id, lock_key, algorithm, user_id, optimization_target)
            
            yield lock_context
            
        finally:
            # 4. 释放锁
            if db_lock_acquired:
                self._release_db_lock(lock_id)
            if app_lock_acquired:
                self._release_app_lock(lock_key)
            
            self.logger.info(f"🔓 增强锁释放完成: {lock_id}")
    
    def _acquire_app_lock(self, lock_key: str, lock_id: str, timeout: int) -> bool:
        """获取应用级锁"""
        end_time = time.time() + timeout
        
        while time.time() < end_time:
            with self.app_lock_mutex:
                if lock_key not in self.app_locks:
                    self.app_locks[lock_key] = {
                        'lock_id': lock_id,
                        'acquired_time': time.time(),
                        'thread_id': threading.current_thread().ident
                    }
                    return True
            
            time.sleep(0.1)  # 短暂等待
        
        return False
    
    def _acquire_db_lock(self, lock_id: str, timeout: int) -> bool:
        """获取数据库级锁 - 临时禁用以避免事务冲突"""
        # 暂时跳过数据库锁，只使用应用级锁来避免事务冲突
        self.logger.debug(f"跳过数据库锁获取: {lock_id}")
        return True
    
    def _release_app_lock(self, lock_key: str):
        """释放应用级锁"""
        with self.app_lock_mutex:
            if lock_key in self.app_locks:
                del self.app_locks[lock_key]
    
    def _release_db_lock(self, lock_id: str):
        """释放数据库级锁 - 临时禁用以避免事务冲突"""
        # 暂时跳过数据库锁释放，只使用应用级锁来避免事务冲突
        self.logger.debug(f"跳过数据库锁释放: {lock_id}")
    
    def get_lock_status(self) -> Dict[str, Any]:
        """获取锁状态"""
        with self.app_lock_mutex:
            return {
                'active_locks': len(self.app_locks),
                'locks': dict(self.app_locks),
                'current_time': time.time()
            }
    
    def cleanup_expired_locks(self, max_age: int = 300):
        """清理过期的锁（默认5分钟）"""
        current_time = time.time()
        expired_keys = []
        
        with self.app_lock_mutex:
            for lock_key, lock_info in self.app_locks.items():
                if current_time - lock_info['acquired_time'] > max_age:
                    expired_keys.append(lock_key)
            
            for key in expired_keys:
                del self.app_locks[key]
                self.logger.warning(f"⚠️ 清理过期锁: {key}")
        
        return len(expired_keys)
    
    def force_release_lock(self, lock_key: str) -> bool:
        """强制释放锁"""
        try:
            with self.app_lock_mutex:
                if lock_key in self.app_locks:
                    lock_info = self.app_locks[lock_key]
                    lock_id = lock_info['lock_id']
                    
                    # 释放数据库锁
                    self._release_db_lock(lock_id)
                    
                    # 释放应用锁
                    del self.app_locks[lock_key]
                    
                    self.logger.warning(f"⚠️ 强制释放锁: {lock_key}")
                    return True
                else:
                    self.logger.warning(f"⚠️ 尝试释放不存在的锁: {lock_key}")
                    return False
        except Exception as e:
            self.logger.error(f"❌ 强制释放锁失败: {e}")
            return False
    
    def check_lock_exists(self, lock_key: str) -> bool:
        """检查锁是否存在"""
        with self.app_lock_mutex:
            return lock_key in self.app_locks
    
    def get_db_lock_status(self) -> Dict[str, Any]:
        """获取数据库锁状态"""
        try:
            result = db.session.execute(text("""
                SELECT 
                    LOCK_NAME,
                    LOCKED_BY_CONNECTION_ID,
                    LOCKED_BY_THREAD_ID
                FROM performance_schema.metadata_locks
                WHERE LOCK_TYPE = 'USER_LOCK'
            """))
            
            locks = []
            for row in result:
                locks.append({
                    'lock_name': row[0],
                    'connection_id': row[1],
                    'thread_id': row[2]
                })
            
            return {
                'db_locks': locks,
                'count': len(locks)
            }
        except Exception as e:
            self.logger.error(f"❌ 获取数据库锁状态失败: {e}")
            return {'db_locks': [], 'count': 0}


class LockContext:
    """锁上下文"""
    def __init__(self, lock_id: str, lock_key: str, algorithm: str, user_id: str, optimization_target: str):
        self.lock_id = lock_id
        self.lock_key = lock_key
        self.algorithm = algorithm
        self.user_id = user_id
        self.optimization_target = optimization_target
        self.acquired_time = time.time()
    
    def get_lock_duration(self) -> float:
        """获取锁持有时间"""
        return time.time() - self.acquired_time
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'lock_id': self.lock_id,
            'lock_key': self.lock_key,
            'algorithm': self.algorithm,
            'user_id': self.user_id,
            'optimization_target': self.optimization_target,
            'acquired_time': self.acquired_time,
            'duration': self.get_lock_duration()
        }


class SchedulingLockManager:
    """排产锁管理器 - 兼容现有API"""
    
    def __init__(self):
        self.enhanced_lock = EnhancedSchedulingLock()
        self.logger = logging.getLogger(__name__)
    
    def acquire_scheduling_lock(self, algorithm: str, user_id: str, 
                               optimization_target: str, parameters: Dict[str, Any]):
        """获取排产锁 - 兼容现有API"""
        return self.enhanced_lock.acquire_enhanced_lock(
            algorithm, user_id, optimization_target, timeout=60
        )
    
    def check_duplicate_execution(self, algorithm: str, user_id: str, 
                                 optimization_target: str, time_window: int = 60) -> bool:
        """检查重复执行"""
        lock_key = f"{algorithm}_{user_id}_{optimization_target}"
        
        # 检查是否有活动锁
        if self.enhanced_lock.check_lock_exists(lock_key):
            return True
        
        # 这里可以添加更复杂的重复检查逻辑，比如检查历史记录
        return False
    
    def get_lock_status(self) -> Dict[str, Any]:
        """获取锁状态"""
        return self.enhanced_lock.get_lock_status()
    
    def cleanup_expired_locks(self, max_age: int = 300):
        """清理过期锁"""
        return self.enhanced_lock.cleanup_expired_locks(max_age)
    
    def force_release_lock(self, lock_key: str) -> bool:
        """强制释放锁"""
        return self.enhanced_lock.force_release_lock(lock_key)


# 全局实例
_lock_manager = None
_scheduling_lock_manager = None

def get_enhanced_scheduling_lock() -> EnhancedSchedulingLock:
    """获取增强排产锁实例"""
    global _lock_manager
    if _lock_manager is None:
        _lock_manager = EnhancedSchedulingLock()
    return _lock_manager

def get_scheduling_lock_manager() -> SchedulingLockManager:
    """获取排产锁管理器实例（兼容现有API）"""
    global _scheduling_lock_manager
    if _scheduling_lock_manager is None:
        _scheduling_lock_manager = SchedulingLockManager()
    return _scheduling_lock_manager

def get_scheduling_history_manager():
    """获取排产历史管理器（兼容现有API）"""
    # 这里应该导入和返回实际的历史管理器
    # 目前返回一个简单的占位符
    class HistoryManager:
        def check_duplicate_execution(self, algorithm: str, user_id: str, 
                                     optimization_target: str) -> bool:
            return False
    
    return HistoryManager() 