<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>定时任务测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>定时任务功能测试</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <button id="testApiBtn" class="btn btn-primary">测试API连接</button>
                <button id="loadTasksBtn" class="btn btn-success">加载任务列表</button>
                <button id="showModalBtn" class="btn btn-info">显示任务管理弹窗</button>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>测试结果：</h3>
                <div id="testResults" class="border p-3" style="min-height: 200px; background-color: #f8f9fa;">
                    <p>点击上面的按钮开始测试...</p>
                </div>
            </div>
        </div>
        
        <!-- 定时任务管理模态框 -->
        <div class="modal fade" id="scheduledTaskModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">定时任务管理</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 12%;">任务名称</th>
                                        <th style="width: 8%;">类型</th>
                                        <th style="width: 12%;">执行时间</th>
                                        <th style="width: 10%;">排产策略</th>
                                        <th style="width: 8%;">状态</th>
                                        <th style="width: 12%;">下次执行</th>
                                        <th style="width: 12%;">上次执行</th>
                                        <th style="width: 12%;">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="scheduledTaskTableBody">
                                    <tr><td colspan="8" class="text-center text-muted">正在加载...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let scheduledTasks = [];
        
        // 测试API连接
        document.getElementById('testApiBtn').addEventListener('click', async function() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<p>🔍 测试API连接...</p>';
            
            try {
                const response = await fetch('/api/v2/system/scheduled-tasks/status');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    results.innerHTML += `<p>✅ API连接成功</p>`;
                    results.innerHTML += `<p>📊 状态: ${JSON.stringify(data.status, null, 2)}</p>`;
                } else {
                    results.innerHTML += `<p>❌ API连接失败: ${data.message || '未知错误'}</p>`;
                }
            } catch (error) {
                results.innerHTML += `<p>❌ 网络错误: ${error.message}</p>`;
            }
        });
        
        // 加载任务列表
        document.getElementById('loadTasksBtn').addEventListener('click', async function() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<p>📋 加载任务列表...</p>';
            
            try {
                const timestamp = Date.now();
                const response = await fetch(`/api/v2/system/scheduled-tasks?_t=${timestamp}`);
                
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    results.innerHTML += '<p>⚠️ API返回非JSON响应，可能需要重新登录</p>';
                    return;
                }
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    scheduledTasks = data.tasks || [];
                    results.innerHTML += `<p>✅ 成功加载 ${scheduledTasks.length} 个任务</p>`;
                    results.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    results.innerHTML += `<p>❌ 加载失败: ${data.message || '未知错误'}</p>`;
                }
            } catch (error) {
                results.innerHTML += `<p>❌ 加载错误: ${error.message}</p>`;
            }
        });
        
        // 显示任务管理弹窗
        document.getElementById('showModalBtn').addEventListener('click', function() {
            // 先加载任务，然后显示弹窗
            loadAndShowTasks();
        });
        
        async function loadAndShowTasks() {
            try {
                // 加载任务
                const timestamp = Date.now();
                const response = await fetch(`/api/v2/system/scheduled-tasks?_t=${timestamp}`);
                
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    console.warn('API返回非JSON响应，可能需要重新登录');
                    scheduledTasks = [];
                } else {
                    const result = await response.json();
                    if (result.success) {
                        scheduledTasks = result.tasks || [];
                    } else {
                        console.error('加载任务失败:', result.message);
                        scheduledTasks = [];
                    }
                }
                
                // 渲染任务列表
                renderTaskList();
                
                // 显示弹窗
                const modal = new bootstrap.Modal(document.getElementById('scheduledTaskModal'));
                modal.show();
                
            } catch (error) {
                console.error('加载任务失败:', error);
                scheduledTasks = [];
                renderTaskList();
                
                const modal = new bootstrap.Modal(document.getElementById('scheduledTaskModal'));
                modal.show();
            }
        }
        
        function renderTaskList() {
            const tbody = document.getElementById('scheduledTaskTableBody');
            
            if (scheduledTasks.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无定时任务</td></tr>';
            } else {
                tbody.innerHTML = scheduledTasks.map(task => {
                    const typeText = getTaskTypeText(task.type);
                    const scheduleText = getScheduleText(task);
                    const strategyText = getStrategyText(task.strategy);
                    const statusText = getStatusText(task.status);
                    const nextExecution = task.nextExecution || '未设置';
                    const lastExecuted = task.lastExecuted || '从未执行';
                    
                    return `
                        <tr>
                            <td>${task.name || '未命名'}</td>
                            <td>${typeText}</td>
                            <td>${scheduleText}</td>
                            <td>${strategyText}</td>
                            <td><span class="badge bg-success">${statusText}</span></td>
                            <td>${nextExecution}</td>
                            <td>${lastExecuted}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="editTask('${task.id}')">编辑</button>
                                <button class="btn btn-sm btn-danger" onclick="deleteTask('${task.id}')">删除</button>
                            </td>
                        </tr>
                    `;
                }).join('');
            }
        }
        
        function getTaskTypeText(type) {
            const typeMap = {
                'once': '一次性',
                'daily': '每日',
                'weekly': '每周',
                'interval': '间隔'
            };
            return typeMap[type] || type;
        }
        
        function getScheduleText(task) {
            switch (task.type) {
                case 'once':
                    return `${task.date} ${task.hour}:${String(task.minute).padStart(2, '0')}`;
                case 'daily':
                    return `每日 ${task.hour}:${String(task.minute).padStart(2, '0')}`;
                case 'weekly':
                    const days = task.weekdays?.join(', ') || '';
                    return `每周 ${days} ${task.hour}:${String(task.minute).padStart(2, '0')}`;
                case 'interval':
                    return `每 ${task.intervalValue} ${task.intervalUnit}`;
                default:
                    return '未设置';
            }
        }
        
        function getStrategyText(strategy) {
            const strategyMap = {
                'intelligent': '智能排产',
                'balanced': '均衡排产',
                'priority': '优先级排产'
            };
            return strategyMap[strategy] || strategy || '未设置';
        }
        
        function getStatusText(status) {
            const statusMap = {
                'active': '运行中',
                'paused': '已暂停',
                'stopped': '已停止'
            };
            return statusMap[status] || status || '未知';
        }
        
        function editTask(taskId) {
            alert(`编辑任务: ${taskId}`);
        }
        
        function deleteTask(taskId) {
            if (confirm('确定要删除这个任务吗？')) {
                alert(`删除任务: ${taskId}`);
            }
        }
    </script>
</body>
</html>
