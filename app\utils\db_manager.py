#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 统一数据库管理器
替换硬编码的数据库连接，使用环境变量配置
"""

import pymysql
import redis
import logging
from typing import Optional, Dict, Any
from contextlib import contextmanager
from config.enhanced_config import ConfigManager

logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库连接管理器"""
    
    def __init__(self, config=None):
        self.config = config or ConfigManager.get_config()
        self._mysql_pool = None
        self._redis_client = None
    
    def get_mysql_connection(self, **kwargs):
        """获取MySQL连接"""
        try:
            connection_params = {
                'host': self.config.DB_HOST,
                'port': self.config.DB_PORT,
                'user': self.config.DB_USER,
                'password': self.config.DB_PASSWORD,
                'database': kwargs.get('database', self.config.DB_NAME),
                'charset': self.config.DB_CHARSET,
                'autocommit': kwargs.get('autocommit', False),
                'connect_timeout': self.config.DB_POOL_TIMEOUT,
                'cursorclass': kwargs.get('cursorclass', pymysql.cursors.DictCursor)  # 🔥 默认使用DictCursor
            }
            
            # 移除None值
            connection_params = {k: v for k, v in connection_params.items() if v is not None}
            
            if not connection_params.get('password'):
                raise ValueError("数据库密码未配置，请检查环境变量 DB_PASSWORD")
            
            connection = pymysql.connect(**connection_params)
            logger.debug(f"成功连接到MySQL数据库: {self.config.DB_HOST}:{self.config.DB_PORT}/{self.config.DB_NAME}")
            return connection
            
        except Exception as e:
            logger.error(f"MySQL数据库连接失败: {e}")
            raise
    
    @contextmanager
    def get_mysql_cursor(self, **kwargs):
        """获取MySQL游标上下文管理器"""
        connection = None
        cursor = None
        try:
            connection = self.get_mysql_connection(**kwargs)
            cursor = connection.cursor()
            yield cursor
            connection.commit()
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    def get_redis_connection(self, **kwargs):
        """获取Redis连接"""
        try:
            if self._redis_client is None:
                connection_params = {
                    'host': self.config.REDIS_HOST,
                    'port': self.config.REDIS_PORT,
                    'db': self.config.REDIS_DB,
                    'decode_responses': kwargs.get('decode_responses', True),
                    'socket_timeout': kwargs.get('socket_timeout', 5),
                    'socket_connect_timeout': kwargs.get('socket_connect_timeout', 5)
                }
                
                if self.config.REDIS_PASSWORD:
                    connection_params['password'] = self.config.REDIS_PASSWORD
                
                self._redis_client = redis.Redis(**connection_params)
                
                # 测试连接
                self._redis_client.ping()
                logger.debug(f"成功连接到Redis: {self.config.REDIS_HOST}:{self.config.REDIS_PORT}")
            
            return self._redis_client
            
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            # Redis连接失败不应该阻止应用启动
            return None
    
    def test_mysql_connection(self) -> Dict[str, Any]:
        """测试MySQL连接"""
        try:
            with self.get_mysql_cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                return {
                    'status': 'success',
                    'version': version,
                    'host': self.config.DB_HOST,
                    'port': self.config.DB_PORT,
                    'database': self.config.DB_NAME
                }
        except Exception as e:
            return {
                'status': 'failed',
                'error': str(e),
                'host': self.config.DB_HOST,
                'port': self.config.DB_PORT,
                'database': self.config.DB_NAME
            }
    
    def test_redis_connection(self) -> Dict[str, Any]:
        """测试Redis连接"""
        try:
            redis_client = self.get_redis_connection()
            if redis_client:
                info = redis_client.info()
                return {
                    'status': 'success',
                    'version': info.get('redis_version'),
                    'host': self.config.REDIS_HOST,
                    'port': self.config.REDIS_PORT,
                    'db': self.config.REDIS_DB
                }
            else:
                return {
                    'status': 'failed',
                    'error': 'Redis连接失败',
                    'host': self.config.REDIS_HOST,
                    'port': self.config.REDIS_PORT
                }
        except Exception as e:
            return {
                'status': 'failed',
                'error': str(e),
                'host': self.config.REDIS_HOST,
                'port': self.config.REDIS_PORT
            }
    
    def close_connections(self):
        """关闭所有连接"""
        if self._redis_client:
            try:
                self._redis_client.close()
                self._redis_client = None
                logger.debug("Redis连接已关闭")
            except Exception as e:
                logger.warning(f"关闭Redis连接时出错: {e}")

# 全局数据库管理器实例
db_manager = DatabaseManager()

# 向后兼容的函数
def get_mysql_connection(**kwargs):
    """获取MySQL连接（向后兼容）"""
    return db_manager.get_mysql_connection(**kwargs)

def get_redis_connection(**kwargs):
    """获取Redis连接（向后兼容）"""
    return db_manager.get_redis_connection(**kwargs)

# 兼容旧的硬编码方式
def create_mysql_connection(host=None, port=None, user=None, password=None, 
                           database=None, charset=None, **kwargs):
    """创建MySQL连接（兼容旧代码）"""
    config = db_manager.config
    
    # 使用传入参数或配置中的默认值
    connection_params = {
        'host': host or config.DB_HOST,
        'port': port or config.DB_PORT,
        'user': user or config.DB_USER,
        'password': password or config.DB_PASSWORD,
        'database': database or config.DB_NAME,
        'charset': charset or config.DB_CHARSET,
        **kwargs
    }
    
    # 移除None值
    connection_params = {k: v for k, v in connection_params.items() if v is not None}
    
    return pymysql.connect(**connection_params)

if __name__ == '__main__':
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    # 测试MySQL
    mysql_result = db_manager.test_mysql_connection()
    if mysql_result['status'] == 'success':
        print(f"✅ MySQL连接成功: {mysql_result['version']}")
    else:
        print(f"❌ MySQL连接失败: {mysql_result['error']}")
    
    # 测试Redis
    redis_result = db_manager.test_redis_connection()
    if redis_result['status'] == 'success':
        print(f"✅ Redis连接成功: {redis_result['version']}")
    else:
        print(f"⚠️ Redis连接失败: {redis_result['error']}") 