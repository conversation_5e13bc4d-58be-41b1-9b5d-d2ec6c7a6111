#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排产失败批次API接口
专门处理SCHEDULING_STATUS='FAILED'的批次查询、分析和救援功能
"""

import logging
import json
from flask import Blueprint, request, jsonify
from sqlalchemy import text
from app import db
from app.utils.api_config import get_api_route
from flask_login import login_required

logger = logging.getLogger(__name__)

# 创建蓝图
failed_lots_bp = Blueprint('failed_lots_api', __name__)

# 兼容性导出
failed_lots_api = failed_lots_bp

def _check_schema_compatibility():
    """检查数据库schema是否支持统一数据模型"""
    try:
        check_query = text("""
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'lotprioritydone' 
            AND column_name = 'SCHEDULING_STATUS'
        """)
        result = db.session.execute(check_query)
        has_status_field = result.scalar() > 0
        return has_status_field
    except Exception as e:
        logger.error(f"检查schema兼容性失败: {e}")
        return False

def generate_failure_suggestion(failure_reason, failure_details):
    """生成失败原因的建议解决方案"""
    try:
        if "配置需求获取失败" in failure_reason or "CONFIGURATION_NOT_FOUND" in str(failure_details):
            return "请在ET_FT_TEST_SPEC表中补充对应器件和工序的测试规范"
        elif "无合适设备" in failure_reason or "NO_SUITABLE_EQUIPMENT" in str(failure_details):
            return "请检查设备状态和配置匹配，确保有可用的设备"
        elif "设备ID无效" in failure_reason:
            return "请检查设备配置，确保HANDLER_ID字段正确"
        elif "算法执行异常" in failure_reason or "ALGORITHM_EXCEPTION" in str(failure_details):
            return "请检查系统日志，可能需要技术支持"
        elif "测试规范缺失" in failure_reason:
            return "请在ET_FT_TEST_SPEC表中补充对应器件和工序的测试规范"
        elif "配置" in failure_reason or "config" in failure_reason.lower():
            return "请检查配置设置和参数是否正确"
        elif "设备" in failure_reason or "不兼容" in failure_reason:
            return "请检查设备兼容性和状态"
        else:
            return "请联系技术支持进行详细分析"
    except Exception:
        return "请联系技术支持进行详细分析"

@failed_lots_api.route(get_api_route('production/failed-lots'), methods=['GET'])
def get_failed_lots():
    """获取排产失败批次数据"""
    try:
        # 检查schema兼容性
        is_unified_schema = _check_schema_compatibility()
        
        if not is_unified_schema:
            return jsonify({
                'success': False,
                'message': '当前数据库架构不支持失败批次查询功能',
                'suggestion': '请运行数据库迁移以支持统一数据模型'
            }), 400
        
        # 获取请求参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 50))
        device_filter = request.args.get('device', '')
        stage_filter = request.args.get('stage', '')
        failure_reason_filter = request.args.get('failure_reason', '')
        session_id_filter = request.args.get('session_id', '')
        
        # 计算偏移量
        offset = (page - 1) * size
        
        # 构建WHERE条件
        where_conditions = ["SCHEDULING_STATUS = 'FAILED'"]
        params = {'size': size, 'offset': offset}
        
        if device_filter:
            where_conditions.append("DEVICE LIKE :device_filter")
            params['device_filter'] = f'%{device_filter}%'
            
        if stage_filter:
            where_conditions.append("STAGE LIKE :stage_filter")
            params['stage_filter'] = f'%{stage_filter}%'
            
        if failure_reason_filter:
            where_conditions.append("FAILURE_REASON LIKE :failure_reason_filter")
            params['failure_reason_filter'] = f'%{failure_reason_filter}%'
            
        if session_id_filter:
            where_conditions.append("SESSION_ID = :session_id_filter")
            params['session_id_filter'] = session_id_filter
        
        where_clause = "WHERE " + " AND ".join(where_conditions)
        
        # 查询总数
        count_query = text(f"""
            SELECT COUNT(*) FROM lotprioritydone 
            {where_clause}
        """)
        total_result = db.session.execute(count_query, params)
        total = total_result.scalar() or 0
        
        # 🔥 计算失败统计信息
        stats_query = text(f"""
            SELECT 
                COUNT(*) as total_failed,
                COUNT(DISTINCT DEVICE) as failed_devices,
                COUNT(DISTINCT STAGE) as failed_stages,
                COUNT(DISTINCT SESSION_ID) as failed_sessions,
                SUM(GOOD_QTY) as total_failed_quantity
            FROM lotprioritydone 
            {where_clause}
        """)
        
        stats_result = db.session.execute(stats_query, params)
        stats_row = stats_result.fetchone()
        
        # 构建统计信息
        statistics = {
            'total_failed': stats_row[0] or 0,
            'failed_devices': stats_row[1] or 0,
            'failed_stages': stats_row[2] or 0,
            'failed_sessions': stats_row[3] or 0,
            'total_failed_quantity': int(stats_row[4] or 0)
        }
        
        # 🔥 失败原因分析
        reason_analysis_query = text(f"""
            SELECT 
                FAILURE_REASON,
                COUNT(*) as count,
                GROUP_CONCAT(DISTINCT DEVICE SEPARATOR ', ') as affected_devices
            FROM lotprioritydone 
            {where_clause}
            GROUP BY FAILURE_REASON
            ORDER BY count DESC
        """)
        
        reason_result = db.session.execute(reason_analysis_query, params)
        failure_analysis = []
        for row in reason_result.fetchall():
            failure_analysis.append({
                'reason': row[0] or '未知原因',
                'count': row[1],
                'affected_devices': row[2] or '',
                'suggestion': generate_failure_suggestion(row[0] or '', {})
            })
        
        # 查询失败批次数据
        data_query = text(f"""
            SELECT 
                id, PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE,
                WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                RELEASE_TIME, FAC_ID, CREATE_TIME,
                match_type, comprehensive_score, processing_time, changeover_time,
                algorithm_version, priority_score,
                SCHEDULING_STATUS, FAILURE_REASON, FAILURE_DETAILS, SESSION_ID
            FROM lotprioritydone 
            {where_clause}
            ORDER BY CREATE_TIME DESC, PRIORITY ASC
            LIMIT :size OFFSET :offset
        """)
        
        result = db.session.execute(data_query, params)
        
        # 构建返回数据
        records = []
        for row in result.fetchall():
            # 解析failure_details JSON
            failure_details = {}
            try:
                if row[29]:  # FAILURE_DETAILS字段
                    failure_details = json.loads(row[29]) if isinstance(row[29], str) else row[29]
            except (json.JSONDecodeError, TypeError):
                failure_details = {"raw_data": str(row[29])}
            
            record = {
                'id': row[0],
                'PRIORITY': row[1] or 9999,
                'HANDLER_ID': row[2] or '',
                'LOT_ID': row[3] or '',
                'LOT_TYPE': row[4] or '',
                'GOOD_QTY': row[5] or 0,
                'PROD_ID': row[6] or '',
                'DEVICE': row[7] or '',
                'CHIP_ID': row[8] or '',
                'PKG_PN': row[9] or '',
                'PO_ID': row[10] or '',
                'STAGE': row[11] or '',
                'WIP_STATE': row[12] or '',
                'PROC_STATE': row[13] or '',
                'HOLD_STATE': row[14] or 0,
                'FLOW_ID': row[15] or '',
                'FLOW_VER': row[16] or '',
                'RELEASE_TIME': row[17] or '',
                'FAC_ID': row[18] or '',
                'CREATE_TIME': row[19] or '',
                'match_type': row[20] or '',
                'comprehensive_score': row[21] or 0.0,
                'processing_time': row[22] or 0.0,
                'changeover_time': row[23] or 0.0,
                'algorithm_version': row[24] or '',
                'priority_score': row[25] or 0.0,
                # 🔥 失败批次特有字段
                'SCHEDULING_STATUS': row[26] or 'FAILED',
                'FAILURE_REASON': row[27] or '',
                'FAILURE_DETAILS': failure_details,
                'SESSION_ID': row[28] or '',
                # 🔥 生成建议
                'suggestion': generate_failure_suggestion(row[27] or '', failure_details),
                # 🔥 可救援标识
                'can_rescue': bool(row[7] and row[11]),  # 有DEVICE和STAGE才能救援
            }
            
            records.append(record)
        
        # 计算分页信息
        total_pages = (total + size - 1) // size
        
        logger.info(f"📊 失败批次查询: 第{page}页, {size}条/页, 共{total}条失败记录")
        
        return jsonify({
            'success': True,
            'data': records,
            'pagination': {
                'page': page,
                'size': size,
                'total': total,
                'total_pages': total_pages
            },
            'statistics': statistics,
            'failure_analysis': failure_analysis,
            'schema_info': {
                'is_unified_schema': is_unified_schema,
                'supports_failed_lots': True
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 获取失败批次数据失败: {e}")
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}'
        }), 500

@failed_lots_api.route(get_api_route('production/failed-lots/analysis'), methods=['GET'])
def get_failure_analysis():
    """获取失败原因分析报告"""
    try:
        is_unified_schema = _check_schema_compatibility()
        
        if not is_unified_schema:
            return jsonify({
                'success': False,
                'message': '当前数据库架构不支持失败分析功能'
            }), 400
        
        # 按失败原因分组统计
        reason_query = text("""
            SELECT 
                FAILURE_REASON,
                COUNT(*) as failure_count,
                COUNT(DISTINCT DEVICE) as affected_devices_count,
                COUNT(DISTINCT STAGE) as affected_stages_count,
                SUM(GOOD_QTY) as total_lost_quantity,
                GROUP_CONCAT(DISTINCT DEVICE ORDER BY DEVICE SEPARATOR ', ') as device_list,
                GROUP_CONCAT(DISTINCT STAGE ORDER BY STAGE SEPARATOR ', ') as stage_list
            FROM lotprioritydone 
            WHERE SCHEDULING_STATUS = 'FAILED'
            GROUP BY FAILURE_REASON
            ORDER BY failure_count DESC
        """)
        
        reason_result = db.session.execute(reason_query)
        
        failure_reasons = []
        for row in reason_result.fetchall():
            reason_data = {
                'reason': row[0] or '未知原因',
                'failure_count': row[1],
                'affected_devices_count': row[2],
                'affected_stages_count': row[3],
                'total_lost_quantity': int(row[4] or 0),
                'device_list': row[5] or '',
                'stage_list': row[6] or '',
                'suggestion': generate_failure_suggestion(row[0] or '', {}),
                'priority': 'HIGH' if row[1] > 10 else 'MEDIUM' if row[1] > 5 else 'LOW'
            }
            failure_reasons.append(reason_data)
        
        # 按设备分组统计
        device_query = text("""
            SELECT 
                DEVICE,
                COUNT(*) as failure_count,
                COUNT(DISTINCT FAILURE_REASON) as reason_count,
                SUM(GOOD_QTY) as lost_quantity
            FROM lotprioritydone 
            WHERE SCHEDULING_STATUS = 'FAILED'
            GROUP BY DEVICE
            ORDER BY failure_count DESC
            LIMIT 10
        """)
        
        device_result = db.session.execute(device_query)
        
        top_failed_devices = []
        for row in device_result.fetchall():
            device_data = {
                'device': row[0] or '未知设备',
                'failure_count': row[1],
                'reason_count': row[2],
                'lost_quantity': int(row[3] or 0)
            }
            top_failed_devices.append(device_data)
        
        # 按工序分组统计
        stage_query = text("""
            SELECT 
                STAGE,
                COUNT(*) as failure_count,
                COUNT(DISTINCT DEVICE) as device_count,
                SUM(GOOD_QTY) as lost_quantity
            FROM lotprioritydone 
            WHERE SCHEDULING_STATUS = 'FAILED'
            GROUP BY STAGE
            ORDER BY failure_count DESC
            LIMIT 10
        """)
        
        stage_result = db.session.execute(stage_query)
        
        top_failed_stages = []
        for row in stage_result.fetchall():
            stage_data = {
                'stage': row[0] or '未知工序',
                'failure_count': row[1],
                'device_count': row[2],
                'lost_quantity': int(row[3] or 0)
            }
            top_failed_stages.append(stage_data)
        
        return jsonify({
            'success': True,
            'analysis': {
                'failure_reasons': failure_reasons,
                'top_failed_devices': top_failed_devices,
                'top_failed_stages': top_failed_stages
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 获取失败分析报告失败: {e}")
        return jsonify({
            'success': False,
            'message': f'分析失败: {str(e)}'
        }), 500

@failed_lots_api.route(get_api_route('production/failed-lots/sessions'), methods=['GET'])
def get_failed_sessions():
    """获取排产会话失败统计"""
    try:
        is_unified_schema = _check_schema_compatibility()
        
        if not is_unified_schema:
            return jsonify({
                'success': False,
                'message': '当前数据库架构不支持会话统计功能'
            }), 400
        
        # 按会话统计成功和失败
        session_query = text("""
            SELECT 
                SESSION_ID,
                COUNT(*) as total_lots,
                SUM(CASE WHEN SCHEDULING_STATUS = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
                SUM(CASE WHEN SCHEDULING_STATUS = 'FAILED' THEN 1 ELSE 0 END) as failed_count,
                MAX(CREATE_TIME) as last_execution_time
            FROM lotprioritydone 
            WHERE SESSION_ID IS NOT NULL AND SESSION_ID != ''
            GROUP BY SESSION_ID
            ORDER BY last_execution_time DESC
            LIMIT 20
        """)
        
        session_result = db.session.execute(session_query)
        
        sessions = []
        for row in session_result.fetchall():
            total = row[1]
            success = row[2]
            failed = row[3]
            success_rate = (success / total * 100) if total > 0 else 0
            
            session_data = {
                'session_id': row[0],
                'total_lots': total,
                'success_count': success,
                'failed_count': failed,
                'success_rate': f'{success_rate:.1f}%',
                'last_execution_time': row[4],
                'status': 'PARTIAL_SUCCESS' if failed > 0 else 'SUCCESS'
            }
            sessions.append(session_data)
        
        return jsonify({
            'success': True,
            'sessions': sessions
        })
        
    except Exception as e:
        logger.error(f"❌ 获取会话统计失败: {e}")
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}'
        }), 500

@failed_lots_api.route(get_api_route('production/failed-lots/clear'), methods=['POST'])
@login_required
def clear_failed_lots():
    """清空失败批次记录"""
    try:
        is_unified_schema = _check_schema_compatibility()
        
        if not is_unified_schema:
            return jsonify({
                'success': False,
                'message': '当前数据库架构不支持失败批次清理功能'
            }), 400
        
        # 统计删除前的数量
        count_query = text("SELECT COUNT(*) FROM lotprioritydone WHERE SCHEDULING_STATUS = 'FAILED'")
        count_result = db.session.execute(count_query)
        before_count = count_result.scalar() or 0
        
        # 删除失败批次
        delete_query = text("DELETE FROM lotprioritydone WHERE SCHEDULING_STATUS = 'FAILED'")
        db.session.execute(delete_query)
        db.session.commit()
        
        logger.info(f"✅ 成功清空 {before_count} 条失败批次记录")
        
        return jsonify({
            'success': True,
            'message': f'成功清空 {before_count} 条失败批次记录',
            'deleted_count': before_count
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 清空失败批次记录失败: {e}")
        return jsonify({
            'success': False,
            'message': f'清空失败: {str(e)}'
        }), 500 