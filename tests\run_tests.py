#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器 - 执行所有单元测试
"""

import sys
import os
import unittest
import importlib.util

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def run_all_tests():
    """运行所有测试"""
    print("🧪 开始执行单元测试...")
    
    # 设置测试目录
    test_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 发现和运行测试
    loader = unittest.TestLoader()
    suite = loader.discover(test_dir, pattern='test_*.py')
    
    # 执行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果摘要
    print("\n" + "="*60)
    print("🧪 测试结果摘要:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print("="*60)
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback.split('Error:')[-1].strip()}")
    
    # 返回是否所有测试都通过
    return len(result.failures) == 0 and len(result.errors) == 0

def run_specific_test(test_module):
    """运行特定测试模块"""
    print(f"🧪 运行测试模块: {test_module}")
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromName(test_module)
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return len(result.failures) == 0 and len(result.errors) == 0

def check_test_dependencies():
    """检查测试依赖"""
    print("🔍 检查测试依赖...")
    
    dependencies = [
        'unittest',
        'unittest.mock'
    ]
    
    missing_deps = []
    
    for dep in dependencies:
        try:
            importlib.import_module(dep)
            print(f"✅ {dep}")
        except ImportError:
            missing_deps.append(dep)
            print(f"❌ {dep} - 缺失")
    
    if missing_deps:
        print(f"\n⚠️ 缺少依赖: {', '.join(missing_deps)}")
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def integration_test_summary():
    """集成测试摘要"""
    print("\n🔄 executeManualScheduling() 功能集成测试摘要")
    print("="*60)
    
    test_areas = [
        "✅ 数据模型和字段映射",
        "✅ 持久化管理器",
        "✅ 增强的锁管理",
        "✅ 事务管理",
        "✅ 批量操作优化",
        "✅ 性能监控",
        "✅ 组件集成"
    ]
    
    for area in test_areas:
        print(f"  {area}")
    
    print("\n📊 测试覆盖的关键功能:")
    print("  • 字段映射修复 (38个字段)")
    print("  • 数据完整性验证")
    print("  • 并发控制增强")
    print("  • 数据库操作优化")
    print("  • 性能监控集成")
    print("  • 错误处理和异常管理")
    
    print("\n🎯 预期改进效果:")
    print("  • 解决字段映射不匹配问题")
    print("  • 提升数据库保存性能")
    print("  • 增强并发控制稳定性")
    print("  • 提供完整的性能监控")
    print("="*60)

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='运行executeManualScheduling优化测试')
    parser.add_argument('--module', '-m', type=str, help='运行特定测试模块')
    parser.add_argument('--check-deps', '-c', action='store_true', help='检查测试依赖')
    parser.add_argument('--summary', '-s', action='store_true', help='显示集成测试摘要')
    
    args = parser.parse_args()
    
    if args.check_deps:
        success = check_test_dependencies()
        sys.exit(0 if success else 1)
    
    if args.summary:
        integration_test_summary()
        sys.exit(0)
    
    if args.module:
        success = run_specific_test(args.module)
    else:
        success = run_all_tests()
        if success:
            integration_test_summary()
    
    sys.exit(0 if success else 1) 