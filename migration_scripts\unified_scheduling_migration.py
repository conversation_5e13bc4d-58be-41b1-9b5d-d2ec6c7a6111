#!/usr/bin/env python3
"""
统一排产数据架构迁移脚本
将scheduling_failed_lots表数据迁移到扩展的lotprioritydone表

迁移策略：
1. 扩展lotprioritydone表结构，添加状态字段
2. 将失败批次数据迁移到统一表
3. 保持数据完整性和一致性
4. 提供完整的回滚机制

版本：v1.0
创建：2025-01-14
"""

import mysql.connector
from mysql.connector import Error
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UnifiedSchedulingMigration:
    """统一排产数据架构迁移器"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'aps',
            'user': 'root',
            'password': 'WWWwww123!',
            'charset': 'utf8mb4'
        }
        self.migration_stats = {
            'start_time': None,
            'end_time': None,
            'success_records_before': 0,
            'failed_records_before': 0,
            'migrated_records': 0,
            'total_records_after': 0,
            'status': 'pending'
        }
        self.backup_table_created = False
        
    def execute_migration(self) -> bool:
        """执行完整的迁移流程"""
        logger.info("🚀 开始统一排产数据架构迁移...")
        self.migration_stats['start_time'] = datetime.now()
        
        try:
            # 步骤1：数据备份和预检查
            if not self._pre_migration_check():
                raise Exception("迁移前检查失败")
            
            # 步骤2：创建备份表
            if not self._create_backup_tables():
                raise Exception("备份表创建失败")
            
            # 步骤3：扩展lotprioritydone表结构
            if not self._extend_lotprioritydone_schema():
                raise Exception("表结构扩展失败")
            
            # 步骤4：迁移失败批次数据
            if not self._migrate_failed_lots_data():
                raise Exception("失败批次数据迁移失败")
            
            # 步骤5：数据完整性验证
            if not self._validate_migration_integrity():
                raise Exception("数据完整性验证失败")
            
            # 步骤6：完成迁移
            self._finalize_migration()
            
            self.migration_stats['status'] = 'success'
            self.migration_stats['end_time'] = datetime.now()
            
            logger.info("✅ 统一排产数据架构迁移成功完成！")
            self._print_migration_summary()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 迁移失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            
            # 执行回滚
            logger.info("🔄 开始执行回滚...")
            self._rollback_migration()
            
            self.migration_stats['status'] = 'failed'
            self.migration_stats['end_time'] = datetime.now()
            
            return False
    
    def _pre_migration_check(self) -> bool:
        """迁移前检查"""
        logger.info("🔍 执行迁移前检查...")
        
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor(dictionary=True)
            
            # 检查源表存在性
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'lotprioritydone'
            """)
            if cursor.fetchone()['count'] == 0:
                raise Exception("lotprioritydone表不存在")
            
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots'
            """)
            failed_table_exists = cursor.fetchone()['count'] > 0
            
            # 统计当前数据量
            cursor.execute("SELECT COUNT(*) as count FROM lotprioritydone")
            self.migration_stats['success_records_before'] = cursor.fetchone()['count']
            
            if failed_table_exists:
                cursor.execute("SELECT COUNT(*) as count FROM scheduling_failed_lots")
                self.migration_stats['failed_records_before'] = cursor.fetchone()['count']
            else:
                logger.warning("⚠️ scheduling_failed_lots表不存在，跳过失败批次迁移")
                self.migration_stats['failed_records_before'] = 0
            
            # 检查ET_WAIT_LOT表（用于补全字段信息）
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'ET_WAIT_LOT'
            """)
            if cursor.fetchone()['count'] == 0:
                logger.warning("⚠️ ET_WAIT_LOT表不存在，将使用默认值填充缺失字段")
            
            cursor.close()
            conn.close()
            
            logger.info(f"📊 迁移前数据统计:")
            logger.info(f"   - 成功批次: {self.migration_stats['success_records_before']} 条")
            logger.info(f"   - 失败批次: {self.migration_stats['failed_records_before']} 条")
            logger.info(f"   - 总计批次: {self.migration_stats['success_records_before'] + self.migration_stats['failed_records_before']} 条")
            
            return True
            
        except Exception as e:
            logger.error(f"迁移前检查失败: {e}")
            return False
    
    def _create_backup_tables(self) -> bool:
        """创建备份表"""
        logger.info("💾 创建数据备份表...")
        
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 创建lotprioritydone备份表
            backup_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_table_name = f"lotprioritydone_backup_{backup_timestamp}"
            
            cursor.execute(f"""
                CREATE TABLE {backup_table_name} AS 
                SELECT * FROM lotprioritydone
            """)
            
            logger.info(f"✅ 创建备份表成功: {backup_table_name}")
            self.backup_table_created = True
            
            # 如果scheduling_failed_lots表存在，也创建备份
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots'
            """)
            
            result = cursor.fetchone()
            if result[0] > 0:
                failed_backup_table_name = f"scheduling_failed_lots_backup_{backup_timestamp}"
                cursor.execute(f"""
                    CREATE TABLE {failed_backup_table_name} AS 
                    SELECT * FROM scheduling_failed_lots
                """)
                logger.info(f"✅ 创建失败批次备份表成功: {failed_backup_table_name}")
            
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            logger.error(f"创建备份表失败: {e}")
            return False
    
    def _extend_lotprioritydone_schema(self) -> bool:
        """扩展lotprioritydone表结构"""
        logger.info("🔧 扩展lotprioritydone表结构...")
        
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 检查字段是否已存在（防重复执行）
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = 'lotprioritydone' 
                AND column_name = 'SCHEDULING_STATUS'
            """)
            
            if cursor.fetchone()[0] > 0:
                logger.info("⚠️ SCHEDULING_STATUS字段已存在，跳过结构扩展")
                cursor.close()
                conn.close()
                return True
            
            # 添加新字段
            schema_changes = [
                """
                ALTER TABLE lotprioritydone 
                ADD COLUMN SCHEDULING_STATUS ENUM('SUCCESS', 'FAILED', 'MANUAL_ADJUSTED') 
                DEFAULT 'SUCCESS' 
                COMMENT '排产状态：SUCCESS=排产成功，FAILED=排产失败，MANUAL_ADJUSTED=手动调整'
                """,
                """
                ALTER TABLE lotprioritydone 
                ADD COLUMN FAILURE_REASON VARCHAR(500) NULL 
                COMMENT '失败原因（仅失败批次使用）'
                """,
                """
                ALTER TABLE lotprioritydone 
                ADD COLUMN FAILURE_DETAILS TEXT NULL 
                COMMENT '失败详情（仅失败批次使用）'
                """,
                """
                ALTER TABLE lotprioritydone 
                ADD COLUMN SESSION_ID VARCHAR(50) NULL 
                COMMENT '排产会话ID'
                """,
                """
                ALTER TABLE lotprioritydone 
                ADD INDEX idx_scheduling_status (SCHEDULING_STATUS)
                """,
                """
                ALTER TABLE lotprioritydone 
                ADD INDEX idx_session_id (SESSION_ID)
                """
            ]
            
            for i, sql in enumerate(schema_changes, 1):
                try:
                    cursor.execute(sql)
                    logger.info(f"✅ 结构变更 {i}/{len(schema_changes)} 完成")
                except Error as e:
                    if "Duplicate column name" in str(e) or "Duplicate key name" in str(e):
                        logger.warning(f"⚠️ 字段或索引已存在，跳过: {e}")
                    else:
                        raise e
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info("✅ lotprioritydone表结构扩展完成")
            return True
            
        except Exception as e:
            logger.error(f"表结构扩展失败: {e}")
            return False
    
    def _migrate_failed_lots_data(self) -> bool:
        """迁移失败批次数据"""
        logger.info("📦 开始迁移失败批次数据...")
        
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor(dictionary=True)
            
            # 检查scheduling_failed_lots表是否存在
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots'
            """)
            
            if cursor.fetchone()['count'] == 0:
                logger.warning("⚠️ scheduling_failed_lots表不存在，跳过失败批次迁移")
                cursor.close()
                conn.close()
                return True
            
            # 查询所有失败批次数据
            failed_lots_query = """
            SELECT 
                sfl.lot_id,
                sfl.device,
                sfl.stage,
                sfl.good_qty,
                sfl.failure_reason,
                sfl.failure_details,
                sfl.session_id,
                sfl.timestamp,
                sfl.algorithm_version,
                -- 从ET_WAIT_LOT表获取完整字段信息
                ewl.LOT_TYPE,
                ewl.PROD_ID,
                ewl.CHIP_ID,
                ewl.PKG_PN,
                ewl.PO_ID,
                ewl.WIP_STATE,
                ewl.PROC_STATE,
                ewl.HOLD_STATE,
                ewl.FLOW_ID,
                ewl.FLOW_VER,
                ewl.RELEASE_TIME,
                ewl.FAC_ID,
                ewl.CREATE_TIME
            FROM scheduling_failed_lots sfl
            LEFT JOIN ET_WAIT_LOT ewl ON sfl.lot_id = ewl.LOT_ID
            ORDER BY sfl.timestamp ASC
            """
            
            cursor.execute(failed_lots_query)
            failed_lots = cursor.fetchall()
            
            logger.info(f"📊 找到 {len(failed_lots)} 条失败批次记录待迁移")
            
            if not failed_lots:
                logger.info("✅ 无失败批次数据需要迁移")
                cursor.close()
                conn.close()
                return True
            
            # 批量插入到lotprioritydone表
            insert_query = """
            INSERT INTO lotprioritydone (
                LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, DEVICE, CHIP_ID, PKG_PN,
                PO_ID, STAGE, WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                FAC_ID, CREATE_TIME, PRIORITY, HANDLER_ID,
                SCHEDULING_STATUS, FAILURE_REASON, FAILURE_DETAILS, SESSION_ID
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """
            
            migrated_count = 0
            batch_size = 100
            
            for i in range(0, len(failed_lots), batch_size):
                batch = failed_lots[i:i + batch_size]
                insert_data = []
                
                for lot in batch:
                    # 准备插入数据，处理NULL值
                    insert_data.append((
                        lot['lot_id'],
                        lot.get('LOT_TYPE') or '',
                        lot['good_qty'] or 0,
                        lot.get('PROD_ID') or '',
                        lot['device'] or '',
                        lot.get('CHIP_ID') or '',
                        lot.get('PKG_PN') or '',
                        lot.get('PO_ID') or '',
                        lot['stage'] or '',
                        lot.get('WIP_STATE') or '',
                        lot.get('PROC_STATE') or '',
                        lot.get('HOLD_STATE') or 0,
                        lot.get('FLOW_ID') or '',
                        lot.get('FLOW_VER') or '',
                        lot.get('FAC_ID') or '',
                        lot.get('CREATE_TIME') or lot['timestamp'],
                        999,  # 失败批次设置低优先级
                        None,  # 失败批次无分配设备
                        'FAILED',  # 状态设为失败
                        lot['failure_reason'],
                        lot['failure_details'],
                        lot['session_id']
                    ))
                
                cursor.executemany(insert_query, insert_data)
                migrated_count += len(batch)
                
                logger.info(f"📦 迁移进度: {migrated_count}/{len(failed_lots)} ({migrated_count/len(failed_lots)*100:.1f}%)")
            
            conn.commit()
            
            self.migration_stats['migrated_records'] = migrated_count
            logger.info(f"✅ 成功迁移 {migrated_count} 条失败批次记录")
            
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            logger.error(f"失败批次数据迁移失败: {e}")
            return False
    
    def _validate_migration_integrity(self) -> bool:
        """验证迁移数据完整性"""
        logger.info("🔍 验证迁移数据完整性...")
        
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor(dictionary=True)
            
            # 统计迁移后的数据
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN SCHEDULING_STATUS = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
                    SUM(CASE WHEN SCHEDULING_STATUS = 'FAILED' THEN 1 ELSE 0 END) as failed_count
                FROM lotprioritydone
            """)
            
            stats = cursor.fetchone()
            self.migration_stats['total_records_after'] = stats['total']
            
            # 验证数据一致性
            expected_total = self.migration_stats['success_records_before'] + self.migration_stats['migrated_records']
            actual_total = stats['total']
            
            if expected_total != actual_total:
                raise Exception(f"数据总量不匹配: 期望 {expected_total}, 实际 {actual_total}")
            
            if stats['failed_count'] != self.migration_stats['migrated_records']:
                raise Exception(f"失败批次数量不匹配: 期望 {self.migration_stats['migrated_records']}, 实际 {stats['failed_count']}")
            
            # 验证关键字段非空
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM lotprioritydone 
                WHERE LOT_ID IS NULL OR LOT_ID = ''
            """)
            
            if cursor.fetchone()['count'] > 0:
                raise Exception("发现LOT_ID为空的记录")
            
            logger.info("✅ 数据完整性验证通过")
            logger.info(f"📊 迁移后统计:")
            logger.info(f"   - 总记录数: {stats['total']}")
            logger.info(f"   - 成功批次: {stats['success_count']}")
            logger.info(f"   - 失败批次: {stats['failed_count']}")
            
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            logger.error(f"数据完整性验证失败: {e}")
            return False
    
    def _finalize_migration(self):
        """完成迁移后续工作"""
        logger.info("🎯 执行迁移后续工作...")
        
        try:
            # 记录迁移完成时间
            migration_record = {
                'migration_id': f"unified_scheduling_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'migration_type': 'unified_scheduling_architecture',
                'version': '1.0',
                'status': 'completed',
                'stats': self.migration_stats,
                'completion_time': datetime.now().isoformat()
            }
            
            # 保存迁移记录到文件
            with open(f"migration_record_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", 'w', encoding='utf-8') as f:
                json.dump(migration_record, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info("✅ 迁移记录已保存")
            
        except Exception as e:
            logger.warning(f"迁移后续工作部分失败，但不影响主流程: {e}")
    
    def _rollback_migration(self):
        """回滚迁移操作"""
        logger.info("🔄 执行迁移回滚...")
        
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 如果扩展了表结构，需要删除新添加的字段
            new_columns = ['SCHEDULING_STATUS', 'FAILURE_REASON', 'FAILURE_DETAILS', 'SESSION_ID']
            
            for column in new_columns:
                try:
                    cursor.execute(f"ALTER TABLE lotprioritydone DROP COLUMN {column}")
                    logger.info(f"✅ 删除字段: {column}")
                except Error as e:
                    if "doesn't exist" in str(e):
                        logger.info(f"⚠️ 字段 {column} 不存在，跳过删除")
                    else:
                        logger.warning(f"删除字段 {column} 失败: {e}")
            
            # 删除新添加的索引
            indexes = ['idx_scheduling_status', 'idx_session_id']
            for index in indexes:
                try:
                    cursor.execute(f"ALTER TABLE lotprioritydone DROP INDEX {index}")
                    logger.info(f"✅ 删除索引: {index}")
                except Error as e:
                    if "doesn't exist" in str(e):
                        logger.info(f"⚠️ 索引 {index} 不存在，跳过删除")
                    else:
                        logger.warning(f"删除索引 {index} 失败: {e}")
            
            # 如果有失败批次数据被插入，删除它们
            if self.migration_stats['migrated_records'] > 0:
                cursor.execute("DELETE FROM lotprioritydone WHERE PRIORITY = 999 AND HANDLER_ID IS NULL")
                deleted_count = cursor.rowcount
                logger.info(f"✅ 删除迁移的失败批次记录: {deleted_count} 条")
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info("✅ 迁移回滚完成")
            
        except Exception as e:
            logger.error(f"迁移回滚失败: {e}")
    
    def _print_migration_summary(self):
        """打印迁移总结"""
        duration = self.migration_stats['end_time'] - self.migration_stats['start_time']
        
        print("\n" + "="*80)
        print("📊 统一排产数据架构迁移完成报告")
        print("="*80)
        print(f"迁移状态: {self.migration_stats['status'].upper()}")
        print(f"开始时间: {self.migration_stats['start_time']}")
        print(f"结束时间: {self.migration_stats['end_time']}")
        print(f"耗时: {duration}")
        print()
        print("📈 数据统计:")
        print(f"  • 迁移前成功批次: {self.migration_stats['success_records_before']} 条")
        print(f"  • 迁移前失败批次: {self.migration_stats['failed_records_before']} 条")
        print(f"  • 成功迁移记录: {self.migration_stats['migrated_records']} 条")
        print(f"  • 迁移后总记录: {self.migration_stats['total_records_after']} 条")
        print()
        print("🎯 改进效果:")
        print(f"  • 可调整批次数量: {self.migration_stats['success_records_before']} → {self.migration_stats['total_records_after']} (+{self.migration_stats['migrated_records']})")
        print(f"  • 数据架构: 双表分离 → 单表统一")
        print(f"  • 失败批次救援: 不支持 → 支持")
        print("="*80)

def main():
    """主执行函数"""
    print("🚀 统一排产数据架构迁移工具")
    print("="*50)
    
    # 确认执行
    confirm = input("确认执行迁移？这将修改数据库结构和数据 (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 迁移已取消")
        return
    
    # 执行迁移
    migrator = UnifiedSchedulingMigration()
    success = migrator.execute_migration()
    
    if success:
        print("\n✅ 迁移成功完成！")
        print("📝 下一步：更新应用代码以使用新的统一数据模型")
    else:
        print("\n❌ 迁移失败，已执行回滚")
        print("📝 请检查日志文件获取详细错误信息")

if __name__ == "__main__":
    main() 