#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL配置助手 - 统一管理所有MySQL连接配置
解决硬编码localhost问题
"""

import os
import pymysql
from typing import Dict, Optional, Any

class MySQLConfigHelper:
    """MySQL配置助手类"""
    
    @staticmethod
    def get_mysql_config(database: str = 'aps') -> Dict[str, Any]:
        """
        获取MySQL连接配置
        
        Args:
            database: 数据库名称，默认为'aps'
            
        Returns:
            MySQL连接配置字典
        """
        # 优先使用环境变量，然后是配置文件，最后是默认值
        config = {
            'host': (
                os.environ.get('MYSQL_HOST') or 
                os.environ.get('DB_HOST') or 
                'host.docker.internal'  # Docker容器默认值
            ),
            'port': int(os.environ.get('MYSQL_PORT', os.environ.get('DB_PORT', 3306))),
            'user': os.environ.get('MYSQL_USER', os.environ.get('DB_USER', 'root')),
            'password': os.environ.get('MYSQL_PASSWORD', os.environ.get('DB_PASSWORD', 'WWWwww123!')),
            'database': database,
            'charset': os.environ.get('MYSQL_CHARSET', os.environ.get('DB_CHARSET', 'utf8mb4')),
            'connect_timeout': 10,
            'read_timeout': 30,
            'write_timeout': 30,
            'autocommit': True
        }
        
        return config
    
    @staticmethod
    def get_mysql_connection(database: str = 'aps', **kwargs) -> pymysql.Connection:
        """
        创建MySQL连接
        
        Args:
            database: 数据库名称
            **kwargs: 额外的连接参数
            
        Returns:
            pymysql.Connection对象
        """
        config = MySQLConfigHelper.get_mysql_config(database)
        config.update(kwargs)  # 允许覆盖配置
        
        try:
            connection = pymysql.connect(**config)
            return connection
        except Exception as e:
            # 如果连接失败，尝试不指定数据库连接（用于创建数据库）
            if 'Unknown database' in str(e) and database != '':
                config_no_db = config.copy()
                config_no_db.pop('database', None)
                return pymysql.connect(**config_no_db)
            raise e
    
    @staticmethod
    def get_sqlalchemy_uri(database: str = 'aps') -> str:
        """
        获取SQLAlchemy数据库URI
        
        Args:
            database: 数据库名称
            
        Returns:
            SQLAlchemy URI字符串
        """
        config = MySQLConfigHelper.get_mysql_config(database)
        
        uri = (f"mysql+pymysql://{config['user']}:{config['password']}"
               f"@{config['host']}:{config['port']}/{config['database']}"
               f"?charset={config['charset']}")
        
        return uri
    
    @staticmethod
    def test_connection(database: str = 'aps') -> tuple[bool, str]:
        """
        测试MySQL连接
        
        Args:
            database: 数据库名称
            
        Returns:
            (是否连接成功, 错误信息或成功信息)
        """
        try:
            config = MySQLConfigHelper.get_mysql_config(database)
            connection = pymysql.connect(**config)
            
            # 简单查询测试
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1 as test")
                result = cursor.fetchone()
                
            connection.close()
            
            host_info = f"{config['host']}:{config['port']}"
            return True, f"✅ MySQL连接成功 ({host_info}/{database})"
            
        except Exception as e:
            config = MySQLConfigHelper.get_mysql_config(database)
            host_info = f"{config['host']}:{config['port']}"
            return False, f"❌ MySQL连接失败 ({host_info}/{database}): {e}"
    
    @staticmethod
    def get_connection_info() -> Dict[str, str]:
        """
        获取连接信息摘要
        
        Returns:
            连接信息字典
        """
        config = MySQLConfigHelper.get_mysql_config()
        
        return {
            'host': config['host'],
            'port': str(config['port']),
            'user': config['user'],
            'charset': config['charset'],
            'status': 'configured'
        }

# 便捷函数
def get_mysql_connection(database: str = 'aps', **kwargs) -> pymysql.Connection:
    """便捷函数：获取MySQL连接"""
    return MySQLConfigHelper.get_mysql_connection(database, **kwargs)

def get_mysql_config(database: str = 'aps') -> Dict[str, Any]:
    """便捷函数：获取MySQL配置"""
    return MySQLConfigHelper.get_mysql_config(database)

def test_mysql_connection(database: str = 'aps') -> tuple[bool, str]:
    """便捷函数：测试MySQL连接"""
    return MySQLConfigHelper.test_connection(database) 