from contextlib import contextmanager
from typing import Optional, Dict, Any
import logging
from app import db
from sqlalchemy import text
import time
import threading

class EnhancedTransactionManager:
    """增强的事务管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._local = threading.local()
    
    @contextmanager
    def unified_scheduling_transaction(self, lock_id: str, timeout: int = 60):
        """统一的排产事务管理"""
        transaction_id = f"scheduling_tx_{lock_id}_{int(time.time())}"
        
        try:
            # 1. 获取数据库事务
            with db.session.begin():
                # 2. 设置事务超时
                db.session.execute(text(f"SET SESSION innodb_lock_wait_timeout = {timeout}"))
                
                # 3. 获取表级锁
                db.session.execute(text("LOCK TABLES lotprioritydone WRITE, scheduling_history WRITE"))
                
                self.logger.info(f"🔒 事务开始: {transaction_id}")
                
                # 4. 将事务ID存储在线程本地变量中
                self._local.transaction_id = transaction_id
                
                yield db.session
                
        except Exception as e:
            self.logger.error(f"❌ 事务失败: {transaction_id}, 错误: {e}")
            raise
        finally:
            try:
                # 5. 释放表级锁
                db.session.execute(text("UNLOCK TABLES"))
                self.logger.info(f"🔓 事务结束: {transaction_id}")
            except:
                pass
    
    def get_current_transaction_id(self) -> Optional[str]:
        """获取当前事务ID"""
        return getattr(self._local, 'transaction_id', None)
    
    @contextmanager
    def batch_transaction(self, batch_size: int = 1000, timeout: int = 30):
        """批量操作事务管理"""
        transaction_id = f"batch_tx_{int(time.time())}"
        
        try:
            with db.session.begin():
                # 设置批量操作优化参数
                db.session.execute(text("SET SESSION innodb_lock_wait_timeout = 30"))
                db.session.execute(text("SET SESSION autocommit = 0"))
                db.session.execute(text("SET SESSION unique_checks = 0"))
                db.session.execute(text("SET SESSION foreign_key_checks = 0"))
                
                self.logger.info(f"🔒 批量事务开始: {transaction_id}")
                
                self._local.transaction_id = transaction_id
                self._local.batch_size = batch_size
                
                yield db.session
                
        except Exception as e:
            self.logger.error(f"❌ 批量事务失败: {transaction_id}, 错误: {e}")
            raise
        finally:
            try:
                # 恢复默认设置
                db.session.execute(text("SET SESSION unique_checks = 1"))
                db.session.execute(text("SET SESSION foreign_key_checks = 1"))
                db.session.execute(text("SET SESSION autocommit = 1"))
                self.logger.info(f"🔓 批量事务结束: {transaction_id}")
            except:
                pass
    
    @contextmanager
    def read_only_transaction(self, timeout: int = 10):
        """只读事务管理"""
        transaction_id = f"readonly_tx_{int(time.time())}"
        
        try:
            with db.session.begin():
                # 设置只读模式
                db.session.execute(text("SET SESSION TRANSACTION READ ONLY"))
                db.session.execute(text(f"SET SESSION innodb_lock_wait_timeout = {timeout}"))
                
                self.logger.debug(f"🔒 只读事务开始: {transaction_id}")
                
                self._local.transaction_id = transaction_id
                
                yield db.session
                
        except Exception as e:
            self.logger.error(f"❌ 只读事务失败: {transaction_id}, 错误: {e}")
            raise
        finally:
            try:
                # 恢复读写模式
                db.session.execute(text("SET SESSION TRANSACTION READ WRITE"))
                self.logger.debug(f"🔓 只读事务结束: {transaction_id}")
            except:
                pass
    
    def create_backup_table(self, table_name: str, backup_suffix: str = None) -> str:
        """创建备份表"""
        if backup_suffix is None:
            backup_suffix = time.strftime('%Y%m%d_%H%M%S')
        
        backup_table = f"{table_name}_backup_{backup_suffix}"
        
        try:
            db.session.execute(text(f"""
                CREATE TABLE {backup_table} AS SELECT * FROM {table_name}
            """))
            self.logger.info(f"✅ 备份表创建成功: {backup_table}")
            return backup_table
        except Exception as e:
            self.logger.error(f"❌ 备份表创建失败: {e}")
            raise
    
    def drop_backup_table(self, backup_table: str) -> bool:
        """删除备份表"""
        try:
            db.session.execute(text(f"DROP TABLE IF EXISTS {backup_table}"))
            self.logger.info(f"✅ 备份表删除成功: {backup_table}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 备份表删除失败: {e}")
            return False
    
    def get_table_count(self, table_name: str) -> int:
        """获取表记录数"""
        try:
            result = db.session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            return result.scalar()
        except Exception as e:
            self.logger.error(f"❌ 获取表记录数失败: {e}")
            return 0
    
    def validate_table_integrity(self, table_name: str, expected_count: int = None) -> bool:
        """验证表完整性"""
        try:
            actual_count = self.get_table_count(table_name)
            
            if expected_count is not None:
                if actual_count != expected_count:
                    self.logger.warning(f"⚠️ 表记录数不匹配: {table_name}, 期望: {expected_count}, 实际: {actual_count}")
                    return False
            
            # 检查表结构
            result = db.session.execute(text(f"SHOW CREATE TABLE {table_name}"))
            if result.rowcount == 0:
                self.logger.error(f"❌ 表结构检查失败: {table_name}")
                return False
            
            self.logger.info(f"✅ 表完整性验证通过: {table_name}, 记录数: {actual_count}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 表完整性验证失败: {e}")
            return False
    
    def get_transaction_status(self) -> Dict[str, Any]:
        """获取事务状态"""
        try:
            # 获取当前事务状态
            result = db.session.execute(text("""
                SELECT 
                    @@autocommit as autocommit,
                    @@tx_isolation as isolation_level,
                    @@innodb_lock_wait_timeout as lock_timeout
            """))
            
            row = result.fetchone()
            
            return {
                'transaction_id': self.get_current_transaction_id(),
                'autocommit': bool(row[0]),
                'isolation_level': row[1],
                'lock_timeout': row[2],
                'active_transactions': self._get_active_transactions()
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取事务状态失败: {e}")
            return {}
    
    def _get_active_transactions(self) -> int:
        """获取活动事务数"""
        try:
            result = db.session.execute(text("""
                SELECT COUNT(*) FROM information_schema.innodb_trx
            """))
            return result.scalar()
        except:
            return 0
    
    def optimize_table(self, table_name: str) -> bool:
        """优化表"""
        try:
            db.session.execute(text(f"OPTIMIZE TABLE {table_name}"))
            self.logger.info(f"✅ 表优化完成: {table_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 表优化失败: {e}")
            return False
    
    def analyze_table(self, table_name: str) -> Dict[str, Any]:
        """分析表统计信息"""
        try:
            # 更新表统计信息
            db.session.execute(text(f"ANALYZE TABLE {table_name}"))
            
            # 获取表信息
            result = db.session.execute(text(f"""
                SELECT 
                    table_rows,
                    avg_row_length,
                    data_length,
                    index_length,
                    data_free
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = '{table_name}'
            """))
            
            row = result.fetchone()
            if row:
                return {
                    'table_rows': row[0],
                    'avg_row_length': row[1],
                    'data_length': row[2],
                    'index_length': row[3],
                    'data_free': row[4]
                }
            else:
                return {}
                
        except Exception as e:
            self.logger.error(f"❌ 表分析失败: {e}")
            return {}


# 全局实例
_transaction_manager = None

def get_transaction_manager() -> EnhancedTransactionManager:
    """获取事务管理器实例"""
    global _transaction_manager
    if _transaction_manager is None:
        _transaction_manager = EnhancedTransactionManager()
    return _transaction_manager 