#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查定时任务数据库状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import SchedulingTasks
from datetime import datetime
import json

def check_tasks():
    """检查定时任务数据库状态"""
    result = create_app()
    if isinstance(result, tuple):
        app, _ = result
    else:
        app = result

    with app.app_context():
        print("🔍 检查定时任务数据库状态...")
        
        try:
            # 检查表是否存在
            from sqlalchemy import text
            result = db.session.execute(text("SHOW TABLES LIKE 'scheduling_tasks'"))
            table_exists = result.fetchone() is not None
            
            if table_exists:
                print("✅ scheduling_tasks 表存在")
                
                # 查询现有任务
                tasks = SchedulingTasks.query.all()
                print(f"📋 当前数据库中有 {len(tasks)} 个定时任务")
                
                if len(tasks) == 0:
                    print("⚠️ 数据库中没有定时任务，这解释了为什么前端显示空列表")
                    
                    # 创建一个测试任务
                    print("\n🧪 创建测试定时任务...")
                    test_config = {
                        "name": "测试每日排产",
                        "type": "daily",
                        "hour": 9,
                        "minute": 0,
                        "strategy": "intelligent",
                        "target": "balanced",
                        "autoImport": True,
                        "emailNotification": False
                    }
                    
                    test_task = SchedulingTasks(
                        task_name="测试每日排产",
                        task_type="daily",
                        schedule_config=json.dumps(test_config),
                        enabled=True,
                        created_by="admin"
                    )
                    
                    db.session.add(test_task)
                    db.session.commit()
                    print(f"✅ 测试任务创建成功，ID: {test_task.id}")
                    
                    # 再次查询验证
                    tasks = SchedulingTasks.query.all()
                    print(f"📋 现在数据库中有 {len(tasks)} 个定时任务")
                    
                else:
                    for task in tasks:
                        print(f"  - {task.task_name} ({task.task_type}) - 启用: {task.enabled}")
                        print(f"    配置: {task.schedule_config}")
                
            else:
                print("❌ scheduling_tasks 表不存在")
                
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_tasks()
