/*!
 * 统一批次管理 JavaScript 模块
 * 基于现有semi_auto.html扩展，提供跨表统一批次管理功能
 */

class UnifiedLotManager {
    constructor() {
        this.currentStatus = 'all';
        this.selectedLots = new Set();
        this.currentPage = 1;
        this.pageSize = 50;
        this.searchText = '';
        this.statusCounts = {};
        this.isUnifiedMode = false;
        
        this.init();
    }
    
    init() {
        // 等待页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeUI());
        } else {
            this.initializeUI();
        }
    }
    
    initializeUI() {
        // 检查是否在semi_auto.html页面
        if (!document.getElementById('scheduleResultSection')) {
            console.log('不在排产页面，跳过统一批次管理初始化');
            return;
        }
        
        // 在现有页面上添加统一管理功能
        this.addUnifiedManagementPanel();
        this.bindEvents();
        
        console.log('✅ 统一批次管理已初始化');
    }
    
    addUnifiedManagementPanel() {
        // 1. 首先在排产结果区域添加切换模式按钮
        const scheduleResultSection = document.getElementById('scheduleResultSection');
        if (!scheduleResultSection) return;
        
        // 在排产结果区域的头部添加切换模式按钮
        const toggleButton = document.createElement('div');
        toggleButton.innerHTML = `
            <div class="d-flex justify-content-end mb-3">
                <button id="unifiedModeToggle" class="btn btn-outline-primary btn-sm" onclick="unifiedLotManager.toggleMode()">
                    <i class="fas fa-layer-group me-1"></i>统一批次管理
                </button>
            </div>
        `;
        scheduleResultSection.insertBefore(toggleButton, scheduleResultSection.firstChild);
        
        // 2. 然后添加统一管理面板（隐藏状态）
        const unifiedPanel = document.createElement('div');
        unifiedPanel.innerHTML = `
            <div class="card mt-3" id="unifiedLotManagementPanel" style="display: none;">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-layer-group me-1"></i>统一批次管理
                            <small class="text-muted ms-2">等待批次 + 已处理批次</small>
                        </h6>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-secondary" onclick="unifiedLotManager.toggleMode()">
                                <i class="fas fa-arrow-left me-1"></i>返回原始模式
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="unifiedLotManager.refreshUnifiedData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新数据
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 状态筛选标签 -->
                    <div class="status-filter-tabs mb-3">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm status-tab active" data-status="all">
                                全部批次 <span class="badge bg-secondary" id="countAll">0</span>
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm status-tab" data-status="WAITING">
                                等待排产 <span class="badge bg-info" id="countWAITING">0</span>
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm status-tab" data-status="SUCCESS">
                                排产成功 <span class="badge bg-success" id="countSUCCESS">0</span>
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm status-tab" data-status="FAILED">
                                排产失败 <span class="badge bg-danger" id="countFAILED">0</span>
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm status-tab" data-status="MANUAL_ADJUSTED">
                                手动调整 <span class="badge bg-warning" id="countMANUAL_ADJUSTED">0</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 搜索和批量操作 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="unifiedSearchInput" placeholder="搜索批次号、产品名称、设备...">
                                <button class="btn btn-outline-secondary" onclick="unifiedLotManager.searchLots()">搜索</button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-1">
                                <button class="btn btn-sm btn-primary" onclick="unifiedLotManager.showBatchPriorityDialog()">
                                    <i class="fas fa-sort me-1"></i>批量优先级调整
                                </button>
                                <button class="btn btn-sm btn-success" onclick="unifiedLotManager.rescueSelectedFailedLots()">
                                    <i class="fas fa-life-ring me-1"></i>救援失败批次
                                </button>
                                <button class="btn btn-sm btn-info" onclick="unifiedLotManager.exportUnifiedData()">
                                    <i class="fas fa-file-excel me-1"></i>导出数据
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 统一批次表格 -->
                    <div class="table-responsive">
                        <table class="table table-sm table-hover table-striped" id="unifiedLotTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="50px">
                                        <input type="checkbox" id="selectAllUnified" onchange="unifiedLotManager.toggleSelectAll()">
                                    </th>
                                    <th width="80px">状态</th>
                                    <th width="60px">优先级</th>
                                    <th width="100px">批次号</th>
                                    <th width="80px">产品</th>
                                    <th width="60px">工序</th>
                                    <th width="60px">数量</th>
                                    <th width="80px">分选机</th>
                                    <th width="80px">综合评分</th>
                                    <th width="80px">匹配类型</th>
                                    <th width="100px">失败原因</th>
                                    <th width="100px">更新时间</th>
                                    <th width="100px">操作</th>
                                </tr>
                            </thead>
                            <tbody id="unifiedLotTableBody">
                                <!-- 动态内容 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页控件 -->
                    <nav class="mt-3">
                        <ul class="pagination pagination-sm justify-content-center" id="unifiedPagination"></ul>
                    </nav>
                </div>
            </div>
        `;
        
        // 在排产结果区域后插入
        scheduleResultSection.parentNode.insertBefore(unifiedPanel, scheduleResultSection.nextSibling);
    }
    
    bindEvents() {
        // 状态筛选标签点击事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('status-tab')) {
                const status = e.target.dataset.status;
                this.filterByStatus(status);
            }
        });
        
        // 搜索框回车事件
        const searchInput = document.getElementById('unifiedSearchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchLots();
                }
            });
        }
    }
    
    toggleMode() {
        this.isUnifiedMode = !this.isUnifiedMode;
        const panel = document.getElementById('unifiedLotManagementPanel');
        const scheduleSection = document.getElementById('scheduleResultSection');
        const toggleButton = document.getElementById('unifiedModeToggle');
        
        if (this.isUnifiedMode) {
            // 切换到统一模式
            panel.style.display = 'block';
            scheduleSection.style.display = 'none';
            toggleButton.innerHTML = '<i class="fas fa-arrow-left me-1"></i>返回原始模式';
            toggleButton.className = 'btn btn-outline-secondary btn-sm';
            this.loadUnifiedData();
        } else {
            // 切换回原始模式
            panel.style.display = 'none';
            scheduleSection.style.display = 'block';
            toggleButton.innerHTML = '<i class="fas fa-layer-group me-1"></i>统一批次管理';
            toggleButton.className = 'btn btn-outline-primary btn-sm';
        }
    }
    
    async loadUnifiedData() {
        try {
            console.log('🔍 开始加载统一数据...');
            console.log('📊 当前状态:', this.currentStatus);
            console.log('🔍 搜索文本:', this.searchText);
            console.log('📄 当前页:', this.currentPage);
            
            // 1. 获取批次数据
            const lotsUrl = `/api/v2/production/unified-lot-management/lots?status=${this.currentStatus}&search=${encodeURIComponent(this.searchText)}&page=${this.currentPage}&page_size=${this.pageSize}`;
            console.log('🌐 批次数据URL:', lotsUrl);
            
            const lotsResponse = await fetch(lotsUrl);
            const lotsData = await lotsResponse.json();
            console.log('📊 批次数据响应:', lotsData);
            
            // 2. 获取状态计数
            const countsUrl = `/api/v2/production/unified-lot-management/status-counts`;
            console.log('🌐 状态计数URL:', countsUrl);
            
            const countsResponse = await fetch(countsUrl);
            const countsData = await countsResponse.json();
            console.log('📊 状态计数响应:', countsData);
            
            if (lotsData.success && countsData.success) {
                console.log('✅ 数据加载成功');
                console.log('📊 批次数据长度:', lotsData.data.length);
                console.log('📊 状态计数:', countsData.data);
                
                // 正确使用API返回的数据结构
                this.renderUnifiedTable(lotsData.data);
                this.updateStatusCounts(countsData.data);
                this.updatePagination(
                    lotsData.pagination.total, 
                    lotsData.pagination.page, 
                    lotsData.pagination.page_size
                );
            } else {
                console.error('❌ 加载统一数据失败:', lotsData.error || countsData.error);
                showNotification('加载数据失败', lotsData.error || countsData.error, 'error');
            }
        } catch (error) {
            console.error('❌ 加载统一数据异常:', error);
            showNotification('加载数据异常', error.message, 'error');
        }
    }
    
    renderUnifiedTable(lots) {
        const tbody = document.getElementById('unifiedLotTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = lots.map(lot => `
            <tr data-lot-id="${lot.LOT_ID}" data-status="${lot.status}">
                <td><input type="checkbox" class="lot-checkbox" value="${lot.LOT_ID}" onchange="unifiedLotManager.updateSelection()"></td>
                <td>${this.getStatusBadge(lot.status)}</td>
                <td>
                    <input type="number" class="form-control form-control-sm priority-input" 
                           value="${lot.PRIORITY || 999}" 
                           onchange="unifiedLotManager.updateLotPriority('${lot.LOT_ID}', this.value, '${lot.status}')"
                           style="width: 70px;">
                </td>
                <td><span class="text-truncate" title="${lot.LOT_ID}">${lot.LOT_ID}</span></td>
                <td><span class="text-truncate" title="${lot.DEVICE}">${lot.DEVICE || '-'}</span></td>
                <td>${lot.STAGE || '-'}</td>
                <td>${lot.GOOD_QTY || 0}</td>
                <td>${lot.HANDLER_ID || '-'}</td>
                <td>${lot.comprehensive_score ? parseFloat(lot.comprehensive_score).toFixed(1) : '-'}</td>
                <td>${lot.match_type || '-'}</td>
                <td><span class="text-truncate text-danger" title="${lot.FAILURE_REASON}">${lot.FAILURE_REASON || '-'}</span></td>
                <td>${lot.updated_at ? new Date(lot.updated_at).toLocaleString() : '-'}</td>
                <td>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            操作
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" onclick="unifiedLotManager.showLotDetails('${lot.LOT_ID}')">
                                <i class="fas fa-eye me-1"></i>查看详情
                            </a></li>
                            ${this.getStatusActions(lot.status, lot.LOT_ID)}
                        </ul>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    getStatusBadge(status) {
        const badges = {
            'WAITING': '<span class="badge bg-info">等待排产</span>',
            'SUCCESS': '<span class="badge bg-success">排产成功</span>',
            'FAILED': '<span class="badge bg-danger">排产失败</span>',
            'MANUAL_ADJUSTED': '<span class="badge bg-warning">手动调整</span>'
        };
        return badges[status] || '<span class="badge bg-secondary">未知</span>';
    }
    
    getStatusActions(status, lotId) {
        const actions = [];
        
        if (status === 'WAITING') {
            actions.push(`<li><a class="dropdown-item" onclick="unifiedLotManager.forceScheduleLot('${lotId}')">
                <i class="fas fa-play me-1"></i>强制排产
            </a></li>`);
        }
        
        if (status === 'FAILED') {
            actions.push(`<li><a class="dropdown-item" onclick="unifiedLotManager.rescueFailedLot('${lotId}')">
                <i class="fas fa-life-ring me-1"></i>救援批次
            </a></li>`);
        }
        
        if (status === 'SUCCESS') {
            actions.push(`<li><a class="dropdown-item" onclick="unifiedLotManager.manualAdjustLot('${lotId}')">
                <i class="fas fa-edit me-1"></i>手动调整
            </a></li>`);
        }
        
        return actions.join('');
    }
    
    updateStatusCounts(statusCounts) {
        this.statusCounts = statusCounts;
        console.log('📊 更新状态计数:', statusCounts);
        
        // 更新状态标签的数量
        // 'all' 状态的计数是 total 字段
        const countAll = document.getElementById('countAll');
        if (countAll) {
            countAll.textContent = statusCounts.total || 0;
        }
        
        // 更新其他状态的计数
        ['WAITING', 'SUCCESS', 'FAILED', 'MANUAL_ADJUSTED'].forEach(status => {
            const countElement = document.getElementById(`count${status}`);
            if (countElement) {
                countElement.textContent = statusCounts[status] || 0;
            }
        });
    }
    
    filterByStatus(status) {
        this.currentStatus = status;
        this.currentPage = 1;
        
        // 更新激活状态
        document.querySelectorAll('.status-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.status === status);
        });
        
        this.loadUnifiedData();
    }
    
    searchLots() {
        const searchInput = document.getElementById('unifiedSearchInput');
        this.searchText = searchInput ? searchInput.value.trim() : '';
        this.currentPage = 1;
        this.loadUnifiedData();
    }
    
    async updateLotPriority(lotId, priority, status) {
        try {
            const response = await fetch('/api/v2/production/unified-lot-management/priority/batch-update', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    updates: [{
                        lot_id: lotId,
                        priority: parseInt(priority),
                        status: status
                    }]
                })
            });
            
            const result = await response.json();
            if (result.success) {
                showNotification('更新成功', `批次 ${lotId} 优先级已更新为 ${priority}`, 'success');
            } else {
                showNotification('更新失败', result.error, 'error');
            }
        } catch (error) {
            console.error('更新优先级失败:', error);
            showNotification('更新失败', error.message, 'error');
        }
    }
    
    showBatchPriorityDialog() {
        const selectedLots = Array.from(this.selectedLots);
        if (selectedLots.length === 0) {
            alert('请选择要操作的批次');
            return;
        }
        
        const priority = prompt(`请输入新的优先级（1-999）\n将更新 ${selectedLots.length} 个批次的优先级：`, '1');
        if (priority !== null && !isNaN(priority) && priority > 0) {
            this.batchUpdatePriority(selectedLots, parseInt(priority));
        }
    }
    
    async batchUpdatePriority(lotIds, priority) {
        try {
            const updates = lotIds.map(lotId => {
                const row = document.querySelector(`tr[data-lot-id="${lotId}"]`);
                const status = row ? row.dataset.status : 'WAITING';
                return { lot_id: lotId, priority: priority, status: status };
            });
            
            const response = await fetch('/api/v2/production/unified-lot-management/priority/batch-update', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ updates })
            });
            
            const result = await response.json();
            if (result.success) {
                showNotification('批量更新成功', `成功更新 ${result.updated_count} 个批次的优先级`, 'success');
                this.loadUnifiedData();
            } else {
                showNotification('批量更新失败', result.error, 'error');
            }
        } catch (error) {
            console.error('批量更新优先级失败:', error);
            showNotification('批量更新失败', error.message, 'error');
        }
    }
    
    async rescueSelectedFailedLots() {
        const selectedLots = Array.from(this.selectedLots).filter(lotId => {
            const row = document.querySelector(`tr[data-lot-id="${lotId}"]`);
            return row && row.dataset.status === 'FAILED';
        });
        
        if (selectedLots.length === 0) {
            alert('请选择失败的批次进行救援');
            return;
        }
        
        if (confirm(`确定要救援 ${selectedLots.length} 个失败批次吗？\n救援后将重新进入等待排产队列。`)) {
            await this.rescueFailedLots(selectedLots);
        }
    }
    
    async rescueFailedLots(lotIds) {
        try {
            const response = await fetch('/api/v2/production/unified-lot-management/rescue-failed', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ lot_ids: lotIds })
            });
            
            const result = await response.json();
            if (result.success) {
                showNotification('救援成功', `成功救援 ${result.rescued_count} 个失败批次`, 'success');
                this.loadUnifiedData();
            } else {
                showNotification('救援失败', result.error, 'error');
            }
        } catch (error) {
            console.error('救援失败批次失败:', error);
            showNotification('救援失败', error.message, 'error');
        }
    }
    
    updateSelection() {
        const checkboxes = document.querySelectorAll('.lot-checkbox');
        this.selectedLots.clear();
        
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                this.selectedLots.add(checkbox.value);
            }
        });
        
        // 更新全选状态
        const selectAllCheckbox = document.getElementById('selectAllUnified');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkboxes.length > 0 && this.selectedLots.size === checkboxes.length;
        }
    }
    
    toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllUnified');
        const checkboxes = document.querySelectorAll('.lot-checkbox');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
        
        this.updateSelection();
    }
    
    async refreshUnifiedData() {
        await this.loadUnifiedData();
        showNotification('刷新成功', '统一批次数据已刷新', 'info');
    }
    
    // 其他功能方法...
    showLotDetails(lotId) {
        console.log('显示批次详情:', lotId);
        // 实现批次详情显示
    }
    
    forceScheduleLot(lotId) {
        console.log('强制排产:', lotId);
        // 实现强制排产逻辑
    }
    
    rescueFailedLot(lotId) {
        this.rescueFailedLots([lotId]);
    }
    
    manualAdjustLot(lotId) {
        console.log('手动调整:', lotId);
        // 实现手动调整逻辑
    }
    
    exportUnifiedData() {
        console.log('导出统一数据');
        // 实现数据导出逻辑
    }
    
    updatePagination(total, page, pageSize) {
        const totalPages = Math.ceil(total / pageSize);
        const paginationElement = document.getElementById('unifiedPagination');
        
        if (!paginationElement || totalPages <= 1) return;
        
        let pagination = '';
        
        // 上一页
        if (page > 1) {
            pagination += `<li class="page-item"><a class="page-link" onclick="unifiedLotManager.goToPage(${page - 1})">上一页</a></li>`;
        }
        
        // 页码
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(totalPages, page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const active = i === page ? 'active' : '';
            pagination += `<li class="page-item ${active}"><a class="page-link" onclick="unifiedLotManager.goToPage(${i})">${i}</a></li>`;
        }
        
        // 下一页
        if (page < totalPages) {
            pagination += `<li class="page-item"><a class="page-link" onclick="unifiedLotManager.goToPage(${page + 1})">下一页</a></li>`;
        }
        
        paginationElement.innerHTML = pagination;
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.loadUnifiedData();
    }
}

// 全局实例
window.unifiedLotManager = new UnifiedLotManager();

// 兼容现有代码的函数
function showNotification(title, message, type = 'info') {
    // 防止递归调用，直接使用console.log
    console.log(`[${type.toUpperCase()}] ${title}: ${message}`);
    
    // 尝试调用页面的Toast通知函数
    if (typeof window.showToast === 'function') {
        window.showToast(title, message, type);
    } else if (typeof window.Toast === 'function') {
        window.Toast(title, message, type);
    }
} 