from flask import Blueprint
from app.utils.api_config import get_api_prefix

# 创建生产管理API蓝图 - 使用配置化的URL前缀
production_bp = Blueprint('production_api_v2', __name__, url_prefix=get_api_prefix() + '/production')

# 导出bp变量供外部使用
bp = production_bp

# 导入路由
from . import routes
from . import order_processing

# 导入并注册优先级配置蓝图
from .priority_api import priority_bp 

# 导入并注册批次管理API蓝图
from .wait_lots_api import wait_lots_bp
from .done_lots_api import done_lots_bp

# 导入并注册统一批次管理API蓝图
from .unified_lot_management_api import unified_lot_bp
production_bp.register_blueprint(unified_lot_bp)

# 导入并注册优化解析器API蓝图 - 已移动到orders模块
# from .optimized_parser_api import bp as optimized_parser_bp

# 注册缺失的生产API（已在主应用中注册，避免重复注册）
# from .missing_apis import missing_production_bp
# production_bp.register_blueprint(missing_production_bp, url_prefix='/api/production')
