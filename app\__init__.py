from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_socketio import Socket<PERSON>
from config import Config
import os
from flask_cors import CORS
from flask_migrate import Migrate
from dotenv import load_dotenv

# 在应用启动的早期加载.env文件
load_dotenv()

# 🔧 应用MySQL猴子补丁修复硬编码localhost问题
try:
    from app.utils.mysql_monkey_patch import apply_mysql_monkey_patch, get_current_mysql_config
    apply_mysql_monkey_patch()
    config_info = get_current_mysql_config()
    print(f"✅ MySQL猴子补丁已启用: {config_info['host']}:{config_info['port']}")
except Exception as e:
    print(f"⚠️ MySQL猴子补丁启用失败: {e}")

db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = None

# 创建全局SocketIO实例
socketio = SocketIO()

# 创建全局调度器变量
scheduler = None

# 全局Flask应用实例变量
app = None

def create_app(config_class=Config):
    global app
    app = Flask(__name__)
    
    # 启用CORS支持
    CORS(app, supports_credentials=True)
    
    # 确保实例文件夹存在
    try:
        os.makedirs(app.instance_path, exist_ok=True)
    except OSError:
        pass
    
    # 从环境变量获取数据库URI
    if 'DATABASE_URL' in os.environ:
        app.config['SQLALCHEMY_DATABASE_URI'] = os.environ['DATABASE_URL']
    
    # 加载配置
    app.config.from_object(config_class)
    
    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    
    # 🔧 修复：初始化性能监控器
    try:
        from app.utils.performance_monitor import PerformanceMonitor
        performance_monitor = PerformanceMonitor(app)
        app.logger.info('✅ 性能监控器初始化成功')
    except ImportError as e:
        app.logger.warning(f'⚠️ 性能监控器初始化失败: {e}')
    
    # 初始化SocketIO - 兼容PyInstaller打包环境
    import sys
    if getattr(sys, 'frozen', False):
        # PyInstaller环境，禁用SocketIO避免兼容性问题
        app.config['SOCKETIO_ENABLED'] = False
        app.logger.info('🔧 PyInstaller环境：SocketIO已禁用')
    else:
        # 开发环境，正常启用SocketIO
        socketio.init_app(app, cors_allowed_origins="*", logger=True, engineio_logger=True)
        app.config['SOCKETIO_ENABLED'] = True
    
    # 确保所有数据库表都存在
    with app.app_context():
        if app.config.get('ENV') == 'development':
            from sqlalchemy import inspect
            
            # 检查主数据库
            inspector = inspect(db.engine)
            if not inspector.get_table_names():
                app.logger.info('主数据库表不存在，正在创建...')
                db.create_all()
            else:
                app.logger.info('主数据库表已存在，跳过创建')
            
            # 检查绑定数据库
            for bind_key in db.get_binds():
                bind_engine = db.get_engine(bind=bind_key)
                bind_inspector = inspect(bind_engine)
                if not bind_inspector.get_table_names():
                    app.logger.info(f'绑定数据库 {bind_key} 表不存在，正在创建...')
                    db.create_all(bind=bind_key)
                else:
                    app.logger.info(f'绑定数据库 {bind_key} 表已存在，跳过创建')
    
    # 注册用户加载函数
    from app.models import User
    
    @login_manager.user_loader
    def load_user(username):
        return User.query.get(username)
    
    # 添加版本号到上下文
    @app.context_processor
    def inject_version():
        return {'app_version': app.config['APP_VERSION']}
    
    # 注册上下文处理器
    try:
        from app.context_processors import register_context_processors
        register_context_processors(app)
        app.logger.info('✅ 上下文处理器注册成功')
    except Exception as e:
        app.logger.warning(f'⚠️ 上下文处理器注册失败: {e}')
    
    # 注册核心蓝图
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    # 🔥 优先注册手动排产API蓝图（避免路由冲突）
    try:
        from app.api_v2.production.manual_scheduling_api import manual_scheduling_api
        app.register_blueprint(manual_scheduling_api)
        app.logger.info('✅ 手动排产API蓝图优先注册成功')
    except ImportError as e:
        app.logger.error(f'❌ 手动排产API蓝图注册失败: {e}')
    
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # 注册邮件附件处理蓝图
    from app.api.email_attachment import email_attachment_bp
    app.register_blueprint(email_attachment_bp)
    
    # 注册订单处理API蓝图 (已禁用，使用V2版本)
    # from app.api.order_processing_api import order_processing_bp
    # app.register_blueprint(order_processing_bp)
    
    # 注册生产管理蓝图
    from app.routes.production_views import production_views_bp
    app.register_blueprint(production_views_bp)
    app.logger.info('✅ 生产管理视图蓝图注册成功')
    
    # 注册订单管理蓝图
    from app.routes.orders_routes import bp as orders_bp
    app.register_blueprint(orders_bp)
    
    # 注册系统管理蓝图
    from app.routes.system import system_bp
    app.register_blueprint(system_bp)

    # 注册管理工具蓝图
    try:
        from app.routes.admin_tools import admin_tools_bp
        app.register_blueprint(admin_tools_bp)
        app.logger.info('✅ 管理工具蓝图注册成功')
    except ImportError as e:
        app.logger.warning(f'⚠️ 管理工具蓝图注册失败: {e}')
    
    # 注册API v2蓝图
    try:
        # 注册订单API v2蓝图
        from app.api_v2.orders import orders_bp as orders_v2_bp
        app.register_blueprint(orders_v2_bp)
        app.logger.info('✅ 订单API v2蓝图注册成功')
        
        # 注册其他API v2蓝图
        from app.api_v2.orders.summary_preview_api import bp as summary_preview_api_bp
        app.register_blueprint(summary_preview_api_bp, url_prefix='/api/v2/orders')
        app.logger.info('✅ 订单预览API蓝图注册成功')
        
        from app.api_v2.orders.high_concurrency_api import high_concurrency_bp
        app.register_blueprint(high_concurrency_bp)
        app.logger.info('✅ 高并发API蓝图注册成功')
        
        from app.api_v2.production.routes import production_bp as production_api_v2_bp
        app.register_blueprint(production_api_v2_bp)
        app.logger.info('✅ 生产API v2蓝图注册成功')
        
        # 单独注册done_lots_api蓝图
        from app.api_v2.production.done_lots_api import done_lots_bp
        app.register_blueprint(done_lots_bp)
        app.logger.info('✅ 已排产批次API蓝图注册成功')
        
        # 单独注册failed_lots_api蓝图
        from app.api_v2.production.failed_lots_api import failed_lots_bp
        app.register_blueprint(failed_lots_bp)
        app.logger.info('✅ 排产失败批次API蓝图注册成功')
        
        # 注册手动调整API蓝图 - Phase 2.3
        from app.api_v2.production.manual_adjustment_api import manual_adjustment_api
        app.register_blueprint(manual_adjustment_api)
        app.logger.info('✅ 手动调整API蓝图注册成功 - Phase 2.3')
        
        from app.api_v2.resources.routes import resources_bp as resources_api_v2_bp
        app.register_blueprint(resources_api_v2_bp)
        app.logger.info('✅ 资源API v2蓝图注册成功')
        
        from app.api_v2.system import system_bp as system_api_v2_bp
        app.register_blueprint(system_api_v2_bp)
        app.logger.info('✅ 系统API v2蓝图注册成功')
        
        # 注册多级缓存API蓝图 - Task 2.2
        from app.api_v2.system.multilevel_cache_api import multilevel_cache_bp
        app.register_blueprint(multilevel_cache_bp, url_prefix='/api/v2/system')
        app.logger.info('✅ 多级缓存API蓝图注册成功 - Task 2.2')
        
        # 注册并行计算API蓝图 - Task 2.3
        try:
            from app.api_v2.system.parallel_computing_api import parallel_computing_bp
            app.register_blueprint(parallel_computing_bp, url_prefix='/api/v2/system/parallel-computing')
            app.logger.info('✅ 并行计算API蓝图注册成功 - Task 2.3')
        except Exception as e:
            app.logger.warning(f'⚠️ 并行计算API注册失败: {e}')
        
        # 注册认证API v2蓝图
        from app.api_v2.auth import auth_bp as auth_api_v2_bp
        app.register_blueprint(auth_api_v2_bp)
        app.logger.info('✅ 认证API v2蓝图注册成功')
        
        
        # 注册WIP批次管理API蓝图
        from app.api_v2.wip_lot_api import wip_lot_api_bp
        app.register_blueprint(wip_lot_api_bp)
        app.logger.info('✅ WIP批次管理API蓝图注册成功')
        
        app.logger.info('✅ 所有API v2蓝图注册完成')
        
    except ImportError as e:
        app.logger.error(f'❌ API v2蓝图注册失败: {e}')
        import traceback
        app.logger.error(traceback.format_exc())
    
    # 注册API v3蓝图 - 动态字段管理器 (迁移测试版本)
    try:
        from app.api.routes_v3 import api_v3 as api_v3_bp
        app.register_blueprint(api_v3_bp)
        app.logger.info('✅ API v3蓝图注册成功 - 动态字段管理器已激活')
    except ImportError as e:
        app.logger.warning(f'⚠️ API v3蓝图注册失败: {e}')
        app.logger.info('   -> API v3处于开发阶段，当前仍使用API v2')
    
    # 配置精简日志系统
    try:
        from app.utils.simple_logging import setup_logging
        setup_logging(app)
        app.logger.info('🚀 APS应用启动 - v2.1 (Flask + MySQL)')
    except Exception as e:
        import logging
        app.logger.setLevel(logging.INFO)
        app.logger.warning(f'⚠️ 日志系统配置失败: {e}')
        app.logger.info('应用启动')
    
    # 初始化和启动统一调度器服务
    with app.app_context():
        try:
            from app.services.scheduler_service import scheduler_service
            from app.models import SchedulerJob, SchedulerJobLog, SchedulerConfig
            
            # 确保调度器模型表已导入（用于自动创建表）
            scheduler_service.init_app(app)
            
            # 启动APScheduler
            if scheduler_service.start():
                app.logger.info('✅ APScheduler统一调度器启动成功')
                
                # 为了向后兼容，设置全局scheduler变量指向APScheduler服务
                import sys
                current_module = sys.modules[__name__]
                current_module.scheduler = scheduler_service
                
            else:
                app.logger.warning('⚠️ APScheduler统一调度器启动失败或被禁用')
                
        except Exception as e:
            app.logger.error(f'❌ APScheduler统一调度器启动失败: {e}')
            import traceback
            app.logger.error(traceback.format_exc())
    
    # 初始化后端定时任务服务 (替代前端定时任务)
    try:
        from app.services.background_scheduler_service import background_scheduler
        background_scheduler.init_app(app)
        
        # 延迟启动后端定时任务服务
        import threading
        def delayed_start():
            import time
            time.sleep(2)  # 等待应用完全初始化
            with app.app_context():
                background_scheduler.start()
                app.logger.info('✅ 后端定时任务服务启动成功 - 已替代前端定时任务')
        
        threading.Thread(target=delayed_start, daemon=True).start()
        
    except Exception as e:
        app.logger.error(f'❌ 后端定时任务服务初始化失败: {e}')
        import traceback
        app.logger.error(traceback.format_exc())
    
    # 初始化邮箱定时任务服务
    try:
        from app.services.email_scheduler_service import init_email_scheduler_service
        
        # 延迟启动邮箱定时任务服务
        import threading
        def delayed_email_scheduler_start():
            import time
            time.sleep(3)  # 等待应用完全初始化
            with app.app_context():
                init_email_scheduler_service()
                app.logger.info('✅ 邮箱定时任务服务启动成功')
        
        threading.Thread(target=delayed_email_scheduler_start, daemon=True).start()
        
    except Exception as e:
        app.logger.error(f'❌ 邮箱定时任务服务初始化失败: {e}')
        import traceback
        app.logger.error(traceback.format_exc())

    return app, socketio

# 导入模型
from app import models

# 全局调度器变量（在应用创建时设置）
scheduler = None 