#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查et_wait_lot表结构
分析LOT_ID字段类型以便正确创建外键约束
"""

import logging
import mysql.connector
from mysql.connector import Error
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_mysql_config():
    """获取MySQL数据库配置"""
    return {
        'host': os.environ.get('DB_HOST', 'localhost'),
        'port': int(os.environ.get('DB_PORT', 3306)),
        'user': os.environ.get('DB_USER', 'root'),
        'password': os.environ.get('DB_PASSWORD', 'WWWwww123!'),
        'database': os.environ.get('DB_NAME', 'aps'),
        'charset': os.environ.get('DB_CHARSET', 'utf8mb4')
    }

def check_et_wait_lot_structure():
    """检查et_wait_lot表结构"""
    try:
        config = get_mysql_config()
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        logger.info("🔍 检查et_wait_lot表结构...")
        
        # 检查表是否存在
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'et_wait_lot'
        """)
        
        if cursor.fetchone()[0] == 0:
            logger.error("❌ et_wait_lot表不存在")
            return False
        
        # 获取表结构详细信息
        cursor.execute("""
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                CHARACTER_MAXIMUM_LENGTH,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                COLUMN_KEY,
                EXTRA,
                COLUMN_COMMENT
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'et_wait_lot'
            ORDER BY ORDINAL_POSITION
        """)
        
        columns = cursor.fetchall()
        logger.info("📋 et_wait_lot表完整结构:")
        
        lot_id_info = None
        for col in columns:
            column_name, data_type, max_length, is_nullable, default_val, column_key, extra, comment = col
            
            # 特别关注LOT_ID字段
            if column_name == 'LOT_ID':
                lot_id_info = col
                logger.info(f"🎯 LOT_ID字段: {data_type}({max_length}) {'NULL' if is_nullable == 'YES' else 'NOT NULL'} {column_key} {extra}")
            
            logger.info(f"   - {column_name}: {data_type}({max_length if max_length else ''}) {'NULL' if is_nullable == 'YES' else 'NOT NULL'} DEFAULT {default_val} {column_key} {extra} - {comment}")
        
        # 检查LOT_ID字段的索引信息
        cursor.execute("""
            SELECT INDEX_NAME, COLUMN_NAME, NON_UNIQUE, INDEX_TYPE
            FROM information_schema.statistics 
            WHERE table_schema = DATABASE() 
            AND table_name = 'et_wait_lot'
            AND column_name = 'LOT_ID'
            ORDER BY INDEX_NAME
        """)
        
        lot_id_indexes = cursor.fetchall()
        if lot_id_indexes:
            logger.info("📋 LOT_ID字段索引:")
            for idx in lot_id_indexes:
                unique_str = "UNIQUE" if idx[2] == 0 else "INDEX"
                logger.info(f"   - {unique_str} {idx[0]} ({idx[1]}) TYPE: {idx[3]}")
        
        # 生成建议的扩展表字段定义
        if lot_id_info:
            data_type, max_length = lot_id_info[1], lot_id_info[2]
            is_nullable = lot_id_info[3]
            
            if data_type.upper() == 'VARCHAR' and max_length:
                suggested_field = f"lot_id VARCHAR({max_length}) NOT NULL"
            elif data_type.upper() == 'CHAR' and max_length:
                suggested_field = f"lot_id CHAR({max_length}) NOT NULL"
            elif data_type.upper() in ['INT', 'INTEGER']:
                suggested_field = f"lot_id INT NOT NULL"
            elif data_type.upper() == 'BIGINT':
                suggested_field = f"lot_id BIGINT NOT NULL"
            else:
                suggested_field = f"lot_id {data_type.upper()}({max_length if max_length else ''}) NOT NULL"
            
            logger.info(f"💡 建议的扩展表字段定义: {suggested_field}")
            return suggested_field
        
        return None
        
    except Error as e:
        logger.error(f"❌ 检查表结构失败: {e}")
        return None
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def check_sample_data():
    """检查et_wait_lot表的样本数据"""
    try:
        config = get_mysql_config()
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        logger.info("🔍 检查样本数据...")
        
        # 获取LOT_ID样本数据
        cursor.execute("""
            SELECT LOT_ID, DEVICE, STAGE, GOOD_QTY 
            FROM et_wait_lot 
            WHERE LOT_ID IS NOT NULL 
            LIMIT 5
        """)
        
        samples = cursor.fetchall()
        if samples:
            logger.info("📊 LOT_ID样本数据:")
            for sample in samples:
                logger.info(f"   - LOT_ID: '{sample[0]}', DEVICE: {sample[1]}, STAGE: {sample[2]}, QTY: {sample[3]}")
        
        # 检查LOT_ID长度分布
        cursor.execute("""
            SELECT 
                MIN(LENGTH(LOT_ID)) as min_length,
                MAX(LENGTH(LOT_ID)) as max_length,
                AVG(LENGTH(LOT_ID)) as avg_length,
                COUNT(*) as total_count
            FROM et_wait_lot 
            WHERE LOT_ID IS NOT NULL
        """)
        
        length_stats = cursor.fetchone()
        if length_stats:
            logger.info(f"📊 LOT_ID长度统计: 最小{length_stats[0]}, 最大{length_stats[1]}, 平均{length_stats[2]:.1f}, 总数{length_stats[3]}")
        
        return True
        
    except Error as e:
        logger.error(f"❌ 检查样本数据失败: {e}")
        return False
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚀 开始检查et_wait_lot表结构...")
    
    # 检查表结构
    suggested_field = check_et_wait_lot_structure()
    
    # 检查样本数据
    check_sample_data()
    
    if suggested_field:
        print(f"\n✅ 检查完成，建议的扩展表字段定义: {suggested_field}")
    else:
        print("\n❌ 无法确定字段类型") 